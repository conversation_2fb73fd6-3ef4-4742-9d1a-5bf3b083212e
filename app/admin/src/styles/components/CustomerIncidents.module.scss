@import '~@havenengineering/module-shared-owners-ui/dist/styles/variables.scss';
@import '@styles/mixin.scss';

.container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;

  p {
    margin: 0;
    color: $pebble-60;
  }

  @include sm-display {
    flex-wrap: nowrap;
  }
}

.emptyState {
  text-align: center;
  padding: 3rem 1rem;
  color: $pebble-60;

  p {
    margin: 0.5rem 0;
  }
}

.incidentsList {
  .mb15 {
    margin-bottom: 1rem;
  }
}

.tableContainer {
  overflow-x: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: white;
}

.incidentsTable {
  width: 100%;
  border-collapse: collapse;
  min-width: 800px;

  th,
  td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid $pebble-20;
  }

  th {
    background-color: $pebble-10;
    font-weight: 600;
    color: $pebble-80;
    position: sticky;
    top: 0;
    z-index: 1;
  }

  tr:hover {
    background-color: $pebble-5;
  }

  .reasonCell {
    max-width: 200px;
  }

  .reasonText {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: help;
  }

  .yes {
    color: $success-60;
    font-weight: 500;
  }

  .no {
    color: $pebble-60;
  }

  .actionsCell {
    min-width: 150px;
  }

  .actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }
}

// Modal styles
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid $pebble-20;

  h2 {
    margin: 0;
    color: $pebble-80;
    font-size: 1.25rem;
  }
}

.closeButton {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: $pebble-60;
  padding: 0;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;

  &:hover {
    background-color: $pebble-10;
    color: $pebble-80;
  }
}

.modalBody {
  padding: 2rem;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.formRow {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.checkboxRow {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.label {
  font-weight: 500;
  color: $pebble-80;
  margin-bottom: 0.25rem;
}

.modalActions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid $pebble-20;

  @include sm-display {
    flex-direction: row;
  }
}

.mb15 {
  margin-bottom: 1rem;
}

// Responsive adjustments
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .header {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }

  .modalBody {
    padding: 1.5rem;
  }

  .modalActions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .incidentsTable {
    font-size: 0.875rem;

    th,
    td {
      padding: 0.75rem 0.5rem;
    }

    .actions {
      flex-direction: column;
      gap: 0.25rem;
    }
  }
}
