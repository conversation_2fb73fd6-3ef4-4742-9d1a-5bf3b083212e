.account {
  margin: 16px 0px;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 16px;

  h2 {
    font-weight: bold;
    color: #000;
    line-height: 32px;

    & > span {
      color: #757575;
      margin-left: 8px;
      font-weight: normal;
      font-size: 16px;
    }
  }

  .statusRow {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .propertyBox {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #e0e0e0;
  }

  .property {
    font-weight: bold;
    color: #000;
    line-height: 32px;

    & > span {
      color: #757575;
      margin-left: 8px;
      font-weight: normal;
    }
  }
}
