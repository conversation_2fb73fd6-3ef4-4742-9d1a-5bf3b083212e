import { render } from '@testing-library/react';
import { AccountState } from '@models/profile';
import { HavenServiceAccountIdType } from '@models/profile';
import SeawareAccountData from '@components/Profile/CustomerAccounts/SeawareAccountData';

jest.mock('next/navigation');

describe('CustomerAccounts', () => {
  const hid = 'AB123456';
  const seawareAccount = {
    id: 12345,
    type: HavenServiceAccountIdType.SEAWARE_CLIENT_ID,
    state: AccountState.MATCHED,
  };

  it('should render the correct account headings and IDs', () => {
    const { getByText, getAllByText } = render(
      <SeawareAccountData account={seawareAccount} searchedHid={hid} />,
    );

    expect(getByText('Guest')).toBeInTheDocument();
    expect(getByText('( Seaware )')).toBeInTheDocument();
    expect(getByText('Client ID:')).toBeInTheDocument();
    expect(getByText(seawareAccount.id)).toBeInTheDocument();
    expect(getByText('Status:')).toBeInTheDocument();
    expect(getByText(AccountState.MATCHED)).toBeInTheDocument();
  });

  it('should render alert when account is in error state', () => {
    const { getByText } = render(
      <SeawareAccountData
        account={{ ...seawareAccount, state: AccountState.MISMATCH }}
        searchedHid={hid}
      />,
    );

    expect(
      getByText(
        `The Client ID registered in Seaware does not match the id linked to the customer identity.`,
      ),
    );
  });
});
