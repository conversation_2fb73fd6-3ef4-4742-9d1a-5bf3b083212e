import { render } from '@testing-library/react';
import { AccountState } from '@models/profile';
import { HavenServiceAccountIdType } from '@models/profile';
import PlotAccountData from '@components/Profile/CustomerAccounts/PlotAccountData';

jest.mock('next/navigation');

describe('PlotAccountData', () => {
  const hid = 'AB123456';
  const plotAccount = {
    id: 67890,
    type: HavenServiceAccountIdType.OWNER_ID,
    state: AccountState.MATCHED,
  };
  const ownerLegalData = {
    name: 'name',
    firstName: 'firstName',
    lastName: 'lastName',
    title: 'Mr.',
    address: {
      address1: '123 Test St',
      address2: 'Suite 100',
      address3: 'Test City',
      address4: 'Test State',
      postcode: '12345',
    },
  };

  it('should render the correct account headings and IDs', () => {
    const { getByText, getAllByText } = render(
      <PlotAccountData account={plotAccount} searchedHid={hid} ownerLegalData={ownerLegalData} />,
    );

    expect(getByText('Owner')).toBeInTheDocument();
    expect(getByText('( Plot )')).toBeInTheDocument();
    expect(getByText('Owner ID:')).toBeInTheDocument();
    expect(getByText(plotAccount.id)).toBeInTheDocument();
    expect(getByText('Status:')).toBeInTheDocument();
    expect(getByText(AccountState.MATCHED)).toBeInTheDocument();

    expect(getByText(ownerLegalData.name)).toBeInTheDocument();
    expect(getByText(ownerLegalData.title)).toBeInTheDocument();
    expect(getByText(ownerLegalData.firstName)).toBeInTheDocument();
    expect(getByText(ownerLegalData.lastName)).toBeInTheDocument();
    expect(getByText(ownerLegalData.address.address1)).toBeInTheDocument();
    expect(getByText(ownerLegalData.address.address2)).toBeInTheDocument();
    expect(getByText(ownerLegalData.address.address3)).toBeInTheDocument();
    expect(getByText(ownerLegalData.address.address4)).toBeInTheDocument();
    expect(getByText(ownerLegalData.address.postcode)).toBeInTheDocument();
  });

  it('should render alert when account is in error state', () => {
    const { getByText } = render(
      <PlotAccountData
        account={{ ...plotAccount, state: AccountState.MISMATCH }}
        searchedHid={hid}
        ownerLegalData={ownerLegalData}
      />,
    );

    expect(
      getByText(
        `The Owner ID registered in Plot does not match the id linked to the customer identity.`,
      ),
    );
  });
});
