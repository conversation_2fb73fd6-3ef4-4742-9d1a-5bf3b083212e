import { render, screen, fireEvent } from '@testing-library/react';
import { ManageCustomerIncidentsContent } from '@components/CustomerIncidents/ManageCustomerIncidentsContent';
import { useCustomerIncidents } from '@hooks/useCustomerIncidents';
import { renderWithProviders } from '../../testHelpers';

// Mock the hook
jest.mock('../../../hooks/useCustomerIncidents');

// Mock the child components
jest.mock('@components/CustomerIncidents/CustomerIncidentsList', () => ({
  CustomerIncidentsList: ({ onEdit }: { onEdit: (incident: any) => void }) => (
    <div data-testid="incidents-list">
      <button onClick={() => onEdit({ id: '1', bookingReferenceNumber: 'TEST123' })}>
        Edit Test Incident
      </button>
    </div>
  ),
}));

jest.mock('@components/CustomerIncidents/CustomerIncidentModal', () => ({
  CustomerIncidentModal: ({ onClose, incident }: { onClose: () => void; incident: any }) => (
    <div data-testid="incident-modal">
      <p>Modal for: {incident ? incident.bookingReferenceNumber : 'New Incident'}</p>
      <button onClick={onClose}>Close Modal</button>
    </div>
  ),
}));

const mockUseCustomerIncidents = useCustomerIncidents as jest.MockedFunction<typeof useCustomerIncidents>;

describe('ManageCustomerIncidentsContent', () => {
  beforeEach(() => {
    mockUseCustomerIncidents.mockReturnValue({
      data: { incidents: [], total: 0 },
      error: undefined,
      isLoading: false,
      mutate: jest.fn(),
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders the page title and create button', () => {
    renderWithProviders(<ManageCustomerIncidentsContent />, {
      userInfo: { token: 'test-token' },
    });

    expect(screen.getByText('Manage customer incident reports and bans')).toBeInTheDocument();
    expect(screen.getByTestId('create-incident-btn')).toBeInTheDocument();
    expect(screen.getByText('Create New Incident')).toBeInTheDocument();
  });

  it('renders the incidents list', () => {
    renderWithProviders(<ManageCustomerIncidentsContent />, {
      userInfo: { token: 'test-token' },
    });

    expect(screen.getByTestId('incidents-list')).toBeInTheDocument();
  });

  it('opens modal when create button is clicked', () => {
    renderWithProviders(<ManageCustomerIncidentsContent />, {
      userInfo: { token: 'test-token' },
    });

    const createButton = screen.getByTestId('create-incident-btn');
    fireEvent.click(createButton);

    expect(screen.getByTestId('incident-modal')).toBeInTheDocument();
    expect(screen.getByText('Modal for: New Incident')).toBeInTheDocument();
  });

  it('opens modal for editing when edit is triggered', () => {
    renderWithProviders(<ManageCustomerIncidentsContent />, {
      userInfo: { token: 'test-token' },
    });

    const editButton = screen.getByText('Edit Test Incident');
    fireEvent.click(editButton);

    expect(screen.getByTestId('incident-modal')).toBeInTheDocument();
    expect(screen.getByText('Modal for: TEST123')).toBeInTheDocument();
  });

  it('closes modal when close is triggered', () => {
    renderWithProviders(<ManageCustomerIncidentsContent />, {
      userInfo: { token: 'test-token' },
    });

    // Open modal
    const createButton = screen.getByTestId('create-incident-btn');
    fireEvent.click(createButton);

    expect(screen.getByTestId('incident-modal')).toBeInTheDocument();

    // Close modal
    const closeButton = screen.getByText('Close Modal');
    fireEvent.click(closeButton);

    expect(screen.queryByTestId('incident-modal')).not.toBeInTheDocument();
  });

  it('does not render modal initially', () => {
    renderWithProviders(<ManageCustomerIncidentsContent />, {
      userInfo: { token: 'test-token' },
    });

    expect(screen.queryByTestId('incident-modal')).not.toBeInTheDocument();
  });
});
