'use client';

import React from 'react';
import { Control, Controller, FieldErrors, FieldValues, RegisterOptions } from 'react-hook-form';
import TextAreaInput from '@havenengineering/module-shared-library/dist/form/components/TextAreaInput';

type ControlledTextAreaInputProps = {
  id: string;
  label: string;
  control: Control<any>;
  rules?: Omit<
    RegisterOptions<FieldValues, string>,
    'valueAsNumber' | 'valueAsDate' | 'setValueAs' | 'disabled'
  >;
  errors?: FieldErrors;
  className?: string;
  disabled?: boolean;
  rows?: number;
  placeholder?: string;
};

export const ControlledTextAreaInput = ({
  id,
  label,
  control,
  rules = {},
  errors,
  className,
  disabled = false,
  rows = 3,
  placeholder,
}: ControlledTextAreaInputProps) => {
  return (
    <Controller
      name={id}
      control={control}
      rules={rules}
      render={({ field }) => (
        <TextAreaInput
          id={id}
          label={label}
          {...field}
          error={errors?.[id]?.message?.toString()}
          className={className}
          disabled={disabled}
          rows={rows}
          placeholder={placeholder}
        />
      )}
    />
  );
};
