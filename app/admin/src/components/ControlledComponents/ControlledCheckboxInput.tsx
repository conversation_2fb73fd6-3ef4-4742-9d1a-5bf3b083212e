'use client';

import React from 'react';
import { Control, Controller, FieldErrors, FieldValues, RegisterOptions } from 'react-hook-form';
import CheckboxInput from '@havenengineering/module-shared-library/dist/form/components/CheckboxInput';

type ControlledCheckboxInputProps = {
  id: string;
  label: string;
  control: Control<any>;
  rules?: Omit<
    RegisterOptions<FieldValues, string>,
    'valueAsNumber' | 'valueAsDate' | 'setValueAs' | 'disabled'
  >;
  errors?: FieldErrors;
  className?: string;
  disabled?: boolean;
};

export const ControlledCheckboxInput = ({
  id,
  label,
  control,
  rules = {},
  errors,
  className,
  disabled = false,
}: ControlledCheckboxInputProps) => {
  return (
    <Controller
      name={id}
      control={control}
      rules={rules}
      render={({ field }) => (
        <CheckboxInput
          id={id}
          label={label}
          {...field}
          error={errors?.[id]?.message?.toString()}
          className={className}
          disabled={disabled}
        />
      )}
    />
  );
};
