'use client';

import React from 'react';
import { Control, Controller, FieldErrors, FieldValues, RegisterOptions } from 'react-hook-form';
import SelectInput from '@havenengineering/module-shared-library/dist/form/components/SelectInput';

type ControlledSelectInputProps = {
  id: string;
  label: string;
  control: Control<any>;
  options: { label: string; value: string }[];
  rules?: Omit<
    RegisterOptions<FieldValues, string>,
    'valueAsNumber' | 'valueAsDate' | 'setValueAs' | 'disabled'
  >;
  errors?: FieldErrors;
  className?: string;
  disabled?: boolean;
};

export const ControlledSelectInput = ({
  id,
  label,
  control,
  options,
  rules = {},
  errors,
  className,
  disabled = false,
}: ControlledSelectInputProps) => {
  return (
    <Controller
      name={id}
      control={control}
      rules={rules}
      render={({ field }) => (
        <SelectInput
          id={id}
          label={label}
          disabled={disabled}
          {...field}
          options={options}
          error={errors?.[id]?.message?.toString()}
          className={className}
        />
      )}
    />
  );
};
