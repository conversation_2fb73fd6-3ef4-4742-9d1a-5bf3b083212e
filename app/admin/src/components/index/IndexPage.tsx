'use client';

import { useContext } from 'react';
import Link from 'next/link';
import { UserInfoContext } from '@providers/UserInfoProvider';
import styles from '@styles/components/IndexPage.module.scss';

export const IndexPageContent = () => {
  const { roles } = useContext(UserInfoContext);

  return (
    <div className={styles.indexPage}>
      <h1>Identity Admin Dashboard</h1>
      <div className={styles.linkContainer}>
        <div className={styles.linkBlock}>
          <Link href="/profile" prefetch={false}>
            Search profile by email
          </Link>
        </div>
        {roles.includes('Identity.Admin') && (
          <div className={styles.linkBlock}>
            <Link href="/overrideAccountIds" prefetch={false}>
              Override account ids
            </Link>
          </div>
        )}
        <div className={styles.linkBlock}>
          <Link href="/manageCustomerIncidents" prefetch={false}>
            Manage Customer Incidents
          </Link>
        </div>
      </div>
    </div>
  );
};
