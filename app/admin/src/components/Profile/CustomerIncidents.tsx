'use client';

import { useContext } from 'react';
import OverrideAccountIdsForm from '@components/Forms/OverrideAccountIdsForm';
import CurrentAccountIds from '@components/Profile/OverrideAccountIds/CurrentIds';
import { PageHeading } from '@components/shared/PageHeading';
import InlineMessaging, {
  InlineMessagingTheme,
} from '@havenengineering/module-shared-library/dist/components/InlineMessaging/InlineMessaging';
import { LoadingSpinner } from '@havenengineering/module-shared-library/dist/components/LoadingSpinner/LoadingSpinner';
import { useProfile } from '@hooks/useProfile';
import { UserInfoContext } from '@providers/UserInfoProvider';
import styles from '@styles/components/profile/OverrideAccountIds.module.scss';

const CustomerIncidents = ({ hid }: { hid: string }) => {
  const { token } = useContext(UserInfoContext);
  const { data, error, isLoading, mutate } = useProfile(hid, token);

  if (isLoading) return <LoadingSpinner loading />;

  if (error) {
    <>
      <h1>Customer Profile Not Found</h1>
      <InlineMessaging
        message={`${error?.error}: Please try refreshing or `}
        link={{
          url: '/identity',
          description: 'searching again.',
        }}
        type={InlineMessagingTheme.ERROR_THEME}
        dismissible={false}
      />
    </>;
  }

  return (
    <>
      <PageHeading title="Override Account Ids"></PageHeading>
      {data?.identity && (
        <div className={styles.profileWrapper}>
          <div className={styles.sectionContainer}>
            <h2 className={styles.sectionHeading}>Account IDs</h2>
            <p>
              Use this form to override the account IDs for a customer using the input boxes below.
              When searching for linked accounts results will only be displayed where a linked
              account can be found.
            </p>
            <article>
              <CurrentAccountIds customerAccounts={data.identity.accounts} />
            </article>
            <article>
              <OverrideAccountIdsForm onUpdateCallback={mutate} customerProfile={data.identity} />
            </article>
          </div>
        </div>
      )}
    </>
  );
};

export default CustomerIncidents;
