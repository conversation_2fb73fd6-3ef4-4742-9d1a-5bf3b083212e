'use client';

import { useContext, useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { ControlledCheckboxInput } from '@components/ControlledComponents/ControlledCheckboxInput';
import { ControlledSelectInput } from '@components/ControlledComponents/ControlledSelectInput';
import { ControlledTextInput } from '@components/ControlledComponents/ControlledTextInput.';
import { Alert } from '@components/shared/Alert';
import { Button } from '@components/shared/Button';
import { errors } from '@helpers/errors';
import {
  addressLineValidator,
  cityValidator,
  COUNTRY_OPTIONS,
  countyValidator,
} from '@helpers/validator';
import { useProfile } from '@hooks/useProfile';
import { SyncedHavenIdentity } from '@models/profile';
import { UserInfoContext } from '@providers/UserInfoProvider';
import { updateProfile } from '@services/profile';
import styles from '@styles/components/profile/EditProfileDialogs.module.scss';

export type EditAddressFormValues = {
  postcode: string;
  line1: string;
  line2?: string;
  city: string;
  county?: string;
  countryCode: string;
  approveAddressChanges: boolean;
};

type EditAddressDialogProps = {
  customerProfile: SyncedHavenIdentity;
  handleClose: () => void;
  onUpdateCallback: () => void;
};

export const EditAddressDialog = ({
  customerProfile: { email, hid, accounts, migrated, address },
  handleClose,
  onUpdateCallback,
}: EditAddressDialogProps) => {
  const { token } = useContext(UserInfoContext);
  const { mutate } = useProfile(hid, token);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const initialState: EditAddressFormValues = {
    postcode: address?.postcode || '',
    line1: address?.line1 || '',
    line2: address?.line2 || '',
    city: address?.city || '',
    county: address?.county || '',
    countryCode: address?.country || 'GB',
    approveAddressChanges: false,
  };

  const {
    control,
    handleSubmit,
    formState: { errors: formStateErrors },
    setValue,
    reset,
  } = useForm({
    defaultValues: initialState,
    mode: 'onTouched',
  });

  const onSubmit: SubmitHandler<EditAddressFormValues> = async (formState) => {
    if (isLoading) return;

    setIsLoading(true);
    setError('');

    const updateResponse = await updateProfile(token, hid, {
      address: {
        postcode: formState.postcode,
        line1: formState.line1,
        line2: formState.line2,
        city: formState.city,
        county: formState.county,
        countryCode: formState.countryCode,
      },
      email: email,
      migrated,
      accounts,
    });

    if ('hid' in updateResponse && updateResponse.success) {
      await mutate();
      onUpdateCallback();
      setValue('approveAddressChanges', false);
      setIsLoading(false);
      handleClose();
      return;
    }

    if ('error' in updateResponse && !updateResponse.success) {
      setError(updateResponse.error);
      setIsLoading(false);
      return;
    }

    setError('Error updating customer profile.');
    setIsLoading(false);
  };

  const onClose = () => {
    reset(initialState);
    handleClose();
  };

  return (
    <form
      autoComplete="off"
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      onSubmit={handleSubmit(onSubmit)}
      className={styles.form}
      data-testid="address-update-form"
    >
      <h2>Update Address</h2>
      <ControlledSelectInput
        id="countryCode"
        label="Country"
        control={control}
        options={COUNTRY_OPTIONS}
        rules={{
          required: errors.requiredField.message,
        }}
        errors={formStateErrors}
      />
      <ControlledTextInput
        id="postcode"
        label="Postcode"
        control={control}
        rules={{
          required: errors.requiredField.message,
          /*           validate: (value, formValues) =>
            value && formValues.countryCode === 'GB' ? ukPostcodeValidator(value) : undefined, */
        }}
        errors={formStateErrors}
      />
      <ControlledTextInput
        id="line1"
        label="Address line 1"
        control={control}
        rules={{
          required: errors.requiredField.message,
          validate: addressLineValidator,
        }}
        errors={formStateErrors}
      />
      <ControlledTextInput id="line2" label="Address line 2" control={control} />
      <ControlledTextInput
        id="city"
        label="City / Town"
        control={control}
        rules={{
          required: errors.requiredField.message,
          validate: cityValidator,
        }}
        errors={formStateErrors}
      />
      <ControlledTextInput
        id="county"
        label="County / State"
        control={control}
        rules={{
          validate: (value: string) => (value ? countyValidator(value) : undefined),
        }}
        errors={formStateErrors}
      />

      <ControlledCheckboxInput
        label="Approve profile changes"
        id="approveAddressChanges"
        control={control}
        rules={{
          required: errors.approveRequired.message,
        }}
        errors={formStateErrors}
      />

      <div className={styles.buttonGroup}>
        <Button
          size="medium"
          id="cancel-address-update"
          data-testid="cancel-address-update"
          type="button"
          variant="outlined"
          disabled={isLoading}
          onClick={() => onClose()}
        >
          Cancel
        </Button>
        <Button
          size="medium"
          id="submit-address-update"
          data-testid="submit-address-update"
          type="submit"
          variant="contained"
          isLoading={isLoading}
        >
          Update Address
        </Button>
      </div>

      {error && <Alert title="Update Failed." text={error} severity="error" />}
    </form>
  );
};

export default EditAddressDialog;
