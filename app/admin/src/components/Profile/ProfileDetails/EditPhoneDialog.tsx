'use client';

import { useContext, useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { ControlledCheckboxInput } from '@components/ControlledComponents/ControlledCheckboxInput';
import { ControlledTextInput } from '@components/ControlledComponents/ControlledTextInput.';
import { Alert } from '@components/shared/Alert';
import { Button } from '@components/shared/Button';
import { errors } from '@helpers/errors';
import { phoneNumberValidator } from '@helpers/validator';
import { useProfile } from '@hooks/useProfile';
import { SyncedHavenIdentity } from '@models/profile';
import { UserInfoContext } from '@providers/UserInfoProvider';
import { updateProfile } from '@services/profile';
import styles from '@styles/components/profile/EditProfileDialogs.module.scss';

export type EditPhoneFormValues = {
  phoneNumber: string;
  approvePhoneChanges: boolean;
};

type EditPhoneDialogProps = {
  customerProfile: SyncedHavenIdentity;
  handleClose: () => void;
  onUpdateCallback: () => void;
};

export const EditPhoneDialog = ({
  customerProfile: { email, hid, accounts, migrated, phoneNumber },
  handleClose,
  onUpdateCallback,
}: EditPhoneDialogProps) => {
  const { token } = useContext(UserInfoContext);
  const { mutate } = useProfile(hid, token);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const initialState: EditPhoneFormValues = {
    phoneNumber: phoneNumber || '',
    approvePhoneChanges: false,
  };

  const {
    control,
    handleSubmit,
    formState: { errors: formStateErrors },
    setValue,
    reset,
  } = useForm({
    defaultValues: initialState,
    mode: 'onTouched',
  });

  const onSubmit: SubmitHandler<EditPhoneFormValues> = async (formState) => {
    if (isLoading) return;

    setIsLoading(true);
    setError('');

    const updateResponse = await updateProfile(token, hid, {
      ...(formState.phoneNumber && formState.phoneNumber !== initialState.phoneNumber
        ? { phoneNumber: formState.phoneNumber }
        : {}),
      email: email,
      migrated,
      accounts,
    });

    if ('hid' in updateResponse && updateResponse.success) {
      await mutate();
      onUpdateCallback();
      setValue('approvePhoneChanges', false);
      setIsLoading(false);
      handleClose();
      return;
    }

    if ('error' in updateResponse && !updateResponse.success) {
      setError(updateResponse.error);
      setIsLoading(false);
      return;
    }

    setError('Error updating customer profile.');
    setIsLoading(false);
  };

  const onClose = () => {
    reset(initialState);
    handleClose();
  };

  return (
    <form
      autoComplete="off"
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      onSubmit={handleSubmit(onSubmit)}
      className={styles.form}
      data-testid="phone-update-form"
    >
      <h2>Update Phone</h2>
      <ControlledTextInput
        id="phoneNumber"
        label="Phone number"
        control={control}
        rules={{
          validate: (value: string) => (value ? phoneNumberValidator(value) : undefined),
        }}
        errors={formStateErrors}
      />

      <ControlledCheckboxInput
        label="Approve profile changes"
        id="approvePhoneChanges"
        control={control}
        rules={{
          required: errors.approveRequired.message,
        }}
        errors={formStateErrors}
      />

      <div className={styles.buttonGroup}>
        <Button
          size="medium"
          id="cancel-phone-update"
          data-testid="cancel-phone-update"
          type="button"
          variant="outlined"
          disabled={isLoading}
          onClick={() => onClose()}
        >
          Cancel
        </Button>
        <Button
          size="medium"
          id="submit-phone-update"
          data-testid="submit-phone-update"
          type="submit"
          variant="contained"
          isLoading={isLoading}
        >
          Update Phone
        </Button>
      </div>

      {error && <Alert title="Update Failed." text={error} severity="error" />}
    </form>
  );
};

export default EditPhoneDialog;
