'use client';

import { useContext, useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { ControlledCheckboxInput } from '@components/ControlledComponents/ControlledCheckboxInput';
import { ControlledTextInput } from '@components/ControlledComponents/ControlledTextInput.';
import { Alert } from '@components/shared/Alert';
import { Button } from '@components/shared/Button';
import { errors } from '@helpers/errors';
import { emailValidator } from '@helpers/validator';
import { useProfile } from '@hooks/useProfile';
import { SyncedHavenIdentity } from '@models/profile';
import { UserInfoContext } from '@providers/UserInfoProvider';
import { updateProfile } from '@services/profile';
import styles from '@styles/components/profile/EditProfileDialogs.module.scss';

export type EditEmailFormValues = {
  email: string;
  approveEmailChanges: boolean;
};

type EditEmailDialogProps = {
  customerProfile: SyncedHavenIdentity;
  handleClose: () => void;
  onUpdateCallback: () => void;
};

export const EditEmailDialog = ({
  customerProfile: { hid, accounts, migrated, email },
  handleClose,
  onUpdateCallback,
}: EditEmailDialogProps) => {
  const { token } = useContext(UserInfoContext);
  const { mutate } = useProfile(hid, token);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const initialState: EditEmailFormValues = {
    email,
    approveEmailChanges: false,
  };

  const {
    control,
    handleSubmit,
    formState: { errors: formStateErrors },
    setValue,
    reset,
  } = useForm({
    defaultValues: initialState,
    mode: 'onTouched',
  });

  const onSubmit: SubmitHandler<EditEmailFormValues> = async (formState) => {
    if (isLoading) return;

    setIsLoading(true);
    setError('');

    const updateResponse = await updateProfile(token, hid, {
      email: formState.email,
      migrated,
      accounts,
    });

    if ('hid' in updateResponse && updateResponse.success) {
      await mutate();
      onUpdateCallback();
      setValue('approveEmailChanges', false);
      setIsLoading(false);
      handleClose();
      return;
    }

    if ('error' in updateResponse && !updateResponse.success) {
      setError(updateResponse.error);
      setIsLoading(false);
      return;
    }

    setError('Error updating customer profile.');
    setIsLoading(false);
  };

  const onClose = () => {
    reset(initialState);
    handleClose();
  };

  return (
    <form
      autoComplete="off"
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      onSubmit={handleSubmit(onSubmit)}
      className={styles.form}
      data-testid="email-update-form"
    >
      <h2>Update Email</h2>
      <ControlledTextInput
        id="email"
        label="Email address"
        type="email"
        control={control}
        rules={{
          required: errors.requiredField.message,
          validate: emailValidator,
        }}
        errors={formStateErrors}
      />

      <ControlledCheckboxInput
        label="Approve profile changes"
        id="approveEmailChanges"
        control={control}
        rules={{
          required: errors.approveRequired.message,
        }}
        errors={formStateErrors}
      />

      <div className={styles.buttonGroup}>
        <Button
          size="medium"
          id="cancel-email-update"
          data-testid="cancel-email-update"
          type="button"
          variant="outlined"
          disabled={isLoading}
          onClick={() => onClose()}
        >
          Cancel
        </Button>
        <Button
          size="medium"
          id="submit-email-update"
          data-testid="submit-email-update"
          type="submit"
          variant="contained"
          isLoading={isLoading}
        >
          Update Email
        </Button>
      </div>

      {error && <Alert title="Update Failed." text={error} severity="error" />}
    </form>
  );
};

export default EditEmailDialog;
