'use client';

import { useContext, useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { ControlledCheckboxInput } from '@components/ControlledComponents/ControlledCheckboxInput';
import { ControlledSelectInput } from '@components/ControlledComponents/ControlledSelectInput';
import { ControlledTextInput } from '@components/ControlledComponents/ControlledTextInput.';
import { Alert } from '@components/shared/Alert';
import { Button } from '@components/shared/Button';
import { errors } from '@helpers/errors';
import { nameValidator, TITLE_OPTIONS, titleValidator } from '@helpers/validator';
import { useProfile } from '@hooks/useProfile';
import { SyncedHavenIdentity } from '@models/profile';
import { UserInfoContext } from '@providers/UserInfoProvider';
import { updateProfile } from '@services/profile';
import styles from '@styles/components/profile/EditProfileDialogs.module.scss';

export type EditNameFormValues = {
  title: string | null;
  firstName: string;
  lastName: string;
  approveNameChanges: boolean;
};

type EditNameDialogProps = {
  customerProfile: SyncedHavenIdentity;
  handleClose: () => void;
  onUpdateCallback: () => void;
};

export const EditNameDialog = ({
  customerProfile: { email, hid, accounts, migrated, title, firstName, lastName },
  handleClose,
  onUpdateCallback,
}: EditNameDialogProps) => {
  const { token } = useContext(UserInfoContext);
  const { mutate } = useProfile(hid, token);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const initialState: EditNameFormValues = {
    title: title || '',
    firstName: firstName || '',
    lastName: lastName || '',
    approveNameChanges: false,
  };

  const {
    control,
    handleSubmit,
    formState: { errors: formStateErrors },
    setValue,
    reset,
  } = useForm({
    defaultValues: initialState,
    mode: 'onTouched',
  });

  const onSubmit: SubmitHandler<EditNameFormValues> = async (formState) => {
    if (isLoading) return;

    setIsLoading(true);
    setError('');

    const updateResponse = await updateProfile(token, hid, {
      ...(formState.title && formState.title !== initialState.title
        ? { title: formState.title }
        : {}),
      ...(formState.firstName && formState.firstName !== initialState.firstName
        ? { firstName: formState.firstName }
        : {}),
      ...(formState.lastName && formState.lastName !== initialState.lastName
        ? { lastName: formState.lastName }
        : {}),
      email: email,
      migrated,
      accounts,
    });

    if ('hid' in updateResponse && updateResponse.success) {
      await mutate();
      onUpdateCallback();
      setValue('approveNameChanges', false);
      setIsLoading(false);
      handleClose();
      return;
    }

    if ('error' in updateResponse && !updateResponse.success) {
      setError(updateResponse.error);
      setIsLoading(false);
      return;
    }

    setError('Error updating customer profile.');
    setIsLoading(false);
  };

  const onClose = () => {
    reset(initialState);
    handleClose();
  };

  return (
    <form
      autoComplete="off"
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      onSubmit={handleSubmit(onSubmit)}
      className={styles.form}
      data-testid="name-update-form"
    >
      <h2>Update Name</h2>

      <ControlledSelectInput
        data-testid="title"
        id="title"
        label="Title"
        control={control}
        options={TITLE_OPTIONS}
        rules={{
          required: errors.requiredField.message,
          validate: titleValidator,
        }}
        errors={formStateErrors}
      />
      <ControlledTextInput
        id="firstName"
        label="First name"
        control={control}
        rules={{
          required: errors.requiredField.message,
          validate: nameValidator,
        }}
        errors={formStateErrors}
      />
      <ControlledTextInput
        id="lastName"
        label="Last name"
        control={control}
        rules={{
          required: errors.requiredField.message,
          validate: nameValidator,
        }}
        errors={formStateErrors}
      />

      <ControlledCheckboxInput
        label="Approve profile changes"
        id="approveNameChanges"
        control={control}
        rules={{
          required: errors.approveRequired.message,
        }}
        errors={formStateErrors}
      />

      <div className={styles.buttonGroup}>
        <Button
          size="medium"
          id="cancel-name-update"
          data-testid="cancel-name-update"
          type="button"
          variant="outlined"
          disabled={isLoading}
          onClick={() => onClose()}
        >
          Cancel
        </Button>
        <Button
          size="medium"
          id="submit-name-update"
          data-testid="submit-name-update"
          type="submit"
          variant="contained"
          isLoading={isLoading}
        >
          Update Name
        </Button>
      </div>

      {error && <Alert title="Update Failed." text={error} severity="error" />}
    </form>
  );
};

export default EditNameDialog;
