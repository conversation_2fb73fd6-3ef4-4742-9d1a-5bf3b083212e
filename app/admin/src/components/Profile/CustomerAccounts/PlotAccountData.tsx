'use client';

import { Alert } from '@components/shared/Alert';
import { getAlertProps, isErrorState } from '@helpers/accounts';
import { OwnerLegalData, ValidatedHavenServiceAccount } from '@models/profile';
import styles from '@styles/components/profile/AccountData.module.scss';

import { ErrorStateControls } from './ErrorStateControls';

interface PlotAccountDataProps {
  account: ValidatedHavenServiceAccount | undefined;
  ownerLegalData: OwnerLegalData | undefined;
  searchedHid: string;
}

const PlotAccountData = ({ account, ownerLegalData, searchedHid }: PlotAccountDataProps) => {
  return (
    <div className={styles.account}>
      <h2>
        Owner <span>( Plot )</span>
      </h2>
      {account ? (
        <>
          {isErrorState(account.state) && <Alert {...getAlertProps(account.state, 'Plot')} />}

          <p className={styles.property}>
            Owner ID:
            <span>{account.id}</span>
          </p>
          <div className={styles.statusRow}>
            <p className={styles.property}>
              Status:
              <span>{account.state}</span>
            </p>
            <ErrorStateControls account={account} hid={searchedHid} />
          </div>
          {ownerLegalData && (
            <p className={styles.property}>
              Owner&apos;s legal data from plot<span>| Not editable from this app</span>
              <br />
              Name: <span className="data-amp-mask">{ownerLegalData.name}</span>
              <br />
              Title: <span className="data-amp-mask">{ownerLegalData.title}</span>
              <br />
              First Name: <span className="data-amp-mask">{ownerLegalData.firstName}</span>
              <br />
              Last Name: <span className="data-amp-mask">{ownerLegalData.lastName}</span>
              <br />
              address1:
              <span className="data-amp-mask">{ownerLegalData.address.address1}</span>
              <br />
              address2:
              <span className="data-amp-mask">{ownerLegalData.address.address2}</span>
              <br />
              address3: <span className="data-amp-mask">{ownerLegalData.address.address3}</span>
              <br />
              address4: <span className="data-amp-mask">{ownerLegalData.address.address4}</span>
              <br />
              Postcode: <span className="data-amp-mask">{ownerLegalData.address.postcode}</span>
            </p>
          )}
        </>
      ) : (
        <p>No plot account data available.</p>
      )}
    </div>
  );
};

export default PlotAccountData;
