'use client';

import { Alert } from '@components/shared/Alert';
import { getAlertProps, isErrorState } from '@helpers/accounts';
import { ValidatedHavenServiceAccount } from '@models/profile';
import styles from '@styles/components/profile/AccountData.module.scss';

import { ErrorStateControls } from './ErrorStateControls';

interface SeawareAccountDataProps {
  account: ValidatedHavenServiceAccount | undefined;
  searchedHid: string;
}

const SeawareAccountData = ({ account, searchedHid }: SeawareAccountDataProps) => {
  return (
    <div className={styles.account}>
      <h2>
        Guest <span>( Seaware )</span>
      </h2>
      {account ? (
        <>
          {isErrorState(account?.state) && <Alert {...getAlertProps(account.state, 'Seaware')} />}

          <p className={styles.property}>
            Client ID:
            <span>{account.id}</span>
          </p>
          <div className={styles.statusRow}>
            <p className={styles.property}>
              Status:
              <span>{account.state}</span>
            </p>
            <ErrorStateControls account={account} hid={searchedHid} />
          </div>
        </>
      ) : (
        <p>No Seaware account data available.</p>
      )}
    </div>
  );
};

export default SeawareAccountData;
