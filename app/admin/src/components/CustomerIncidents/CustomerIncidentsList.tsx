'use client';

import React, { useState } from 'react';
import { Alert } from '@components/shared/Alert';
import { LoadingIndicator } from '@components/shared/LoadingIndicator';
import Button, {
  Size,
  Theme,
} from '@havenengineering/module-shared-library/dist/components/Button/Button';
import InlineMessaging, {
  InlineMessagingTheme,
} from '@havenengineering/module-shared-library/dist/components/InlineMessaging/InlineMessaging';
import { UserIncident } from './ManageCustomerIncidentsContent';
import { useCustomerIncidents } from '@hooks/useCustomerIncidents';
import { getHeaders } from '@helpers/utils';
import styles from '@styles/components/CustomerIncidents.module.scss';

type CustomerIncidentsListProps = {
  token: string;
  onEdit: (incident: UserIncident) => void;
  refreshTrigger: number;
};

export const CustomerIncidentsList = ({
  token,
  onEdit,
  refreshTrigger,
}: CustomerIncidentsListProps) => {
  const { data, error, isLoading, mutate } = useCustomerIncidents(token);
  const [deleteError, setDeleteError] = useState<string>('');
  const [deletingId, setDeletingId] = useState<string | null>(null);

  const incidents = data?.incidents || [];

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this incident? This action cannot be undone.')) {
      return;
    }

    try {
      setDeletingId(id);
      setDeleteError('');

      const response = await fetch(`/identity/api/customer-incidents/${id}`, {
        method: 'DELETE',
        headers: getHeaders(token),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete incident');
      }

      // Refresh the incidents list
      await mutate();
    } catch (err) {
      setDeleteError((err as Error).message);
    } finally {
      setDeletingId(null);
    }
  };

  // Refresh data when refreshTrigger changes
  React.useEffect(() => {
    if (refreshTrigger > 0) {
      mutate();
    }
  }, [refreshTrigger, mutate]);

  if (isLoading) {
    return (
      <div data-testid="incidents-loading">
        <LoadingIndicator text="Loading customer incidents..." />
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        severity="error"
        title="Error Loading Incidents"
        text={error.message}
        body={
          <Button theme={Theme.SECONDARY_THEME} scale={Size.SMALL_SIZE} onClick={() => mutate()}>
            Retry
          </Button>
        }
      />
    );
  }

  if (!incidents.length) {
    return (
      <div className={styles.emptyState}>
        <p>No customer incidents found.</p>
        <p>Click "Create New Incident" to add the first incident report.</p>
      </div>
    );
  }

  return (
    <div className={styles.incidentsList}>
      {deleteError && (
        <InlineMessaging
          type={InlineMessagingTheme.ERROR_THEME}
          message={deleteError}
          dismissible={true}
          onDismiss={() => setDeleteError('')}
          className={styles.mb15}
        />
      )}

      <div className={styles.tableContainer}>
        <table className={styles.incidentsTable}>
          <thead>
            <tr>
              <th>Booking Reference</th>
              <th>Lead Guest Name</th>
              <th>Reason for Ban</th>
              <th>After Departure</th>
              <th>Police Involved</th>
              <th>Created By</th>
              <th>Created Date</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {incidents.map((incident) => (
              <tr key={incident.id}>
                <td>{incident.bookingReferenceNumber}</td>
                <td>{incident.leadGuestName}</td>
                <td className={styles.reasonCell}>
                  <div className={styles.reasonText} title={incident.reasonForBan}>
                    {incident.reasonForBan}
                  </div>
                </td>
                <td>
                  <span className={incident.isDiscoveredAfterDeparture ? styles.yes : styles.no}>
                    {incident.isDiscoveredAfterDeparture ? 'Yes' : 'No'}
                  </span>
                </td>
                <td>
                  <span className={incident.isPoliceInvolved ? styles.yes : styles.no}>
                    {incident.isPoliceInvolved ? 'Yes' : 'No'}
                  </span>
                </td>
                <td>{incident.createdBy}</td>
                <td>
                  {incident.createdAt ? new Date(incident.createdAt).toLocaleDateString() : '-'}
                </td>
                <td className={styles.actionsCell}>
                  <div className={styles.actions}>
                    <Button
                      theme={Theme.SECONDARY_THEME}
                      scale={Size.SMALL_SIZE}
                      onClick={() => onEdit(incident)}
                      data-testid={`edit-incident-${incident.id}`}
                    >
                      Edit
                    </Button>
                    <Button
                      theme={Theme.DANGER_THEME}
                      scale={Size.SMALL_SIZE}
                      onClick={() => incident.id && handleDelete(incident.id)}
                      loading={deletingId === incident.id}
                      disabled={deletingId === incident.id}
                      data-testid={`delete-incident-${incident.id}`}
                    >
                      Delete
                    </Button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};
