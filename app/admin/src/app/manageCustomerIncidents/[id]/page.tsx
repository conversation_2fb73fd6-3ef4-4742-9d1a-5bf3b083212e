import type { Metadata } from 'next';
import OverrideAccountIds from '@components/Profile/OverrideAccountIds';

export const dynamic = 'force-dynamic';
export const metadata: Metadata = {
  title: 'Identity Admin',
};

const ManageCustomerIncidentsPage = async ({ params }: { params: Promise<{ hid: string }> }) => {
  const hid = (await params).hid;

  return (
    <main className="container" data-testid="fix-account-ids-page">
      <OverrideAccountIds hid={hid} />
    </main>
  );
};

export default ManageCustomerIncidentsPage;
