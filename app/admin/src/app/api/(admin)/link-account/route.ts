import { hidService } from '@services/hidService';

export async function POST(request: Request) {
  const adminUserUpn = request.headers.get('x-admin-upn') as string;
  const body = await request.json();

  const result = await hidService.linkAccount(
    body.hid,
    body.account,
    body.account.migrated,
    adminUserUpn,
  );
  if ('statusCode' in result) throw new Error(result.message);

  return Response.json({ success: true });
}
