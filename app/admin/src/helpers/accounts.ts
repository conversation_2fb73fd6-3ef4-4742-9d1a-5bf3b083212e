import { AccountState } from '@models/profile';

type AlertMessageProps = { title: string; text: string; severity: 'error' | 'warning' };
type AccountErrorState = Exclude<AccountState, AccountState.MATCHED>;

export const getAlertProps = (
  errorType: AccountErrorState,
  accountType: 'Seaware' | 'Plot',
): AlertMessageProps => {
  const idType = accountType === 'Seaware' ? 'Client' : 'Owner';

  const errorStateMap: Record<AccountErrorState, AlertMessageProps> = {
    MISMATCH: {
      title: `The ${idType} ID registered in ${accountType} does not match the id linked to the customer identity.`,
      text: 'Use the override account ids button or contact <PERSON>gi Ops to set the correct id.',
      severity: 'warning',
    },
    OWNED_BY_OTHER_IDENTITY: {
      title: `This ${idType} ID is linked to another registered customer identity.`,
      text: `You can view the other identity, Use the override account ids button or contact Digi Ops override the id.`,
      severity: 'warning',
    },
    UNREGISTERED: {
      title: `An ${idType} account could not be found in ${accountType}, but an account is linked to the customer identity.`,
      text: `Please try updating the profile, or Contact Digi Ops to resolve this issue`,
      severity: 'warning',
    },
    UNLINKED_IN_IDENTITY: {
      title: `This profile has not been migrated to Custmer Identity.`,
      text: `Please use the "Link Account" process or update the profile to link automatically.`,
      severity: 'warning',
    },
    ERROR: {
      title: `An Error occurred while looking for customer ${idType} account.`,
      text: `Please try refreshing the page.`,
      severity: 'error',
    },
    EMAIL_CHANGED: {
      title: `The Email in ${accountType} does not match the email linked to the customer identity.`,
      text: `Use update profile to set the correct email.`,
      severity: 'warning',
    },
  };

  return errorStateMap[errorType];
};

export const isErrorState = (state: AccountState): state is AccountErrorState =>
  [
    AccountState.EMAIL_CHANGED,
    AccountState.ERROR,
    AccountState.MISMATCH,
    AccountState.OWNED_BY_OTHER_IDENTITY,
    AccountState.UNLINKED_IN_IDENTITY,
  ].includes(state);
