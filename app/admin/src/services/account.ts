import { getHeaders } from '@helpers/utils';
import { ValidatedHavenServiceAccount } from '@models/profile';
import { SearchByAccountsResultWithSyncStatus } from '@models/search';

export const unlinkAccount = async (
  token: string,
  account: SearchByAccountsResultWithSyncStatus,
) => {
  const res = await fetch(`/identity/api/unlink-account`, {
    method: 'POST',
    headers: getHeaders(token),
    body: JSON.stringify({ account: { ...account, state: account.state } }),
  });

  if (!res.ok) {
    throw new Error(
      `Error unlinking profile ${account.type} ${account.id} from ${account.email} ${account.hid}`,
    );
  }
};

export const linkAccount = async (
  token: string,
  account: ValidatedHavenServiceAccount,
  hid: string,
): Promise<void> => {
  const res = await fetch(`/identity/api/link-account`, {
    method: 'POST',
    headers: getHeaders(token),
    body: JSON.stringify({ account, hid }),
  });

  if (!res.ok) {
    throw new Error(`Error linking profile ${account.id} to ${account.hid}`);
  }
};
