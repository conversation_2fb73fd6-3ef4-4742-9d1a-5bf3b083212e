// @ts-ignore
import request from 'superagent';
import { afterAll, beforeAll, beforeEach, describe, expect, it, vi } from 'vitest';
import { Server } from '../../server.js';
import Provider, { PromptDetail } from 'oidc-provider';
import { getSessionByUid } from '../../../src/openid-connect/handler/current-session.js';
import { completeLoginInteraction } from '../../../src/utils/interaction.js';
import { HavenAccountProvider } from '../../../src/openid-connect/account-provider/account-provider.js';
import { AuthenticationResult } from '../../../src/openid-connect/account-provider/types.js';
import { createSession, emptyAuths, validHid, validIat, validSession } from './request-helper.js';
import { logger } from '@havenengineering/module-haven-logging';

const mockUnleash = { isEnabled: vi.fn().mockReturnValue(false) };
vi.mock('../../../src/services/unleash.js', async () => ({
  ...(await vi.importActual<typeof import('../../../src/services/unleash.js')>(
    '../../../src/services/unleash.js',
  )),
  createFeatureFlagChecker: vi.fn(() => mockUnleash),
}));

beforeEach(() => {
  vi.resetAllMocks();
  vi.restoreAllMocks();
});

const makeCompliantRequest = async (
  url: string,
  clientId: string = 'owners',
  cookies: string[] = [],
) => {
  return request
    .get(url)
    .set('Cookie', [`client-id=${clientId}`, ...cookies])
    .set('x-app-id', 'service-haven-identity')
    .set('x-api-key', process.env.IDENTITY_SSO_API_KEY as string)
    .set('user-agent', 'haven-internal-tests')
    .ok((_) => true);
};

describe('auto-login server tests', async () => {
  const server = new Server();
  const autoURL = `${server.baseUrl}/internal/interaction/autologin`;

  beforeAll(async () => {
    await server.start();
  });

  afterAll(async () => {
    await server.stop();
  });

  describe('server failure cases', async () => {
    it('returns 403 if x-api headers are missing or invalid', async () => {
      const { status, body } = await request
        .get(autoURL)
        .set('Cookie', ['client-id=owners'])
        .set('user-agent', 'haven-internal-tests')
        .ok((_) => true);

      expect(status).toEqual(403);
      expect(body.error).toEqual({
        message: 'missing x-app-id header',
      });
      expect(body.userLoggedIn).toBe(false);
      expect(body.clientContext).toBeDefined();
      expect(body.clientContext.id).toEqual('owners');
    });

    it('returns 400 if request is valid but no session is found', async () => {
      const { status, body } = await makeCompliantRequest(autoURL);

      expect(status).toEqual(400);
      expect(body.error).toEqual({
        message: 'invalid_request',
      });
      expect(body.userLoggedIn).toBe(false);
      expect(body.askUserToReAuthenticate).toBe(true);
      expect(body.clientContext).toBeDefined();
      expect(body.clientContext.id).toEqual('owners');
    });

    it('returns 403 if session has no auths', async () => {
      vi.spyOn(Provider.prototype, 'interactionDetails').mockResolvedValue(
        // @ts-ignore
        {
          uid: '1234',
          prompt: {
            name: 'login',
          } as PromptDetail,
          params: {
            client_id: 'owners',
          },
        },
      );
      vi.mock('../../../src/openid-connect/handler/current-session.js');
      // @ts-ignore
      vi.mocked(getSessionByUid).mockReturnValue(createSession(validIat, validHid, emptyAuths));

      const { status, body } = await makeCompliantRequest(autoURL);

      expect(status).toEqual(403);
      expect(body.userLoggedIn).toBe(false);
      expect(body.askUserToReAuthenticate).toBe(true);
      expect(body.clientContext).toBeDefined();
      expect(body.clientContext.id).toEqual('owners');
    });

    it('returns 500 for consent as its not implemented', async () => {
      vi.spyOn(Provider.prototype, 'interactionDetails').mockResolvedValueOnce(
        // @ts-ignore
        {
          uid: '1234',
          prompt: {
            name: 'consent',
          } as PromptDetail,
          params: {},
        },
      );

      const { status, body } = await makeCompliantRequest(autoURL);

      expect(status).toEqual(500);
      expect(body.userLoggedIn).toBe(false);
      expect(body.askUserToReAuthenticate).toBe(true);
      expect(body.clientContext).toBeDefined();
      expect(body.clientContext.id).toEqual('owners');
    });

    // TODO error for unknown prompt?

    it('returns 200 and askUserToReAuthenticate for session with invalid account', async () => {
      vi.spyOn(Provider.prototype, 'interactionDetails').mockResolvedValueOnce(
        // @ts-ignore
        {
          uid: '1234',
          prompt: {
            name: 'Haven',
          } as PromptDetail,
          params: {},
        },
      );

      vi.mock('../../../src/openid-connect/handler/current-session.js');
      // @ts-ignore
      vi.mocked(getSessionByUid).mockReturnValue(validSession);

      // @ts-ignore
      vi.spyOn(HavenAccountProvider.prototype, 'authenticateWithSession').mockResolvedValue(
        undefined,
      );

      const { status, body } = await makeCompliantRequest(autoURL);

      expect(status).toEqual(200);
      expect(body.askUserToReAuthenticate).toBe(true);
    });

    it('returns 200 and askUserToReAuthenticate when identity-skipautologin cookie is set', async () => {
      const errorSpy = vi.spyOn(logger, 'error').mockImplementation(() => {
        /* do nothing in this test */
      });
      const warningSpy = vi.spyOn(logger, 'warn').mockImplementation(() => {
        console.log(`got a warning`); /* do nothing in this test */
      });
      const infoSpy = vi.spyOn(logger, 'info').mockImplementation(() => {
        /* do nothing in this test */
      });

      vi.spyOn(Provider.prototype, 'interactionDetails').mockResolvedValueOnce(
        // @ts-ignore
        {
          uid: '1234',
          prompt: {
            name: 'Haven',
          } as PromptDetail,
          params: {},
        },
      );

      vi.mock('../../../src/openid-connect/handler/current-session.js');
      // @ts-ignore
      vi.mocked(getSessionByUid).mockReturnValue(validSession);

      // @ts-ignore
      vi.spyOn(HavenAccountProvider.prototype, 'authenticateWithSession').mockReturnValue({
        accountId: '1234',
        hid: '1234',
      } as AuthenticationResult);

      vi.mock('../../../src/utils/interaction.js');
      // @ts-ignore
      vi.mocked(completeLoginInteraction).mockResolvedValue({
        redirectTo: '/somewhere',
        hid: '1234',
      });

      const { status, body } = await makeCompliantRequest(autoURL, 'owners', [
        `identity-skipautologin=true`,
      ]);

      expect(status).toEqual(200);
      expect(body.askUserToReAuthenticate).toBe(true);
    });
  });

  describe('server success cases', async () => {
    it('returns 200 for login regardless of session', async () => {
      vi.spyOn(Provider.prototype, 'interactionDetails').mockResolvedValueOnce(
        // @ts-ignore
        {
          uid: '1234',
          prompt: {
            name: 'login',
          } as PromptDetail,
          params: {},
        },
      );

      const { status, body } = await makeCompliantRequest(autoURL);

      expect(status).toEqual(200);
      expect(body).toEqual({
        clientContext: {
          name: 'Haven',
          appleSignInEnabled: false,
          googleSignInEnabled: false,
          microsoftSignInEnabled: false,
        },
        uid: '1234',
        upgrade: false,
        askUserToReAuthenticate: false,
        userLoggedIn: false,
      });
    });

    it('returns 200 for register regardless of session', async () => {
      vi.spyOn(Provider.prototype, 'interactionDetails').mockResolvedValueOnce(
        // @ts-ignore
        {
          uid: '1234',
          prompt: {
            name: 'register',
          } as PromptDetail,
          params: {},
        },
      );

      const { status, body } = await makeCompliantRequest(autoURL);

      expect(status).toEqual(200);
      expect(body).toEqual({
        clientContext: {
          name: 'Haven',
          appleSignInEnabled: false,
          googleSignInEnabled: false,
          microsoftSignInEnabled: false,
        },
        uid: '1234',
        upgrade: false,
        askUserToReAuthenticate: false,
        userLoggedIn: false,
      });
    });

    it('returns 200 and redirect for session with valid account', async () => {
      const errorSpy = vi.spyOn(logger, 'error').mockImplementation(() => {
        /* do nothing in this test */
      });
      const warningSpy = vi.spyOn(logger, 'warn').mockImplementation(() => {
        console.log(`got a warning`); /* do nothing in this test */
      });
      const infoSpy = vi.spyOn(logger, 'info').mockImplementation(() => {
        /* do nothing in this test */
      });

      vi.spyOn(Provider.prototype, 'interactionDetails').mockResolvedValueOnce(
        // @ts-ignore
        {
          uid: '1234',
          prompt: {
            name: 'Haven',
          } as PromptDetail,
          params: {},
        },
      );

      vi.mock('../../../src/openid-connect/handler/current-session.js');
      // @ts-ignore
      vi.mocked(getSessionByUid).mockReturnValue(validSession);

      // @ts-ignore
      vi.spyOn(HavenAccountProvider.prototype, 'authenticateWithSession').mockReturnValue({
        accountId: '1234',
        hid: '1234',
      } as AuthenticationResult);

      vi.mock('../../../src/utils/interaction.js');
      // @ts-ignore
      vi.mocked(completeLoginInteraction).mockResolvedValue({
        redirectTo: '/somewhere',
        hid: '1234',
      });

      const { status, body } = await makeCompliantRequest(autoURL);

      expect(status).toEqual(200);
      expect(body.redirectTo).toBe('/somewhere');
      expect(body.hid).toBe('1234');
    });

    // TODO upgrade cases
  });
});
