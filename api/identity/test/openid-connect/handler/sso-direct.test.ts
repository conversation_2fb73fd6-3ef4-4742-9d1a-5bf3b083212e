import * as http from 'http';
import * as http2 from 'http2';
// @ts-ignore
import { beforeEach, describe, expect, it, vi } from 'vitest';
import Provider, { InteractionResults } from 'oidc-provider';
import { interactionSSO } from '../../../src/openid-connect/handler/interaction-sso.js';
import {
  getSessionByUid,
  IdentitySession,
} from '../../../src/openid-connect/handler/current-session.js';
import { HavenAccountProvider } from '../../../src/openid-connect/account-provider/account-provider.js';
import {
  AuthenticationResult,
  UpgradeNeededResult,
} from '../../../src/openid-connect/account-provider/types.js';
import { AuthenticationEventContext } from '../../../src/authenticate/authenticate.event.js';
import { FastifyReply, FastifyRequest } from 'fastify';
import { Payload } from '../../../src/utils/responses.js';
import { validSession, validIat, validHid, validAuths } from './request-helper.js';

const mockUnleash = { isEnabled: vi.fn().mockReturnValue(false) };
vi.mock('../../../src/services/unleash.js', async () => ({
  ...(await vi.importActual<typeof import('../../../src/services/unleash.js')>(
    '../../../src/services/unleash.js',
  )),
  createFeatureFlagChecker: vi.fn(() => mockUnleash),
}));

beforeEach(() => {
  vi.resetAllMocks();
  vi.restoreAllMocks();
});

describe('direct call tests', async () => {
  const testOIDCProvider = (
    prompt: string,
    session: IdentitySession = {},
    result: InteractionResults | undefined = undefined,
  ): Provider => {
    const interactionDetails = async (
      req: http.IncomingMessage | http2.Http2ServerRequest,
      res: http.ServerResponse | http2.Http2ServerResponse,
    ): Promise<object> => {
      return { uid: '1234', params: {}, grantId: 'ABCD', prompt: { name: prompt }, result };
    };

    const interactionResult = async (
      req: http.IncomingMessage | http2.Http2ServerRequest,
      res: http.ServerResponse | http2.Http2ServerResponse,
      result: InteractionResults,
      options?: {
        mergeWithLastSubmission?: boolean | undefined;
      },
    ): Promise<string> => {
      return '/somewhere';
    };

    return {
      interactionDetails,
      interactionResult,
      Session: {
        findByUid: async (uid: string) => {
          return session;
        },
      },
    } as Provider;
  };

  const testHavenProvider = (
    result: AuthenticationResult | UpgradeNeededResult | undefined,
  ): HavenAccountProvider => {
    const authenticateWithSession = async (
      session: IdentitySession,
      context: AuthenticationEventContext,
    ): Promise<AuthenticationResult | UpgradeNeededResult | undefined> => {
      return result;
    };
    return {
      authenticateWithSession,
    } as HavenAccountProvider;
  };

  const testRequest = (
    headers: { [key: string]: string },
    cookies: { [key: string]: string } = {},
  ): FastifyRequest => {
    return {
      headers,
      cookies,
    } as unknown as FastifyRequest;
  };

  interface TestReply {
    sendlog: Payload[];
    cookie: () => void;
    send: (payload: Payload) => TestReply;
    code: (code: number) => TestReply;
  }

  const testReply = () => {
    const reply = {
      sendlog: [] as Payload[],
      cookie: () => {},
      send: function (payload: Payload) {
        this.sendlog.push(payload);
        return this;
      },
      code: function (code: number) {
        return this;
      },
      clearCookie: function (
        name: string,
        options?: fastifyCookie.CookieSerializeOptions | undefined,
      ) {
        return this;
      },
    };
    return reply as unknown as FastifyReply;
  };

  it('can call handler directly', async () => {
    const handler = interactionSSO(
      testOIDCProvider('owners', undefined),
      testHavenProvider(undefined),
    );
    const req = testRequest({
      'x-app-id': 'ugh',
    });
    const res = testReply();
    await handler(req, res);
  });

  it('can call handler and return response', async () => {
    vi.mock('../../../src/openid-connect/handler/current-session.js');
    // @ts-ignore
    vi.mocked(getSessionByUid).mockReturnValue(validSession);

    const handler = interactionSSO(
      testOIDCProvider('owners', {
        iat: validIat(),
        accountId: validHid,
        authorizations: validAuths,
      }),
      testHavenProvider({
        accountId: '1234',
        hid: '1234',
        isOwner: true,
      }),
    );
    const req = testRequest(
      {
        'x-app-id': 'service-haven-identity',
        'x-api-key': process.env.IDENTITY_SSO_API_KEY as string,
      },
      {
        'client-id': 'owners',
      },
    );
    const res = testReply() as unknown as TestReply & FastifyReply;
    await handler(req, res);
    expect(res.sendlog).toEqual([{ redirectTo: '/somewhere', hid: '1234' }]);
  });

  it('will fail autologin if identity-skipautologin cookie is set', async () => {
    vi.mock('../../../src/openid-connect/handler/current-session.js');
    // @ts-ignore
    vi.mocked(getSessionByUid).mockReturnValue(validSession);

    const handler = interactionSSO(
      testOIDCProvider('owners', {
        iat: validIat(),
        accountId: validHid,
        authorizations: validAuths,
      }),
      testHavenProvider({
        accountId: '1234',
        hid: '1234',
        isOwner: true,
      }),
    );
    const req = testRequest(
      {
        'x-app-id': 'service-haven-identity',
        'x-api-key': process.env.IDENTITY_SSO_API_KEY as string,
      },
      {
        'client-id': 'owners',
        'identity-skipautologin': 'true',
      },
    );
    const res = testReply() as unknown as TestReply & FastifyReply;
    await handler(req, res);
    expect(res.sendlog).toEqual([
      expect.objectContaining({ userLoggedIn: false, askUserToReAuthenticate: true }),
    ]);
  });
});
