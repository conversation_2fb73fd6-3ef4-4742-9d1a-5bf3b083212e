import { afterEach, describe, expect, it, vi, beforeEach, beforeAll, afterAll, Mock } from 'vitest';
import * as customSpan from '../../../src/utils/custom-span.js';
import {
  checkStartInteractionResponse,
  getInteractionSSO,
  startAuthenticationWithPKCE,
} from '../handler/request-helper.js';
import { Server } from '../../server.js';
import { PgHavenIdentityTransactionalStore } from 'haven-hid-pgsql-adapter';
import { identityStore, setupLiteIdentity } from '../../helpers.js';
import { nextValidEmail } from 'haven-hid-domain/mock.js';
import { logger } from '@havenengineering/module-haven-logging';
import { AuthenticationMethodType, AuthenticationOutcome, AutoLoginType } from 'haven-hid-domain';
import Provider from 'oidc-provider';

const mocks = vi.hoisted(() => ({
  mockCreateLocalJWKSet: vi.fn(),
  jwks: vi.fn(),
  mockJwtVerify: vi.fn(),
  mockDecodeJwt: vi.fn(),
  mockGetSessionByUid: vi.fn(),
  mockGetCurrentSession: vi.fn(),
  mockIsEnabled: vi.fn(),
}));

const mockUnleash = { isEnabled: mocks.mockIsEnabled };
vi.mock('../../../src/services/unleash.js', async () => ({
  ...(await vi.importActual<typeof import('../../../src/services/unleash.js')>(
    '../../../src/services/unleash.js',
  )),
  createFeatureFlagChecker: vi.fn(() => mockUnleash),
}));

vi.mock('../../../src/openid-connect/handler/current-session.js', async () => ({
  ...(await vi.importActual<
    typeof import('../../../src/openid-connect/handler/current-session.js')
  >('../../../src/openid-connect/handler/current-session.js')),
  getSessionByUid: mocks.mockGetSessionByUid,
  getCurrentSession: mocks.mockGetCurrentSession,
}));

vi.mock('jose', async () => ({
  ...(await vi.importActual<typeof import('jose')>('jose')),
  jwtVerify: mocks.mockJwtVerify,
  createLocalJWKSet: mocks.mockCreateLocalJWKSet.mockReturnValue(mocks.jwks),
  decodeJwt: mocks.mockDecodeJwt,
}));

vi.mock('haven-hid-pgsql-adapter/adapter.js');
(PgHavenIdentityTransactionalStore as Mock).mockImplementation(() => identityStore);

describe('interaction mobile autologin', async () => {
  process.env.COOKIE_ENCRYPTION_KEYS = 'secret,old-secret';
  process.env.SPA_APP_URL = '/';

  const server = new Server();
  const identity = await setupLiteIdentity(nextValidEmail());

  const currentTimestamp = Math.floor(Date.now() / 1000);
  const mockAccessToken = {
    client_id: 'havenguestapp',
    iss: 'https://id.testhaven.com/identity',
    aud: 'havenguestapp',
    hid: identity.hid,
    accounts: identity.accounts,
    sub: identity.hid,
    auth_time: currentTimestamp,
    exp: currentTimestamp + 100,
    iat: currentTimestamp,
    sessionUid: 'session-uid',
    grantId: 'grant-id',
  };
  const setCustomSpanSpy = vi.spyOn(customSpan, 'setCustomSpan');
  const setCustomSpansSpy = vi.spyOn(customSpan, 'setCustomSpans');

  const startAuthOptions = {
    client_id: 'havenguestapp',
    redirect_uri: 'com.bourne.haven://redirect',
  };

  beforeAll(async () => {
    await server.start();
  });

  afterAll(async () => {
    await server.stop();
  });

  beforeEach(() => {
    vi.resetAllMocks();
    vi.clearAllMocks();
    mocks.mockIsEnabled.mockReturnValue(false);
    process.env.JWKS = JSON.stringify({
      keys: [{ kty: 'RSA', e: 'ABCD', n: '0', kid: 'test-key' }],
    });
  });

  afterEach(() => {
    vi.resetAllMocks();
    delete process.env.JWKS;
  });

  describe('when haven-mobile-login cookie is valid', () => {
    it('should authenticate the user and return auth redirect', async () => {
      mocks.mockIsEnabled.mockReturnValue(true);
      mocks.mockDecodeJwt.mockReturnValue(mockAccessToken);
      mocks.mockJwtVerify.mockResolvedValue({ payload: mockAccessToken });
      mocks.mockGetSessionByUid.mockResolvedValue({
        exp: currentTimestamp + 100,
        loginTimestamp: currentTimestamp,
        clientId: mockAccessToken.client_id,
        uid: mockAccessToken.sessionUid,
        accountId: identity.hid,
      });

      const startInteractionResponse = await startAuthenticationWithPKCE(server, startAuthOptions);
      checkStartInteractionResponse(startInteractionResponse);

      const ssoResponse = await getInteractionSSO(server, [
        ...startInteractionResponse.cookies,
        { name: 'haven-mobile-login', value: JSON.stringify(mockAccessToken) },
      ]);
      const parsedBody = JSON.parse(ssoResponse.body);

      expect(setCustomSpanSpy).toHaveBeenCalledWith('client_id', mockAccessToken.client_id);
      expect(setCustomSpansSpy).toHaveBeenCalledWith({
        auth_method: AuthenticationMethodType.IDENTITY_ACCESS_TOKEN,
        auth_outcome: AuthenticationOutcome.SUCCESS,
        auto_login_type: AutoLoginType.MOBILE_APP_AUTO,
      });

      expect(ssoResponse.status).toEqual(200);
      expect(parsedBody).toEqual(
        expect.objectContaining({
          redirectTo: expect.any(String),
          hid: identity.hid,
        }),
      );
    });
  });

  describe('error scenarios', () => {
    it('should return interaction details when HID in access token is invalid', async () => {
      const loggerWarnSpy = vi.spyOn(logger, 'warn');
      const mockFailAuthAccessToken = {
        ...mockAccessToken,
        hid: 'RANDOMHID123',
      };

      mocks.mockIsEnabled.mockReturnValue(true);
      mocks.mockDecodeJwt.mockReturnValue(mockFailAuthAccessToken);
      mocks.mockJwtVerify.mockResolvedValue({ payload: mockFailAuthAccessToken });
      mocks.mockGetSessionByUid.mockResolvedValue({
        exp: currentTimestamp + 100,
        loginTimestamp: currentTimestamp,
        clientId: mockAccessToken.client_id,
        uid: mockAccessToken.sessionUid,
        accountId: identity.hid,
      });

      const startInteractionResponse = await startAuthenticationWithPKCE(server, startAuthOptions);
      checkStartInteractionResponse(startInteractionResponse);

      const ssoResponse = await getInteractionSSO(server, [
        ...startInteractionResponse.cookies,
        { name: 'haven-mobile-login', value: JSON.stringify(mockFailAuthAccessToken) },
      ]);
      const parsedBody = JSON.parse(ssoResponse.body);

      expect(loggerWarnSpy).toHaveBeenCalledWith(
        'Failed to authenticate with mobile login token',
        expect.objectContaining({
          error: {
            message: 'Invalid token, accountId does not match the session',
          },
          identity_context: expect.objectContaining({
            type: 'session',
            app_id: 'service-haven-identity',
            client_id: 'havenguestapp',
            hid: 'RANDOMHID123',
            sub: identity.hid,
            sessionUid: 'session-uid',
            grantId: 'grant-id',
            auth_time: expect.any(Number),
            token_exp: expect.any(Number),
            token_iat: expect.any(Number),
            aud: 'havenguestapp',
            sessionExp: expect.any(Number),
          }),
        }),
      );

      expect(ssoResponse.status).toEqual(200);
      expect(parsedBody).toEqual(
        expect.objectContaining({
          uid: expect.any(String),
          clientContext: expect.objectContaining({
            id: 'havenguestapp',
            name: 'Guest App',
          }),
          upgrade: false,
          askUserToReAuthenticate: true,
          userLoggedIn: false,
        }),
      );
    });

    it('should return interaction details when access token fails JWT verification', async () => {
      const loggerWarnSpy = vi.spyOn(logger, 'warn');

      mocks.mockIsEnabled.mockReturnValue(true);
      mocks.mockDecodeJwt.mockReturnValue(mockAccessToken);
      mocks.mockJwtVerify.mockRejectedValue(new Error('Token expired'));

      const startInteractionResponse = await startAuthenticationWithPKCE(server, startAuthOptions);
      checkStartInteractionResponse(startInteractionResponse);

      const ssoResponse = await getInteractionSSO(server, [
        ...startInteractionResponse.cookies,
        { name: 'haven-mobile-login', value: JSON.stringify(mockAccessToken) },
      ]);
      const parsedBody = JSON.parse(ssoResponse.body);

      expect(setCustomSpanSpy).toHaveBeenCalledWith('client_id', mockAccessToken.client_id);
      expect(loggerWarnSpy).toHaveBeenCalledWith(
        'Failed to authenticate with mobile login token',
        expect.objectContaining({
          error: {
            message: 'Invalid token: Token expired',
          },
          identity_context: expect.objectContaining({
            app_id: 'service-haven-identity',
            client_id: 'havenguestapp',
            hid: identity.hid,
            sub: identity.hid,
            sessionUid: 'session-uid',
            grantId: 'grant-id',
            auth_time: expect.any(Number),
            token_exp: expect.any(Number),
            token_iat: expect.any(Number),
            aud: 'havenguestapp',
          }),
        }),
      );
      const havenMobileLoginCookie = ssoResponse.cookies.find(
        (cookie) => cookie.name === 'haven-mobile-login',
      );
      expect(havenMobileLoginCookie).toBeDefined();
      expect(havenMobileLoginCookie?.value).toBeUndefined();

      expect(ssoResponse.status).toEqual(200);
      expect(parsedBody).toEqual(
        expect.objectContaining({
          uid: expect.any(String),
          clientContext: expect.objectContaining({
            id: 'havenguestapp',
            name: 'Guest App',
          }),
          upgrade: false,
          askUserToReAuthenticate: true,
          userLoggedIn: false,
        }),
      );
    });

    it('should return correct response when handler throws an error and the user is not logged in', async () => {
      vi.spyOn(Provider.prototype, 'interactionDetails').mockImplementation(() => {
        throw new Error('Test error');
      });
      const loggerErrorSpy = vi.spyOn(logger, 'error');

      mocks.mockIsEnabled.mockReturnValue(true);
      const startInteractionResponse = await startAuthenticationWithPKCE(server, startAuthOptions);
      checkStartInteractionResponse(startInteractionResponse);

      const ssoResponse = await getInteractionSSO(server, [
        ...startInteractionResponse.cookies,
        { name: 'haven-mobile-login', value: JSON.stringify(mockAccessToken) },
        { name: 'client-id', value: mockAccessToken.client_id },
      ]);
      const parsedBody = JSON.parse(ssoResponse.body);

      expect(loggerErrorSpy).toHaveBeenCalledWith('Test error', expect.any(Object));
      expect(ssoResponse.status).toEqual(400);
      expect(parsedBody).toEqual(
        expect.objectContaining({
          clientContext: expect.objectContaining({
            id: 'havenguestapp',
            name: 'Guest App',
          }),
          error: { message: 'Test error' },
        }),
      );
    });

    it('should return correct response when handler throws an error and the user is logged in', async () => {
      const loggerErrorSpy = vi.spyOn(logger, 'error');

      vi.spyOn(Provider.prototype, 'interactionDetails').mockImplementation(() => {
        throw new Error('Test error');
      });

      mocks.mockIsEnabled.mockReturnValue(true);
      mocks.mockGetCurrentSession.mockResolvedValue({
        uid: 'session-uid',
        clientId: mockAccessToken.client_id,
        accountId: identity.hid,
        loginTimestamp: **********,
      });

      const startInteractionResponse = await startAuthenticationWithPKCE(server, startAuthOptions);
      checkStartInteractionResponse(startInteractionResponse);

      const ssoResponse = await getInteractionSSO(server, [
        ...startInteractionResponse.cookies,
        { name: 'haven-mobile-login', value: JSON.stringify(mockAccessToken) },
      ]);
      const parsedBody = JSON.parse(ssoResponse.body);

      expect(loggerErrorSpy).toHaveBeenCalledWith('Test error', expect.any(Object));
      expect(ssoResponse.status).toEqual(400);
      expect(parsedBody).toEqual(
        expect.objectContaining({
          clientContext: expect.objectContaining({
            id: 'havenguestapp',
            name: 'Guest App',
          }),
          error: { message: 'Test error' },
          userLoggedIn: true,
          askUserToReAuthenticate: true,
        }),
      );
    });
  });
});
