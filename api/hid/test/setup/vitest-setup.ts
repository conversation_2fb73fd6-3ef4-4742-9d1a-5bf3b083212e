import { expect } from 'vitest';
import * as hidMatchers from './hid-matchers.js';
import * as httpMatchers from './http-matchers.js';
import { HidMatchers } from './hid-matchers.js';
import { HttpMatchers } from './http-matchers.js';

expect.extend(hidMatchers);
expect.extend(httpMatchers);

declare module 'vitest' {
  interface Assertion<T = any> extends HidMatchers<T>, HttpMatchers<T> {}
  interface AsymmetricMatchersContaining extends HidMatchers, HttpMatchers {}
}

process.env.AUTHENTICATION_CONFIG = JSON.stringify({
  clients: [
    {
      client: 'test',
      key: 'MY-KEY',
      roles: ['client'],
    },
    {
      client: 'admin',
      key: 'ADMIN-KEY',
      roles: ['admin'],
    },
    {
      client: 'management',
      key: 'MANAGEMENT-KEY',
      roles: ['user_management'],
    },
  ],
});

process.env.PLOT_API_URL = 'https://plot.local/api';
process.env.PLOT_API_KEY = 'plot_secret';
process.env.BLAPI_URL = 'https://blapi.local/api';
process.env.BLAPI_IDENTITY_URL = 'https://blapi.local/api/identity';
process.env.BLAPI_X_API_KEY = 'aws-gateway-key';
process.env.BLAPI_X_IDENTITY_KEY = 'secret-identity-key';
process.env.BLAPI_ACCOUNT_URL = 'https://blapi-account.local/api';
process.env.BLAPI_ACCOUNT_X_API_KEY = 'aws-gateway-key';
process.env.IDENTITY_JWKS_URL = 'https://test.haven.com/identity/jwks';
process.env.OWNERS_BLOCKED_EMAIL_DOMAINS = 'blocked-domain-1.com,blocked-domain-1.com';
