// @ts-ignore
import request from 'superagent';
import { fail, pass } from './vitest-helpers.js';

export const toBeOK = (received: request.Response) => {
  if (received.status == 200) {
    return pass();
  } else {
    return failResponse(received);
  }
};
export const toBeBadRequest = (received: request.Response) => {
  return expectStatus(400, received);
};

export const toBeNotFound = (received: request.Response) => {
  return expectStatus(404, received);
};

export const toBeUnauthorised = (received: request.Response) => {
  return expectStatus(403, received);
};

export const toBeMethodNotAllowed = (received: request.Response) => {
  return expectStatus(405, received);
};

const expectStatus = (code: number, received: request.Response) => {
  if (received.status == code) {
    return pass();
  } else {
    return failResponse(received);
  }
};

const failResponse = (received: request.Response) => {
  return fail(`Unexpected response: [${received.status}][${JSON.stringify(received.body)}]`);
};

export interface HttpMatchers<R = unknown> {
  toBeBadRequest(): R;
  toBeOK(): R;
  toBeMethodNotAllowed(): R;
  toBeNotFound(): R;
  toBeUnauthorised(): R;
}
