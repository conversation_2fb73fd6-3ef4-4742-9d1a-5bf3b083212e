// @ts-ignore
import request from 'superagent';
import { isValidHID } from 'haven-hid-domain';
import { fail, pass } from './vitest-helpers.js';

export const toBeValidHIDResponse = (received: request.Response) => {
  const data = received.body;

  if (isValidHID(data.hid)) {
    return pass();
  } else {
    return fail(`Not a valid HID: [${JSON.stringify(data)}]`);
  }
};

export interface HidMatchers<R = unknown> {
  toBeValidHIDResponse(): R;
}
