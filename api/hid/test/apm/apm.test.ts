import { describe, expect, test, vi } from 'vitest';
import apm from '../../src/apm/apm.js';
import { HavenApm } from '@havenengineering/module-shared-apm';

vi.mock('@havenengineering/module-shared-apm', () => ({
  HavenApm: {
    Start: vi.fn(),
    Context: {
      use: vi.fn(),
    },
  },
}));

describe('Apm', () => {
  const mockStart = HavenApm.Start;
  const mockContextUse = HavenApm.Context?.use;

  test('should start and setup contexts', () => {
    apm();
    expect(mockStart).toBeCalledWith({
      tags: {
        platform: 'identity',
      },
    });
    expect(mockContextUse).toBeCalledTimes(1);
    expect(mockContextUse).toBeCalledWith('http', {
      headers: ['x-app-id'],
      blocklist: ['/status', '/metrics'],
    });
  });
});
