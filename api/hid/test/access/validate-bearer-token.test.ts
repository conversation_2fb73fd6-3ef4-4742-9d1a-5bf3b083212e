import { describe, expect, it, Mock, vi } from 'vitest';
import { getBearerToken, validateBearerToken } from '../../src/access/validate-bearer-token.js';
import { jwtVerify } from 'jose';

describe('bearer token', () => {
  describe('validateBearerToken', () => {
    it('Bearer authorization is parsed', async () => {
      const authorizationHeader = 'Bearer xxxx';
      const result = getBearerToken(authorizationHeader);
      expect(result).toEqual('xxxx');
    });

    it('no header is rejected', async () => {
      const authorizationHeader = undefined;
      expect(() => getBearerToken(authorizationHeader)).toThrow('No bearer token for identity');
    });

    it('Non Bearer authorization is rejected', async () => {
      const authorizationHeader = 'xxxx';
      expect(() => getBearerToken(authorizationHeader)).toThrow('No bearer token for identity');
    });
  });

  describe('validateBearerToken', () => {
    const jwks = vi.fn();
    const payload = {
      sub: '<some-hid>',
    };

    it('Bearer authorization is verified', async () => {
      vi.mock('jose');
      const mockVerify = jwtVerify as Mock;
      mockVerify.mockResolvedValue({ payload });
      const result = await validateBearerToken(jwks, 'xxxx');
      expect(result).toEqual(payload);
      expect(mockVerify).toHaveBeenCalledWith('xxxx', jwks);
    });

    it('Bearer authorization throws error from verifier', async () => {
      vi.mock('jose');
      const mockVerify = jwtVerify as Mock;
      mockVerify.mockRejectedValue(new Error('some error with verify'));
      await expect(validateBearerToken(jwks, 'xxxx')).rejects.toThrow('Invalid token');
      expect(mockVerify).toHaveBeenCalledWith('xxxx', jwks);
    });
  });
});
