import { beforeEach, describe, expect, it, Mock, vi } from 'vitest';
import { jwksProvider } from '../../src/access/jwks-provider.js';
import { createRemoteJWKSet } from 'jose';
import casual from 'casual';

vi.mock('jose');
describe('jwks-provider', () => {
  const mockKeys = vi.fn();

  beforeEach(() => {
    vi.resetAllMocks();
  });

  it('jwks provider provides key set', async () => {
    const url = casual.url;
    (createRemoteJWKSet as Mock).mockReturnValue(mockKeys);
    const provider = jwksProvider(url);

    const keys = await provider.jwks();
    expect(keys).toEqual(mockKeys);
    expect(createRemoteJWKSet).toHaveBeenCalledWith(new URL(url), {
      headers: { 'User-Agent': 'hid-jwt-verify' },
    });
  });

  it('jwks provider provides same keyset from future calls', async () => {
    const url = casual.url;
    (createRemoteJWKSet as Mock).mockReturnValue(mockKeys);
    const provider = jwksProvider(url);
    const keys1 = await provider.jwks();
    const keys2 = await provider.jwks();

    expect(keys1).toEqual(mockKeys);
    expect(keys2).toEqual(mockKeys);
    expect(createRemoteJWKSet).toHaveBeenCalledOnce();
  });

  it('jwks provider provides same keyset from other calls', async () => {
    const url = casual.url;
    (createRemoteJWKSet as Mock).mockReturnValue(mockKeys);
    const keys1 = await jwksProvider(url).jwks();
    const keys2 = await jwksProvider(url).jwks();

    expect(keys1).toEqual(mockKeys);
    expect(keys2).toEqual(mockKeys);
    expect(createRemoteJWKSet).toHaveBeenCalledOnce();
  });

  it('jwks provider provides different keys for different urls', async () => {
    const url = casual.url;
    (createRemoteJWKSet as Mock).mockReturnValue(mockKeys);
    const keys1 = await jwksProvider(url).jwks();
    expect(keys1).toEqual(mockKeys);

    const otherUrl = casual.url;
    const otherKeys = vi.fn();
    (createRemoteJWKSet as Mock).mockReturnValue(otherKeys);
    const keys2 = await jwksProvider(otherUrl).jwks();
    expect(keys2).toEqual(otherKeys);

    expect(createRemoteJWKSet).toHaveBeenCalledTimes(2);
  });

  it('throws error if no url', async () => {
    expect(() => jwksProvider('')).toThrow('IDENTITY_JWKS_URL env-var is not set');
  });
});
