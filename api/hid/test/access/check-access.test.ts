import { beforeAll, describe, expect, it, vi } from 'vitest';
import { FastifyReply, FastifyRequest } from 'fastify';
import { checkAccess, validateAccount } from '../../src/access/check-access.js';
import AuthenticationConfig, { Role } from '../../src/config/authentication-config.js';
import { HavenServiceAccountIdType } from 'haven-hid-domain';
import { nextPlotOwnerId } from 'haven-hid-domain/mock.js';

describe('access-checks', () => {
  let config: AuthenticationConfig;

  beforeAll(() => {
    process.env.AUTHENTICATION_CONFIG =
      '{ "clients":[{"client":"test","key":"MY-KEY", "roles":["client"]},{"client":"admin","key":"ADMIN-KEY","roles":["admin"]}]}';
    config = new AuthenticationConfig();
  });

  describe('checkAccess', () => {
    it('calls done when app-id and app-key headers are present', () => {
      const reply = {} as FastifyReply;
      const request = {
        headers: {
          'x-app-id': 'APP',
          'x-app-key': 'MY-KEY',
        },
      } as any as FastifyRequest;
      const done = vi.fn();
      checkAccess(config, Role.CLIENT)(request, reply, done);
      expect(done).toHaveBeenCalled();
    });

    it('throws access error when app-id is missing', () => {
      const reply = {} as FastifyReply;
      const request = {
        headers: { 'x-app-key': 'MY-KEY' },
      } as any as FastifyRequest;
      const done = vi.fn();

      expect(() => checkAccess(config, Role.CLIENT)(request, reply, done)).toThrowError(
        'x-app-id header is missing',
      );
      expect(done).not.toHaveBeenCalled();
    });

    it('throws access error when app-id is blank', () => {
      const reply = {} as FastifyReply;
      const request = {
        headers: {
          'x-app-id': '',
          'x-app-key': 'MY-KEY',
        },
      } as any as FastifyRequest;
      const done = vi.fn();

      expect(() => checkAccess(config, Role.CLIENT)(request, reply, done)).toThrowError(
        'x-app-id header is missing',
      );
      expect(done).not.toHaveBeenCalled();
    });

    it('calls done when valid client key provided ', () => {
      const reply = {} as FastifyReply;
      const request = {
        headers: {
          'x-app-id': 'APP',
          'x-app-key': 'MY-KEY',
        },
      } as any as FastifyRequest;
      const done = vi.fn();
      checkAccess(config, Role.CLIENT)(request, reply, done);
      expect(done).toHaveBeenCalled();
    });

    it('calls done when valid admin key provided ', () => {
      const reply = {} as FastifyReply;
      const request = {
        headers: {
          'x-app-id': 'APP',
          'x-app-key': 'ADMIN-KEY',
        },
      } as any as FastifyRequest;
      const done = vi.fn();
      checkAccess(config, Role.ADMIN)(request, reply, done);
      expect(done).toHaveBeenCalled();
    });

    it('throws AccessError when Client APP_KEY is not valid', () => {
      expect(() =>
        checkAccess(config, Role.CLIENT)(
          {
            headers: {
              'x-app-id': 'APP',
              'x-app-key': 'ADMIN-KEY',
            },
          } as any as FastifyRequest,
          {} as FastifyReply,
          () => {},
        ),
      ).toThrowError('invalid x-app-key header');
    });

    it('throws AccessError when Admin APP_KEY is not valid', () => {
      expect(() =>
        checkAccess(config, Role.ADMIN)(
          {
            headers: {
              'x-app-id': 'APP',
              'x-app-key': 'MY-KEY',
            },
          } as any as FastifyRequest,
          {} as FastifyReply,
          () => {},
        ),
      ).toThrowError('invalid x-app-key header');
    });

    it('throws AccessError when APP_KEY is blank', () => {
      expect(() =>
        checkAccess(config, Role.CLIENT)(
          {
            headers: {
              'x-app-id': 'APP',
              'x-app-key': '',
            },
          } as any as FastifyRequest,
          {} as FastifyReply,
          () => {},
        ),
      ).toThrowError('x-app-key header is required');
    });

    it('throws access error when Bearer token is missing', () => {
      const reply = {} as FastifyReply;
      const request = {
        headers: { 'x-app-id': 'APP' },
      } as any as FastifyRequest;
      const done = vi.fn();

      expect(() => checkAccess(config, Role.CLIENT)(request, reply, done)).toThrowError(
        'x-app-key header is required',
      );
      expect(done).not.toHaveBeenCalled();
    });

    it('throws access error when Bearer token is blank', () => {
      const reply = {} as FastifyReply;
      const request = {
        headers: {
          'x-app-id': 'APP',
          'x-app-key': '',
        },
      } as any as FastifyRequest;
      const done = vi.fn();

      expect(() => checkAccess(config, Role.CLIENT)(request, reply, done)).toThrowError(
        'x-app-key header is required',
      );
      expect(done).not.toHaveBeenCalled();
    });

    it('throws access error when key is unknown', () => {
      const reply = {} as FastifyReply;
      const request = {
        headers: {
          'x-app-id': 'APP',
          'x-app-key': 'hack-attempt',
        },
      } as any as FastifyRequest;
      const done = vi.fn();

      expect(() => checkAccess(config, Role.CLIENT)(request, reply, done)).toThrowError(
        'invalid x-app-key header',
      );
      expect(done).not.toHaveBeenCalled();
    });

    it('throws access error when key is supplied but unknown', () => {
      const reply = {} as FastifyReply;
      const request = {
        headers: {
          'x-app-id': 'APP',
          'x-app-key': 'hack-attempt',
        },
      } as any as FastifyRequest;
      const done = vi.fn();

      expect(() => checkAccess(config, Role.ADMIN)(request, reply, done)).toThrowError(
        'invalid x-app-key header',
      );
      expect(done).not.toHaveBeenCalled();
    });
  });

  describe('validateAccount', () => {
    it('validates when accounts contain the request id', async () => {
      const id = nextPlotOwnerId();
      validateAccount(id, HavenServiceAccountIdType.OWNER_ID, {
        accounts: [
          {
            id: id,
            type: HavenServiceAccountIdType.OWNER_ID,
          },
        ],
      });
    });

    it('fails when no accounts but account provided', async () => {
      const id = nextPlotOwnerId();
      expect(() =>
        validateAccount(id, HavenServiceAccountIdType.OWNER_ID, {
          accounts: [],
        }),
      ).toThrowError('Access token accounts do not match request');
    });

    it('validates when no account id/type provided with accounts', async () => {
      const id = nextPlotOwnerId();
      validateAccount(undefined, undefined, {
        accounts: [
          {
            id: 1234,
            type: HavenServiceAccountIdType.OWNER_ID,
          },
        ],
      });
    });

    it('validates when no account id/type provided with no accounts', async () => {
      const id = nextPlotOwnerId();
      validateAccount(undefined, undefined, {
        accounts: [],
      });
    });

    it('fails when account type does not match', async () => {
      const id = nextPlotOwnerId();
      expect(() =>
        validateAccount(id, HavenServiceAccountIdType.OWNER_ID, {
          accounts: [
            {
              id,
              type: HavenServiceAccountIdType.SEAWARE_CLIENT_ID,
            },
          ],
        }),
      ).toThrowError('Access token accounts do not match request');
    });

    it('fails when account id does not match', async () => {
      const id = nextPlotOwnerId();
      const otherId = nextPlotOwnerId();
      expect(() =>
        validateAccount(id, HavenServiceAccountIdType.OWNER_ID, {
          accounts: [
            {
              id: otherId,
              type: HavenServiceAccountIdType.OWNER_ID,
            },
          ],
        }),
      ).toThrowError('Access token accounts do not match request');
    });
  });
});
