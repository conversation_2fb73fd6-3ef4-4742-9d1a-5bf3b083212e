import { getDependencies } from '../../src/dependencies/dependencies.js';
import { describe, expect, it } from 'vitest';
import {
  HavenIdentityTransactionalStore,
  RightToBeForgotten,
  HIDForEmailPlotAndSeaWareTransactional,
  HIDEmailVerificationRequestGenerator,
} from 'haven-hid-domain';
import { BLAPI, BLAPIAccount } from 'haven-identity-blapi-client';
import { Plot } from 'haven-identity-plot-client';

describe('getDependencies function', () => {
  it('returns an object with expected dependencies', () => {
    const result = getDependencies();

    expect(result).toHaveProperty('identityManagement');
    expect(result).toHaveProperty('blapi');
    expect(result).toHaveProperty('blapiAccount');
    expect(result).toHaveProperty('plot');
    expect(result).toHaveProperty('keysProvider');
    expect(result).toHaveProperty('store');
    expect(result).toHaveProperty('hidStore');
    expect(result).toHaveProperty('rightToBeForgotten');
    expect(result).toHaveProperty('emailVerification');

    expect(result.identityManagement).toBeInstanceOf(HavenIdentityTransactionalStore);
    expect(result.blapi).toBeInstanceOf(BLAPI);
    expect(result.blapiAccount).toBeInstanceOf(BLAPIAccount);
    expect(result.plot).toBeInstanceOf(Plot);
    expect(result.keysProvider.jwks).toBeDefined();
    expect(result.rightToBeForgotten).toBeInstanceOf(RightToBeForgotten);
    expect(result.store).toBeInstanceOf(HIDForEmailPlotAndSeaWareTransactional);
    expect(result.hidStore).toBeDefined();
    expect(result.emailVerification).toBeInstanceOf(HIDEmailVerificationRequestGenerator);
  });

  it('ensures singleton behaviour', () => {
    const firstCall = getDependencies();
    const secondCall = getDependencies();

    expect(firstCall).toBe(secondCall);
  });
});
