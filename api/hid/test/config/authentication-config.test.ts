import AuthenticationConfig, { Role } from '../../src/config/authentication-config.js';
import { describe, expect, it } from 'vitest';

describe('authentication config', () => {
  it('should create default', () => {
    process.env.AUTHENTICATION_CONFIG = '';
    const instance = new AuthenticationConfig();
    expect(instance.getKeys(Role.ADMIN)).toEqual([]);
    expect(instance.getKeys(Role.CLIENT)).toEqual([]);
  });

  it('creates keys from multipleClients var', () => {
    process.env.AUTHENTICATION_CONFIG = JSON.stringify(multipleClients);
    const instance = new AuthenticationConfig();
    expect(instance.getKeys(Role.ADMIN)).toEqual(['adminsecret', 'dualsecret']);
    expect(instance.getKeys(Role.CLIENT)).toEqual(['johnsecret', 'dualsecret']);
  });

  it('no client roles defaults to client', () => {
    process.env.AUTHENTICATION_CONFIG = JSON.stringify(legacyClients);
    const instance = new AuthenticationConfig();
    expect(instance.getKeys(Role.CLIENT)).toEqual(['johnsecret']);
  });
});

const multipleClients = {
  clients: [
    {
      client: 'admin',
      key: 'adminsecret',
      roles: ['admin'],
    },
    {
      client: 'john',
      key: 'johnsecret',
      roles: ['client'],
    },
    {
      client: 'dual',
      key: 'dualsecret',
      roles: ['client', 'admin'],
    },
  ],
};

const legacyClients = {
  clients: [{ client: 'john', key: 'johnsecret' }],
};
