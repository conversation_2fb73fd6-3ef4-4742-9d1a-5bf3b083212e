// @ts-ignore
import request from 'superagent';
import { afterAll, beforeAll, describe, expect, it } from 'vitest';
import { Server } from '../mock/server.js';

let server: Server;

beforeAll(async () => {
  server = await new Server().start();
});

afterAll(async () => {
  await server.stop();
});

describe('metrics endpoint', () => {
  it('returns ok (200)', async () => {
    const response = await request.get(`${server.baseUrl}/metrics`);
    expect(response.status).toEqual(200);
  });
});
