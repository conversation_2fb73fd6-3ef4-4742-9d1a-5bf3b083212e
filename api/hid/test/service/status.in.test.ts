// @ts-ignore
import request from 'superagent';
import { afterAll, beforeAll, describe, expect, it } from 'vitest';
import { Server } from '../mock/server.js';

let server: Server;

beforeAll(async () => {
  process.env.APP_VERSION = '1.2.3';
  server = await new Server().start();
});

afterAll(async () => {
  await server.stop();
});

describe('status endpoint', () => {
  it('returns ok (200)', async () => {
    const response = await request.get(`${server.baseUrl}/status`);

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      healthy: true,
      version: '1.2.3',
    });
  });
});
