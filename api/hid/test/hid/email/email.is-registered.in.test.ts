import request from 'superagent';
import { afterAll, beforeAll, beforeEach, describe, expect, it, Mock, vi } from 'vitest';

import { PgHavenIdentityTransactionalStore } from 'haven-hid-pgsql-adapter/adapter.js';
import { inMemoryHIDDomain } from 'haven-hid-domain';
import { PlotClientError } from 'haven-identity-plot-client';
import { BlapiClientError } from 'haven-identity-blapi-client';
import { nextIdentity, nextInvalidEmail, nextTx, nextValidEmail } from 'haven-hid-domain/mock.js';

import { URI as path } from '../../../src/hid/email/email.is-registered.in.js';
import { Server } from '../../mock/server.js';
import { X_APP_ID_HEADER, X_APP_KEY_HEADER } from '../../../src/hid/header.js';
import { AxiosError } from 'axios';
import { validEmail } from '../../mock/email.mock.js';

const mockIdentityStore = inMemoryHIDDomain().identityStore;
const mockBlapi = { isRegistered: vi.fn() };
const mockPlot = { isRegistered: vi.fn() };

vi.mock('haven-hid-pgsql-adapter/adapter.js');
(PgHavenIdentityTransactionalStore as Mock).mockImplementation(() => mockIdentityStore);

vi.mock('haven-identity-plot-client', async (originalModule) => ({
  ...(await originalModule<typeof import('haven-identity-plot-client')>()),
  Plot: vi.fn().mockImplementation(() => mockPlot),
}));

vi.mock('haven-identity-blapi-client', async (originalModule) => ({
  ...(await originalModule<typeof import('haven-identity-blapi-client')>()),
  BLAPI: vi.fn().mockImplementation(() => mockBlapi),
}));

const server = new Server();

const makeRequest = async (url: string, email = ''): Promise<request.Response> => {
  return request
    .post(url)
    .ok(() => true)
    .set(X_APP_ID_HEADER, 'test')
    .set(X_APP_KEY_HEADER, 'MY-KEY')
    .send({ email });
};

describe('POST emailIsRegistered Endpoint', () => {
  const url = `${server.baseUrl}${path}`;

  beforeAll(async () => {
    await server.start();
  });

  afterAll(async () => {
    await server.stop();
  });

  beforeEach(() => {
    vi.restoreAllMocks();
  });

  it('should respond with 400 when email is missing ', async () => {
    const { body } = await makeRequest(url);

    expect(body).toMatchObject({
      code: 'VALIDATION',
      message: 'body/email is not a valid email',
      statusCode: 400,
    });
  });

  it('should respond with 400 when email is not valid', async () => {
    const email = 'notValidEmail';

    const { body } = await makeRequest(url, email);

    expect(body).toMatchObject({
      code: 'VALIDATION',
      message: 'body/email is not a valid email',
      statusCode: 400,
    });
  });

  it('should respond with 500 when Plot throws an error', async () => {
    const { email } = nextValidEmail();
    const plotErrorCode = 'PLOT_ERROR';
    const plotErrorMessage = `Error retrieving profile for email`;
    const axiosError = {
      isAxiosError: true,
      request,
      response: {
        data: {
          Errors: [{ Message: 'PLOT API Error' }],
        },
      },
    } as AxiosError;

    mockPlot.isRegistered.mockImplementation(() => {
      throw new PlotClientError(plotErrorCode, plotErrorMessage, axiosError);
    });

    const { body } = await makeRequest(`${server.baseUrl}${path}`, email);

    expect(body).toMatchObject({
      code: plotErrorCode,
      message: plotErrorMessage,
      statusCode: 500,
    });
  });

  it('should respond with 500 when Blapi throws an error', async () => {
    const { email } = nextValidEmail();
    const blapiErrorCode = 'BLAPI_ERROR';
    const blapiErrorMessage = `Error retrieving profile for email`;
    const axiosError = {
      isAxiosError: true,
      request,
      response: {
        data: {
          Errors: [{ Message: 'PLOT API Error' }],
        },
      },
    } as AxiosError;

    mockBlapi.isRegistered.mockImplementation(() => {
      throw BlapiClientError.from(blapiErrorMessage, axiosError, blapiErrorCode);
    });

    const { body } = await makeRequest(`${server.baseUrl}${path}`, email);

    expect(body).toMatchObject({
      code: blapiErrorCode,
      message: blapiErrorMessage,
      statusCode: 500,
    });
  });

  it('should respond with 500 when Identity throws an error', async () => {
    const email = validEmail();
    const errorMessage = 'Unexpected Identity Error';

    vi.spyOn(mockIdentityStore, 'findByEmail').mockRejectedValue(new Error(errorMessage));

    const { body } = await makeRequest(`${server.baseUrl}${path}`, email);

    expect(body).toMatchObject({
      code: 'ERROR',
      message: errorMessage,
      statusCode: 500,
    });
  });

  it('should respond false when a profile does not exist in identity and an email does not exist in plot or blapi', async () => {
    const { email } = nextValidEmail();

    const { body, status } = await makeRequest(`${server.baseUrl}${path}`, email);

    expect(body).toEqual({
      isRegistered: false,
      hid: null,
      source: null,
    });
    expect(status).toBe(200);
  });

  it('should respond true when profile does exist in identity', async () => {
    const identity = nextIdentity();
    const createdIdentity = await mockIdentityStore.createIdentity(identity, nextTx());

    const { body, status } = await makeRequest(`${server.baseUrl}${path}`, identity.email.email);

    expect(body).toEqual({
      isRegistered: true,
      hid: createdIdentity.hid,
      source: 'identity',
    });
    expect(status).toBe(200);
  });

  it('should respond true when a profile does not exist in identity and when an email does exist in plot', async () => {
    const { email } = nextValidEmail();

    mockPlot.isRegistered.mockResolvedValue(true);

    const { body, status } = await makeRequest(`${server.baseUrl}${path}`, email);

    expect(body).toEqual({
      isRegistered: true,
      hid: null,
      source: 'plot',
    });
    expect(status).toBe(200);
  });

  it('should respond true when a profile does not exist in identity and when an email does exist in blapi', async () => {
    const { email } = nextValidEmail();

    mockBlapi.isRegistered.mockResolvedValue(true);

    const { body, status } = await makeRequest(`${server.baseUrl}${path}`, email);

    expect(body).toEqual({
      isRegistered: true,
      hid: null,
      source: 'seaware',
    });
    expect(status).toBe(200);
  });
});
