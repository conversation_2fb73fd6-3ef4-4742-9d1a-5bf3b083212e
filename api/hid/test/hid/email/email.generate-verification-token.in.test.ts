import request from 'superagent';
import { afterAll, beforeAll, beforeEach, describe, expect, it, Mock, vi } from 'vitest';
import {
  PgHavenIdentityTransactionalStore,
  PgHIDForEmailPlotAndSeaWare,
  PgEmailVerificationRequestStore,
  PgEmailStore,
} from 'haven-hid-pgsql-adapter/adapter.js';
import { inMemoryHIDDomain } from 'haven-hid-domain';
import { URI as path } from '../../../src/hid/email/email.generate-verification-token.in.js';
import { Server } from '../../mock/server.js';
import { X_APP_ID_HEADER, X_APP_KEY_HEADER } from '../../../src/hid/header.js';
import { validEmail } from '../../mock/email.mock.js';

const mockIdentityStore = inMemoryHIDDomain().identityStore;
const hidForEmail = inMemoryHIDDomain().hidForEmailPlotAndSeaWare;
const emailVerificationRequestStore = inMemoryHIDDomain().hidEmailVerificationRequestStore;
const emailStore = inMemoryHIDDomain().emailStore;

vi.mock('haven-hid-pgsql-adapter/adapter.js');
(PgHavenIdentityTransactionalStore as Mock).mockImplementation(() => mockIdentityStore);
(PgHIDForEmailPlotAndSeaWare as Mock).mockImplementation(() => hidForEmail);
(PgEmailVerificationRequestStore as Mock).mockImplementation(() => emailVerificationRequestStore);
(PgEmailStore as Mock).mockImplementation(() => emailStore);

const server = new Server();

const makeRequest = async (url: string, email = ''): Promise<request.Response> => {
  return request
    .post(url)
    .ok(() => true)
    .set(X_APP_ID_HEADER, 'test')
    .set(X_APP_KEY_HEADER, 'MY-KEY')
    .send({ email });
};

describe('POST generateEmailVerificationToken Endpoint', () => {
  const url = `${server.baseUrl}${path}`;

  beforeAll(async () => {
    await server.start();
  });

  afterAll(async () => {
    await server.stop();
  });

  beforeEach(() => {
    vi.restoreAllMocks();
  });

  it('should respond with 403 when x-app-id is missing ', async () => {
    const { body } = await request
      .post(url)
      .ok(() => true)
      .set(X_APP_KEY_HEADER, 'MY-KEY')
      .send();

    expect(body).toMatchObject({
      code: 'FORBIDDEN',
      message: 'x-app-id header is missing',
      statusCode: 403,
    });
  });

  it('should respond with 401 when x-app-key is missing ', async () => {
    const { body } = await request
      .post(url)
      .ok(() => true)
      .set(X_APP_ID_HEADER, 'test')
      .send({});

    expect(body).toMatchObject({
      code: 'NOT_AUTHORISED',
      message: 'x-app-key header is required',
      statusCode: 401,
    });
  });

  it('should respond with 400 when email is missing ', async () => {
    const { body } = await makeRequest(url);

    expect(body).toMatchObject({
      code: 'VALIDATION',
      message: 'body/email is not a valid email',
      statusCode: 400,
    });
  });

  it('should respond with 400 when email is not valid', async () => {
    const email = 'notValidEmail';

    const { body } = await makeRequest(url, email);

    expect(body).toMatchObject({
      code: 'VALIDATION',
      message: 'body/email is not a valid email',
      statusCode: 400,
    });
  });

  it('should respond with 500 when unexpected error occurs', async () => {
    const email = validEmail();
    const errorMessage = 'Failed to generate email verification token';

    vi.spyOn(emailVerificationRequestStore, 'add').mockRejectedValue(new Error(errorMessage));

    const { body, status } = await makeRequest(`${server.baseUrl}${path}`, email);

    expect(status).toBe(500);
    expect(body).toMatchObject({
      code: 'UNKNOWN_ERROR',
      message: errorMessage,
      statusCode: 500,
    });
  });

  it('should respond with 200 when email is valid', async () => {
    const email = validEmail();

    const { body, status } = await makeRequest(url, email);

    expect(status).toBe(200);
    expect(body).toHaveProperty('emailVerificationToken');
  });
});
