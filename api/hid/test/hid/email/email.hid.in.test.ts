import { afterAll, beforeAll, describe, expect, it, Mock, vi } from 'vitest';

vi.mock('haven-hid-pgsql-adapter/adapter.js');

import { PgHIDForEmailPlotAndSeaWare } from 'haven-hid-pgsql-adapter/adapter.js';
import request from 'superagent';
import { nextHID } from 'haven-hid-domain/mock.js';
import { inMemoryHIDDomain, tx, ValidEmail } from 'haven-hid-domain';
import { URI as path } from '../../../src/hid/email/email.hid.in.js';
import { validEmail } from '../../mock/email.mock.js';
import { Server } from '../../mock/server.js';
import { X_APP_ID_HEADER, X_APP_KEY_HEADER } from '../../../src/hid/header.js';

const mockPersistence = inMemoryHIDDomain().hidForEmailPlotAndSeaWare;
(PgHIDForEmailPlotAndSeaWare as Mock).mockImplementation(() => mockPersistence);

const TEST_APP_ID = 'TEST_APP';

const server = new Server();
let url: string;

const getInfoForHID = async (hid: string, appId: string | undefined): Promise<request.Response> => {
  const post = request.post(url).ok((_) => true);
  if (appId !== undefined) post.set(X_APP_ID_HEADER, appId).set(X_APP_KEY_HEADER, 'MY-KEY');

  return post.send({ hid: hid });
};

describe('get email for hid endpoint', () => {
  beforeAll(async () => {
    await server.start();
    url = `${server.baseUrl}${path}`;
  });

  afterAll(async () => {
    await server.stop();
  });

  describe('email requests', () => {
    it('unknown hid returns 400 (BAD_REQUEST)', async () => {
      const validHID = nextHID();
      const response = await getInfoForHID(validHID, TEST_APP_ID);

      expect(response).toBeBadRequest();
    });

    it('known HID retrieves existing info (EMAIL_RETRIEVED | OK)', async () => {
      const email = validEmail();
      const hid = await mockPersistence.getHIDForEmail(
        ValidEmail.newFromSafeInput(email),
        undefined,
        tx('test'),
      );

      const response = await getInfoForHID(hid, TEST_APP_ID);

      expect(response).toBeOK();

      const cleanEmail = ValidEmail.newFromSafeInput(email).email;
      expect(response.body).toEqual({
        email: cleanEmail,
      });
    });
  });

  describe('error handling', () => {
    it('invalid hid (INVALID_HID_PROVIDED | BAD_REQUEST)', async () => {
      const response = await getInfoForHID('A--', TEST_APP_ID);

      expect(response.body).toEqual({
        code: 'VALIDATION',
        message: 'body/hid must match pattern "[A-Z0-9]{12}"',
        statusCode: 400,
      });
      expect(response).toBeBadRequest();
    });

    it('missing hid (NO_HID_PROVIDED | BAD_REQUEST)', async () => {
      const response = await getInfoForHID('', TEST_APP_ID);

      expect(response.body).toEqual({
        code: 'VALIDATION',
        message: 'body/hid must match pattern "[A-Z0-9]{12}"',
        statusCode: 400,
      });
      expect(response).toBeBadRequest();
    });
  });

  describe('unsupported methods', () => {
    it.each(['GET', 'PUT', 'DELETE', 'OPTIONS', 'TRACE', 'PATCH'])(
      `unsupported method [%s] (NOT_FOUND)`,
      async (method: string) => {
        const response = await request(method, url)
          .ok((_) => true)
          .send();
        expect(response).toBeNotFound();
        expect(response.body).toEqual({
          code: 'NOT_FOUND',
          message: `Route ${method}:/email/hid not found`,
          statusCode: 404,
        });
      },
    );
  });

  describe('app-id check', () => {
    it('no APP ID (NO_APP_ID | UNAUTHORIZED)', async () => {
      const response = await getInfoForHID(validEmail(), undefined);
      expect(response).toBeUnauthorised();
      expect(response.body).toEqual({
        code: 'FORBIDDEN',
        message: 'x-app-id header is missing',
        statusCode: 403,
      });
    });

    it('empty APP ID (NO_APP_ID | UNAUTHORIZED)', async () => {
      const response = await getInfoForHID(validEmail(), '');
      expect(response).toBeUnauthorised();
      expect(response.body).toEqual({
        code: 'FORBIDDEN',
        message: 'x-app-id header is missing',
        statusCode: 403,
      });
    });
  });
});
