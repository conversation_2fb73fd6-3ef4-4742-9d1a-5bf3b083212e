import { afterAll, beforeAll, describe, expect, it, Mock, vi } from 'vitest';
import request from 'superagent';
import { PgHIDForEmailPlotAndSeaWare } from 'haven-hid-pgsql-adapter/adapter.js';
import { Server } from '../../mock/server.js';
import { invalidEmail, validEmail } from '../../mock/email.mock.js';
import { DomainError, DomainErrorCode, inMemoryHIDDomain } from 'haven-hid-domain';
import { X_APP_ID_HEADER, X_APP_KEY_HEADER } from '../../../src/hid/header.js';
import { nextAppId } from '../../mock/appid.mock.js';

vi.mock('haven-hid-pgsql-adapter/adapter.js');

const server = new Server();
const path = '/hid/emails';
const url = `${server.baseUrl}${path}`;

const getHIDsForEmails = async (
  emails: string[],
  appId: string | undefined,
): Promise<request.Response> => {
  const post = request.post(url).ok((_) => true);
  if (appId !== undefined) post.set(X_APP_ID_HEADER, appId).set(X_APP_KEY_HEADER, 'MY-KEY');

  const data = emails.map((email) => {
    return { email: email };
  });
  return post.send(data);
};

const mockPersistence = inMemoryHIDDomain().hidForEmailPlotAndSeaWare;
(PgHIDForEmailPlotAndSeaWare as Mock).mockImplementation(() => mockPersistence);

describe('get hid for bulk email endpoint', () => {
  beforeAll(async () => {
    await server.start();
  });

  afterAll(async () => {
    await server.stop();
  });

  describe('Rate limiting is based on x-app-id', () => {
    it('rate limiting applies to requests from same app', async () => {
      const email = validEmail();
      const xAppId = nextAppId();
      const firstCall = await getHIDsForEmails([email], xAppId);
      const secondCall = await getHIDsForEmails([email], xAppId);
      expect(firstCall.statusCode).toBe(200);
      expect(secondCall.statusCode).toBe(429);
    });

    it('rate limiting does not apply to requests from different apps', async () => {
      const email = validEmail();
      const firstCall = await getHIDsForEmails([email], nextAppId());
      const secondCall = await getHIDsForEmails([email], nextAppId());
      expect(firstCall.statusCode).toBe(200);
      expect(secondCall.statusCode).toBe(200);
    });
  });

  describe('hids for emails requests', () => {
    it('unknown emails generate new HIDs', async () => {
      const email1 = validEmail();
      const email2 = validEmail();
      const response = await getHIDsForEmails([email1, email2], nextAppId());
      expect(response).toBeOK();
      expect(response.body).toMatchObject({
        success: [
          {
            email: email1,
            hid: expect.anything(),
          },
          {
            email: email2,
            hid: expect.anything(),
          },
        ],
        failure: [],
      });
    });

    it('known email retrieves existing HID (HID_RETRIEVED | OK)', async () => {
      const email1 = validEmail();
      const email2 = validEmail();
      const setup = await getHIDsForEmails([email1], nextAppId());
      expect(setup).toBeOK();

      const response = await getHIDsForEmails([email1, email2], nextAppId());
      expect(response).toBeOK();

      expect(response.body.success.length).toEqual(2);
      expect(response.body.success[0]).toEqual(setup.body.success[0]);
      expect(response.body.success[1]).toEqual({
        email: email2,
        hid: expect.any(String),
      });
    });

    it('HID generation is not case sensitive (HID_GENERATED | OK)', async () => {
      const email = validEmail();
      const uppercaseEmail = email.toLocaleUpperCase();
      const lowercaseEmail = email.toLocaleLowerCase();

      const setup = await getHIDsForEmails([email], nextAppId());
      const uppercase = await getHIDsForEmails([uppercaseEmail], nextAppId());
      const lowercase = await getHIDsForEmails([lowercaseEmail], nextAppId());

      expect(setup).toBeOK();

      const { hid } = setup.body.success[0];
      const { hid: upperHid } = uppercase.body.success[0];
      const { hid: lowerHid } = lowercase.body.success[0];

      expect(hid).toEqual(upperHid);
      expect(hid).toEqual(lowerHid);
    });

    it('invalid email (INVALID_EMAIL_PROVIDED | BAD_REQUEST)', async () => {
      const response = await getHIDsForEmails([validEmail(), invalidEmail()], nextAppId());

      expect(response).toBeOK();
      expect(response.body.failure).toEqual([
        {
          email: invalidEmail(),
          code: 'VALIDATION',
          message: 'body/email is not a valid email',
        },
      ]);
    });

    it('duplicate emails are ignored', async () => {
      const email1 = validEmail();
      const email2 = validEmail();
      const response = await getHIDsForEmails([email1, email2, email1], nextAppId());
      expect(response).toBeOK();
      expect(response.body).toMatchObject({
        success: [
          {
            email: email1,
            hid: expect.anything(),
          },
          {
            email: email2,
            hid: expect.anything(),
          },
        ],
        failure: [],
      });
    });

    it('too large batch generates BAD_REQUEST)', async () => {
      const emails = Array.from({ length: 1001 }, (_, i) => validEmail());
      const response = await getHIDsForEmails(emails, nextAppId());

      expect(response).toBeBadRequest();
      expect(response.body).toEqual({
        code: 'VALIDATION',
        message: 'Maximum batch size (1000) exceeded',
        statusCode: 400,
      });
    });
  });

  describe('error handling', () => {
    it('runtime error returns failure entries', async () => {
      const spy = vi.spyOn(mockPersistence, 'getHIDForEmail');
      spy.mockRejectedValue(new DomainError(DomainErrorCode.SERVICE_ERROR, 'Bang'));
      const email = validEmail();
      const response = await getHIDsForEmails([email], nextAppId());

      expect(response).toBeOK();
      expect(response.body.success).toEqual([]);
      expect(response.body.failure).toEqual([
        {
          email: email,
          code: 'SERVICE_ERROR',
          message: 'Bang',
        },
      ]);
    });
  });

  describe('unsupported methods', () => {
    it.each(['GET', 'PUT', 'DELETE', 'OPTIONS', 'TRACE', 'PATCH'])(
      `unsupported method [%s] (NOT_FOUND)`,
      async (method: string) => {
        const response = await request(method, url)
          .ok((_) => true)
          .send();
        expect(response).toBeNotFound();
        expect(response.body).toEqual({
          code: 'NOT_FOUND',
          message: `Route ${method}:/hid/emails not found`,
          statusCode: 404,
        });
      },
    );
  });

  describe('app-id check', () => {
    it('no APP ID (NO_APP_ID | UNAUTHORIZED)', async () => {
      const response = await getHIDsForEmails(['xxx'], undefined);
      expect(response).toBeUnauthorised();
      expect(response.body).toEqual({
        code: 'FORBIDDEN',
        message: 'x-app-id header is missing',
        statusCode: 403,
      });
    });

    it('empty APP ID (NO_APP_ID | UNAUTHORIZED)', async () => {
      const response = await getHIDsForEmails([validEmail()], '');
      expect(response).toBeUnauthorised();
      expect(response.body).toEqual({
        code: 'FORBIDDEN',
        message: 'x-app-id header is missing',
        statusCode: 403,
      });
    });
  });
});
