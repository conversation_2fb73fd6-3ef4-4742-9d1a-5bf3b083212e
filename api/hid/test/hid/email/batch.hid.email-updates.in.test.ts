import { afterAll, beforeAll, describe, expect, it, Mock, vi } from 'vitest';
import request from 'superagent';
import {
  PgHavenIdentityTransactionalStore,
  PgHIDForEmailPlotAndSeaWare,
} from 'haven-hid-pgsql-adapter/adapter.js';
import { nextValidEmail, nextTx } from 'haven-hid-domain/mock.js';
import { Server } from '../../mock/server.js';
import { invalidEmail, validEmail } from '../../mock/email.mock.js';
import {
  DomainError,
  DomainErrorCode,
  HavenServiceAccountIdType as IdType,
  HID,
  inMemoryHIDDomain,
  ValidEmail,
  HavenIdentity,
  HavenServiceAccount,
  HavenServiceAccountIdType,
} from 'haven-hid-domain';
import { X_APP_ID_HEADER, X_APP_KEY_HEADER } from '../../../src/hid/header.js';
import * as emailUpdates from '../../../src/hid/email/batch.hid.email-updates.in.js';
import { addMinutes } from 'date-fns/addMinutes';
import casual from 'casual';
import { nextAppId } from '../../mock/appid.mock.js';
import { beforeEach } from 'node:test';

vi.mock('haven-hid-pgsql-adapter/adapter.js');

const server = new Server();
const url: string = `${server.baseUrl}/hid/account/email-updates`;

const getHIDsForEmailUpdates = async (
  updates: emailUpdates.EmailUpdate[],
  appId: string | undefined,
): Promise<request.Response> => {
  const post = request.post(url).ok((_) => true);
  if (appId !== undefined) post.set(X_APP_ID_HEADER, appId).set(X_APP_KEY_HEADER, 'MY-KEY');

  return post.send(updates);
};

const getHIDForEmail = async (email: string): Promise<HID> => {
  const response = await request
    .post(`${server.baseUrl}/hid/email`)
    .ok((_) => true)
    .set(X_APP_ID_HEADER, nextAppId())
    .set(X_APP_KEY_HEADER, 'MY-KEY')
    .send({ email: email });
  if (!response.body.hid) throw new Error(`Unexpected response to find hid ${response.body}`);

  return response.body.hid;
};

const mockPersistence = inMemoryHIDDomain().hidForEmailPlotAndSeaWare;
const mockCustomerIdentityPersistence = inMemoryHIDDomain().identityStore;
(PgHIDForEmailPlotAndSeaWare as Mock).mockImplementation(() => mockPersistence);
(PgHavenIdentityTransactionalStore as Mock).mockImplementation(
  () => mockCustomerIdentityPersistence,
);

describe('get hid for bulk email endpoint', () => {
  beforeAll(async () => {
    await server.start();
  });

  afterAll(async () => {
    await server.stop();
  });

  const nextId = () => {
    return casual.integer(5000, 10000000);
  };

  const update = (
    email: string,
    idType: IdType,
    id: number = nextId(),
    updatedAt: string = new Date().toISOString(),
  ): emailUpdates.EmailUpdate => ({
    email,
    idType,
    id,
    updatedAt,
  });

  describe('Rate limiting is based on x-app-id', () => {
    it('rate limiting applies to requests from same app', async () => {
      const email = validEmail();
      const id = nextId();
      const xAppId = nextAppId();
      const firstCall = await getHIDsForEmailUpdates([update(email, IdType.OWNER_ID, id)], xAppId);
      const secondCall = await getHIDsForEmailUpdates([update(email, IdType.OWNER_ID, id)], xAppId);
      expect(firstCall.statusCode).toBe(200);
      expect(secondCall.statusCode).toBe(429);
    });

    it('rate limiting does not apply to requests from different apps', async () => {
      const email = validEmail();
      const id = nextId();
      const firstCall = await getHIDsForEmailUpdates(
        [update(email, IdType.OWNER_ID, id)],
        nextAppId(),
      );
      const secondCall = await getHIDsForEmailUpdates(
        [update(email, IdType.OWNER_ID, id)],
        nextAppId(),
      );
      expect(firstCall.statusCode).toBe(200);
      expect(secondCall.statusCode).toBe(200);
    });
  });

  describe.each([IdType.SEAWARE_CLIENT_ID, IdType.OWNER_ID])(
    'email updates for %s',
    (type: IdType) => {
      beforeEach(() => {
        vi.clearAllMocks();
      }),
        it('unknown emails generate new HIDs', async () => {
          const email1 = validEmail();
          const email2 = validEmail();
          const response = await getHIDsForEmailUpdates(
            [update(email1, type, nextId()), update(email2, type, nextId())],
            nextAppId(),
          );
          expect(response).toBeOK();
          expect(response.body).toMatchObject({
            success: [
              {
                email: email1,
                hid: expect.anything(),
              },
              {
                email: email2,
                hid: expect.anything(),
              },
            ],
            failure: [],
          });
        });

      it('known email retrieves existing HID (HID_RETRIEVED | OK)', async () => {
        const email1 = validEmail();
        const email2 = validEmail();
        const id = nextId();
        const seawareId = nextId();
        const setup = await getHIDsForEmailUpdates([update(email1, type, id)], nextAppId());
        expect(setup).toBeOK();

        const response = await getHIDsForEmailUpdates(
          [update(email1, type, id), update(email2, type, seawareId)],
          nextAppId(),
        );

        expect(response).toBeOK();

        expect(response.body.success.length).toEqual(2);
        expect(response.body.success[0]).toEqual(setup.body.success[0]);
        expect(response.body.success[1]).toEqual({
          email: email2,
          hid: expect.any(String),
        });
      });

      it('email updates for account without existing haven identity', async () => {
        const originalEmail = validEmail();
        const id = nextId();
        const setup = await getHIDsForEmailUpdates([update(originalEmail, type, id)], nextAppId());
        expect(setup).toBeOK();
        expect(setup.body.success.length).toEqual(1);
        const hid = setup.body.success[0].hid;

        const updatedEmail = validEmail();
        const updated = await getHIDsForEmailUpdates([update(updatedEmail, type, id)], nextAppId());

        expect(updated).toBeOK();

        expect(updated.body.success.length).toEqual(1);
        expect(updated.body.success[0]).toEqual({
          email: updatedEmail,
          hid: hid,
        });
      });

      it('returns hid if updated email and account id match migrated user', async () => {
        const validEmail = nextValidEmail();
        const id = nextId();
        const migratedCustomerIdentity = await setupCustomerIdentity(validEmail, [{ type, id }]);
        const generateOrRetrieveSpy = vi.spyOn(emailUpdates, 'generateOrRetrieve');

        const updated = await getHIDsForEmailUpdates(
          [update(validEmail.email, type, id)],
          nextAppId(),
        );

        expect(generateOrRetrieveSpy).not.toHaveBeenCalled();
        expect(updated).toBeOK();

        expect(updated.body.success.length).toEqual(1);
        expect(updated.body.success[0]).toEqual({
          email: validEmail.email,
          hid: migratedCustomerIdentity.hid,
        });
      });

      it('returns error if updated email and account id match different migrated user', async () => {
        const validEmail = nextValidEmail();
        const id = nextId();
        const identity = await setupCustomerIdentity(validEmail, [{ type, id }]);
        const generateOrRetrieveSpy = vi.spyOn(emailUpdates, 'generateOrRetrieve');

        const updated = await getHIDsForEmailUpdates(
          [update(validEmail.email, type, nextId())],
          nextAppId(),
        );

        expect(generateOrRetrieveSpy).not.toHaveBeenCalled();
        expect(updated).toBeOK();

        expect(updated.body.success.length).toEqual(0);
        expect(updated.body.failure.length).toEqual(1);
        expect(updated.body.failure[0]).toEqual({
          email: validEmail.email,
          code: 'IDENTITY_CONFLICT',
          message: `Email is registered against identity ${identity.hid} and already has a different ${type === HavenServiceAccountIdType.SEAWARE_CLIENT_ID ? 'clientid' : 'owner'} ${id}`,
        });
      });

      it('allows update to identity when it has no linked accounts', async () => {
        const validEmail = nextValidEmail();
        const id = nextId();
        await setupCustomerIdentity(validEmail, []);
        const generateOrRetrieveSpy = vi.spyOn(emailUpdates, 'generateOrRetrieve');

        const updated = await getHIDsForEmailUpdates(
          [update(validEmail.email, type, nextId())],
          nextAppId(),
        );

        expect(generateOrRetrieveSpy).not.toHaveBeenCalled();
        expect(updated).toBeOK();

        expect(updated.body.success.length).toEqual(1);
        expect(updated.body.failure.length).toEqual(0);
      });

      it('HID generation is not case sensitive (HID_GENERATED | OK)', async () => {
        const email = validEmail();
        const uppercaseEmail = email.toLocaleUpperCase();
        const lowercaseEmail = email.toLocaleLowerCase();

        const request: emailUpdates.EmailUpdate = update(email, type);
        const setup = await getHIDsForEmailUpdates([request], nextAppId());
        const uppercase = await getHIDsForEmailUpdates(
          [{ ...request, email: uppercaseEmail }],
          nextAppId(),
        );
        const lowercase = await getHIDsForEmailUpdates(
          [{ ...request, email: lowercaseEmail }],
          nextAppId(),
        );

        expect(setup).toBeOK();

        const { hid } = setup.body.success[0];
        const { hid: upperHid } = uppercase.body.success[0];
        const { hid: lowerHid } = lowercase.body.success[0];

        expect(hid).toEqual(upperHid);
        expect(hid).toEqual(lowerHid);
      });

      it('invalid email (INVALID_EMAIL_PROVIDED | BAD_REQUEST)', async () => {
        const valid: emailUpdates.EmailUpdate = update(validEmail(), type, nextId());
        const invalid: emailUpdates.EmailUpdate = update(invalidEmail(), type, nextId());
        const response = await getHIDsForEmailUpdates([valid, invalid], nextAppId());

        expect(response).toBeOK();
        expect(response.body.failure).toEqual([
          {
            email: invalidEmail(),
            code: 'VALIDATION',
            message: 'body/email is not a valid email',
          },
        ]);
      });

      it('duplicate requests are ignored', async () => {
        const request1: emailUpdates.EmailUpdate = update(validEmail(), type, 101);
        const request2: emailUpdates.EmailUpdate = update(validEmail(), type, 102);

        const response = await getHIDsForEmailUpdates([request1, request2, request1], nextAppId());
        expect(response).toBeOK();
        expect(response.body).toMatchObject({
          success: [
            {
              email: request1.email,
              hid: expect.anything(),
            },
            {
              email: request2.email,
              hid: expect.anything(),
            },
          ],
          failure: [],
        });
      });

      it('known update retains existing hid', async () => {
        const now = new Date();
        const beforeNow = addMinutes(now, -1);
        const email = validEmail();
        const id = nextId();
        const original = await getHIDsForEmailUpdates(
          [update(email, type, id, now.toISOString())],
          nextAppId(),
        );
        expect(original).toBeOK();

        const response = await getHIDsForEmailUpdates(
          [update(email, type, id, beforeNow.toISOString())],
          nextAppId(),
        );

        expect(response).toBeOK();

        expect(response.body.success.length).toEqual(1);
        expect(response.body.success[0]).toEqual(original.body.success[0]);

        expect(await getHIDForEmail(email)).toEqual(original.body.success[0].hid);
      });

      it('new update retains existing hid but updates email', async () => {
        const now = new Date();
        const later = addMinutes(now, 2);
        const originalEmail = validEmail();
        const id = nextId();
        const original = await getHIDsForEmailUpdates(
          [update(originalEmail, type, id, now.toISOString())],
          nextAppId(),
        );
        expect(original).toBeOK();

        const updatedEmail = validEmail();
        const response = await getHIDsForEmailUpdates(
          [update(updatedEmail, type, id, later.toISOString())],
          nextAppId(),
        );

        expect(response).toBeOK();

        expect(response.body.success.length).toEqual(1);
        expect(response.body.success[0]).toEqual({
          email: updatedEmail,
          hid: original.body.success[0].hid,
        });
        expect(await getHIDForEmail(updatedEmail)).toEqual(original.body.success[0].hid);
      });

      it('historic update which has been superseded returns error', async () => {
        const id = nextId();
        const currentEmail = validEmail();
        const now = new Date();
        const staleEmail = validEmail();
        const staleUpdatedAt = addMinutes(now, -10);

        const current = await getHIDsForEmailUpdates(
          [update(currentEmail, type, id, now.toISOString())],
          nextAppId(),
        );
        expect(current).toBeOK();

        const response = await getHIDsForEmailUpdates(
          [update(staleEmail, type, id, staleUpdatedAt.toISOString())],
          nextAppId(),
        );

        expect(response).toBeOK();

        expect(response.body.success.length).toEqual(0);
        expect(response.body.failure.length).toEqual(1);
        expect(response.body.failure[0]).toEqual({
          code: 'STALE_EMAIL_UPDATE',
          email: staleEmail,
          message: expect.stringMatching(/is behind latest/),
        });

        expect(await getHIDForEmail(currentEmail)).toEqual(current.body.success[0].hid);
      });
    },
  );

  describe('error handling', () => {
    it('too large batch generates BAD_REQUEST)', async () => {
      const emails: emailUpdates.EmailUpdate[] = Array.from(
        { length: 1001 },
        (_, i): emailUpdates.EmailUpdate => {
          return update(validEmail(), IdType.OWNER_ID, i);
        },
      );
      const response = await getHIDsForEmailUpdates(emails, nextAppId());

      expect(response).toBeBadRequest();
      expect(response.body).toEqual({
        code: 'VALIDATION',
        message: 'Maximum batch size (1000) exceeded',
        statusCode: 400,
      });
    });

    it('runtime error returns failure entries', async () => {
      const spy = vi.spyOn(mockPersistence, 'getHIDForEmailAndPlot');
      spy.mockRejectedValueOnce(new DomainError(DomainErrorCode.SERVICE_ERROR, 'Bang'));
      const request: emailUpdates.EmailUpdate = update(validEmail(), IdType.OWNER_ID);
      const response = await getHIDsForEmailUpdates([request], nextAppId());

      expect(response).toBeOK();
      expect(response.body.success).toEqual([]);
      expect(response.body.failure).toEqual([
        {
          email: request.email,
          code: 'SERVICE_ERROR',
          message: 'Bang',
        },
      ]);
    });
  });

  describe('unsupported methods', () => {
    it.each(['GET', 'PUT', 'DELETE', 'OPTIONS', 'TRACE', 'PATCH'])(
      `unsupported method [%s] (NOT_FOUND)`,
      async (method: string) => {
        const response = await request(method, url)
          .ok((_) => true)
          .send();
        expect(response).toBeNotFound();
        expect(response.body).toEqual({
          code: 'NOT_FOUND',
          message: `Route ${method}:/hid/account/email-updates not found`,
          statusCode: 404,
        });
      },
    );
  });

  describe('app-id check', () => {
    it('no APP ID (NO_APP_ID | UNAUTHORIZED)', async () => {
      const request: emailUpdates.EmailUpdate = update(validEmail(), IdType.OWNER_ID);
      const response = await getHIDsForEmailUpdates([request], undefined);
      expect(response).toBeUnauthorised();
      expect(response.body).toEqual({
        code: 'FORBIDDEN',
        message: 'x-app-id header is missing',
        statusCode: 403,
      });
    });

    it('empty APP ID (NO_APP_ID | UNAUTHORIZED)', async () => {
      const request: emailUpdates.EmailUpdate = update(validEmail(), IdType.OWNER_ID);
      const response = await getHIDsForEmailUpdates([request], '');
      expect(response).toBeUnauthorised();
      expect(response.body).toEqual({
        code: 'FORBIDDEN',
        message: 'x-app-id header is missing',
        statusCode: 403,
      });
    });
  });
});

const setupCustomerIdentity = async (
  email: ValidEmail,
  accounts: HavenServiceAccount[],
): Promise<HavenIdentity> => {
  const customerIdentity = await inMemoryHIDDomain().identityStore.createIdentity(
    {
      name: casual.name,
      title: casual.title,
      firstName: casual.first_name,
      lastName: casual.last_name,
      email,
      emailVerified: false,
      accounts,
    },
    nextTx(),
  );
  return customerIdentity;
};
