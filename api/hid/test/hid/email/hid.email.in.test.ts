import { afterAll, beforeAll, describe, expect, it, Mock, vi } from 'vitest';
import { DomainError, DomainErrorCode, inMemoryHIDDomain } from 'haven-hid-domain';
import { nextPlotOwnerId, nextSeaWareClientId } from 'haven-hid-domain/mock.js';
import { PgHIDForEmailPlotAndSeaWare } from 'haven-hid-pgsql-adapter/adapter.js';
import request from 'superagent';
import { URI as path } from '../../../src/hid/email/hid.email.in.js';
import { invalidEmail, validEmail } from '../../mock/email.mock.js';
import { Server } from '../../mock/server.js';
import { X_APP_ID_HEADER, X_APP_KEY_HEADER } from '../../../src/hid/header.js';
import { nextAppId } from '../../mock/appid.mock.js';

vi.mock('haven-hid-pgsql-adapter/adapter.js');
vi.mock('../../src/config/transaction-config.js');

const mockPersistence = inMemoryHIDDomain().hidForEmailPlotAndSeaWare;
(PgHIDForEmailPlotAndSeaWare as Mock).mockImplementation(() => mockPersistence);

const server = new Server().withRateLimit({
  max: 5,
  timeWindow: '1 second',
});
let url: string;

const getHIDForEmail = async (
  email: string,
  appId: string | undefined,
): Promise<request.Response> => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const post = request.post(url).ok((_) => true);
  if (appId !== undefined) post.set(X_APP_ID_HEADER, appId).set(X_APP_KEY_HEADER, 'MY-KEY');

  return post.send({ email: email });
};

const getHIDForEmailAndId = async (
  email: string,
  type: 'owner_id' | 'seaware_client_id' | undefined,
  id: number | undefined,
  appId: string | undefined,
): Promise<request.Response> => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const post = request.post(url).ok((_) => true);
  if (appId !== undefined) post.set('X-APP-ID', appId).set(X_APP_KEY_HEADER, 'MY-KEY');

  return post.send({
    email: email,
    idType: type,
    id: id,
  });
};

describe('get hid for email endpoint', () => {
  beforeAll(async () => {
    await server.start();
    url = `${server.baseUrl}${path}`;
  });

  afterAll(async () => {
    await server.stop();
  });

  describe('email only requests', () => {
    it('unknown email generates new HID (HID_GENERATED | OK)', async () => {
      const response = await getHIDForEmail(validEmail(), nextAppId());

      expect(response).toBeOK();
      expect(response).toBeValidHIDResponse();
    });

    it('known email retrieves existing HID (HID_RETRIEVED | OK)', async () => {
      const email = validEmail();
      const setup = await getHIDForEmail(email, nextAppId());
      const response = await getHIDForEmail(email, nextAppId());

      expect(response).toBeOK();
      expect(response).toBeValidHIDResponse();

      expect(setup.text).toBe(response.text);
    });

    it('HID generation is not case sensitive (HID_GENERATED | OK)', async () => {
      const email = validEmail();
      const uppercaseEmail = email.toLocaleUpperCase();
      const lowercaseEmail = email.toLocaleLowerCase();

      const setup = await getHIDForEmail(email, nextAppId());
      const uppercase = await getHIDForEmail(uppercaseEmail, nextAppId());
      const lowercase = await getHIDForEmail(lowercaseEmail, nextAppId());

      expect(setup.body.hid).toBe(uppercase.body.hid);
      expect(setup.body.hid).toBe(lowercase.body.hid);
    });

    it('HID generation seems somewhat unique (HID_GENERATED | OK)', async () => {
      const hids: string[] = [];

      for (let index = 0; index < 50; index++) {
        const response = await getHIDForEmail(validEmail(), nextAppId());
        hids.push(response.body.hid);
      }

      expect(hids.length).toBe(new Set(hids).size);
    });

    it('invalid email (INVALID_EMAIL_PROVIDED | BAD_REQUEST)', async () => {
      const response = await getHIDForEmail(invalidEmail(), nextAppId());

      expect(response.body).toEqual({
        code: 'VALIDATION',
        message: 'body/email is not a valid email',
        statusCode: 400,
      });
      expect(response).toBeBadRequest();
    });

    it('missing email (NO_EMAIL_PROVIDED | BAD_REQUEST)', async () => {
      const response = await getHIDForEmail('', nextAppId());

      expect(response.body).toEqual({
        code: 'VALIDATION',
        message: 'body/email is not a valid email',
        statusCode: 400,
      });
      expect(response).toBeBadRequest();
    });
  });

  describe('error handling', () => {
    it('invalid idtype generates validation error (VALIDATION | BAD REQUEST)', async () => {
      const clientId = nextSeaWareClientId();

      const response = await getHIDForEmailAndId(
        validEmail(),
        'xxxx' as never,
        clientId,
        nextAppId(),
      );

      expect(response).toBeBadRequest();
      expect(response.body).toEqual({
        code: 'VALIDATION',
        message: 'body/idType must be equal to one of the allowed values',
        statusCode: 400,
      });
    });

    it('runtime error returns 500', async () => {
      const spy = vi.spyOn(mockPersistence, 'getHIDForEmail');
      spy.mockRejectedValueOnce(new DomainError(DomainErrorCode.SERVICE_ERROR, 'Bang'));
      const response = await getHIDForEmail(validEmail(), nextAppId());

      expect(response.body).toEqual({
        code: 'SERVICE_ERROR',
        message: 'Bang',
        statusCode: 500,
      });
    });
  });

  describe('email and client-id requests', () => {
    it('unknown email with clientid generates new HID (HID_GENERATED | OK)', async () => {
      const clientId = nextSeaWareClientId();

      const response = await getHIDForEmailAndId(
        validEmail(),
        'seaware_client_id',
        clientId,
        nextAppId(),
      );

      expect(response).toBeOK();
      expect(response).toBeValidHIDResponse();
    });

    it('missing client id (MISSING_ID | BAD_REQUEST)', async () => {
      const response = await getHIDForEmailAndId(
        validEmail(),
        'seaware_client_id',
        undefined,
        nextAppId(),
      );

      expect(response).toBeBadRequest();
      expect(response.body).toEqual({
        code: 'VALIDATION',
        message: 'Missing id for idType="seaware_client_id"',
        statusCode: 400,
      });
    });

    it('missing id type (MISSING_ID_TYPE | BAD_REQUEST)', async () => {
      const clientId = nextSeaWareClientId();
      const response = await getHIDForEmailAndId(validEmail(), undefined, clientId, nextAppId());

      expect(response.body).toEqual({
        code: 'VALIDATION',
        message: `Missing idType for id="${clientId}"`,
        statusCode: 400,
      });
      expect(response).toBeBadRequest();
    });

    it('existing guest id returns existing hid', async () => {
      const email = validEmail();
      const clientId = nextSeaWareClientId();
      const initialResponse = await getHIDForEmailAndId(
        email,
        'seaware_client_id',
        clientId,
        nextAppId(),
      );

      expect(initialResponse).toBeOK();
      expect(initialResponse).toBeValidHIDResponse();

      const response = await getHIDForEmailAndId(email, 'seaware_client_id', clientId, nextAppId());

      expect(response).toBeOK();
      expect(response).toBeValidHIDResponse();

      expect(response.body).toEqual(initialResponse.body);
    });

    it('existing guest id returns existing hid and updates email', async () => {
      const clientId = nextSeaWareClientId();
      const email = validEmail();
      const initialResponse = await getHIDForEmailAndId(
        email,
        'seaware_client_id',
        clientId,
        nextAppId(),
      );

      expect(initialResponse).toBeOK();
      expect(initialResponse).toBeValidHIDResponse();

      const otherEmail = validEmail();
      const response = await getHIDForEmailAndId(
        otherEmail,
        'seaware_client_id',
        clientId,
        nextAppId(),
      );

      expect(response).toBeOK();
      expect(response).toBeValidHIDResponse();

      expect(response.body).toEqual(initialResponse.body);

      const emailResponse = await getHIDForEmail(otherEmail, nextAppId());

      expect(emailResponse).toBeOK();
      expect(emailResponse).toBeValidHIDResponse();

      expect(emailResponse.body).toEqual(initialResponse.body);
    });
  });

  describe('email and owner-id requests', () => {
    it('unknown email with owner id generates new HID (HID_GENERATED | OK)', async () => {
      const clientId = nextSeaWareClientId();
      const response = await getHIDForEmailAndId(validEmail(), 'owner_id', clientId, nextAppId());

      expect(response).toBeOK();
      expect(response).toBeValidHIDResponse();
    });

    it('missing owner id (MISSING_ID | BAD_REQUEST)', async () => {
      const response = await getHIDForEmailAndId(validEmail(), 'owner_id', undefined, nextAppId());

      expect(response.body).toEqual({
        code: 'VALIDATION',
        message: 'Missing id for idType="owner_id"',
        statusCode: 400,
      });
      expect(response).toBeBadRequest();
    });

    it('existing owner id with new email returns existing hid and updates email', async () => {
      const ownerId = nextPlotOwnerId();
      const email = validEmail();
      const initialResponse = await getHIDForEmailAndId(email, 'owner_id', ownerId, nextAppId());

      expect(initialResponse).toBeOK();
      expect(initialResponse).toBeValidHIDResponse();

      const otherEmail = validEmail();
      const response = await getHIDForEmailAndId(otherEmail, 'owner_id', ownerId, nextAppId());

      expect(response).toBeOK();
      expect(response).toBeValidHIDResponse();

      expect(response.body).toEqual(initialResponse.body);

      const emailResponse = await getHIDForEmail(otherEmail, nextAppId());

      expect(emailResponse).toBeOK();
      expect(emailResponse).toBeValidHIDResponse();

      expect(emailResponse.body).toEqual(initialResponse.body);
    });
  });

  describe('owner and guests with shared email', () => {
    it('unknown email with owner id and then seaware id returns same HID', async () => {
      const email = validEmail();
      const ownerId = nextPlotOwnerId();
      const clientId = nextSeaWareClientId();
      const hid = await getHIDForEmail(email, nextAppId());
      expect(hid).toBeOK();
      expect(hid).toBeValidHIDResponse();

      const owner = await getHIDForEmailAndId(email, 'owner_id', ownerId, nextAppId());
      expect(owner).toBeOK();
      expect(owner).toBeValidHIDResponse();
      expect(owner.body).toEqual(hid.body);

      const seaware = await getHIDForEmailAndId(email, 'seaware_client_id', clientId, nextAppId());
      expect(seaware).toBeOK();
      expect(seaware).toBeValidHIDResponse();
      expect(seaware.body).toEqual(hid.body);
    }, 10000000);
  });

  describe('unsupported methods', () => {
    it.each(['GET', 'PUT', 'DELETE', 'OPTIONS', 'TRACE', 'PATCH'])(
      `unsupported method [%s] (NOT_FOUND)`,
      async (method: string) => {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const response = await request(method, url)
          .ok((_) => true)
          .send();
        expect(response).toBeNotFound();
        expect(response.body).toEqual({
          code: 'NOT_FOUND',
          message: `Route ${method}:/hid/email not found`,
          statusCode: 404,
        });
      },
    );
  });

  describe('app-id check', () => {
    it('no APP ID (NO_APP_ID | UNAUTHORIZED)', async () => {
      const response = await getHIDForEmail('xxx', undefined);
      expect(response).toBeUnauthorised();
      expect(response.body).toEqual({
        code: 'FORBIDDEN',
        message: 'x-app-id header is missing',
        statusCode: 403,
      });
    });

    it('empty APP ID (NO_APP_ID | UNAUTHORIZED)', async () => {
      const response = await getHIDForEmail(validEmail(), '');
      expect(response).toBeUnauthorised();
      expect(response.body).toEqual({
        code: 'FORBIDDEN',
        message: 'x-app-id header is missing',
        statusCode: 403,
      });
    });
  });

  describe('Rate limiting is based on x-app-id', () => {
    it('rate limiting applies to requests from same app', async () => {
      const email = validEmail();

      const appId = nextAppId();
      expect((await getHIDForEmail(email, appId)).statusCode).toBe(200);
      expect((await getHIDForEmail(email, appId)).statusCode).toBe(200);
      expect((await getHIDForEmail(email, appId)).statusCode).toBe(200);
      expect((await getHIDForEmail(email, appId)).statusCode).toBe(200);
      expect((await getHIDForEmail(email, appId)).statusCode).toBe(200);
      expect((await getHIDForEmail(email, appId)).statusCode).toBe(429);
    });

    it('rate limiting does not apply to requests from different apps', async () => {
      const email = validEmail();

      expect((await getHIDForEmail(email, nextAppId())).statusCode).toBe(200);
      expect((await getHIDForEmail(email, nextAppId())).statusCode).toBe(200);
      expect((await getHIDForEmail(email, nextAppId())).statusCode).toBe(200);
      expect((await getHIDForEmail(email, nextAppId())).statusCode).toBe(200);
      expect((await getHIDForEmail(email, nextAppId())).statusCode).toBe(200);
      expect((await getHIDForEmail(email, nextAppId())).statusCode).toBe(200);
    });
  });
});
