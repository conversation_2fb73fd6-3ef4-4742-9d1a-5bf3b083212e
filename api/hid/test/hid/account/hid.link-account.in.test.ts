import { vi, Mock, afterAll, beforeAll, describe, expect, it, beforeEach } from 'vitest';
import request from 'superagent';
import { URI as path } from '../../../src/hid/account/hid.link-account.in.js';
import { Server } from '../../mock/server.js';
import { X_APP_ID_HEADER, X_APP_KEY_HEADER } from '../../../src/hid/header.js';
import { HavenServiceAccountIdType as IdType, inMemoryHIDDomain } from 'haven-hid-domain';
import { nextPlotOwnerId, nextSeaWareClientId, nextHID } from 'haven-hid-domain/mock.js';
import { PgHIDForEmailPlotAndSeaWare } from 'haven-hid-pgsql-adapter/adapter.js';

vi.mock('haven-hid-pgsql-adapter/adapter.js');

const mockPersistence = inMemoryHIDDomain().hidForEmailPlotAndSeaWare;
(PgHIDForEmailPlotAndSeaWare as Mock).mockImplementation(() => mockPersistence);

const TEST_APP_ID = 'TEST_APP';
const server = new Server();
let url: string;

type linkAccountBody = {
  hid: string;
  id: number;
  idType: IdType;
};

const linkAccount = async (body: linkAccountBody, appId?: string): Promise<request.Response> => {
  const r = request.post(url).ok((_) => true);
  if (appId !== undefined) r.set(X_APP_ID_HEADER, appId).set(X_APP_KEY_HEADER, 'MANAGEMENT-KEY');

  return r.send(body);
};

describe('link service ID to account', () => {
  beforeAll(async () => {
    await server.start();
    url = `${server.baseUrl}${path}`;
  });

  afterAll(async () => {
    await server.stop();
  });

  describe('handle link request', () => {
    it('link account for plot', async () => {
      const hid = nextHID();
      const response = await linkAccount(
        {
          hid,
          id: nextPlotOwnerId(),
          idType: IdType.OWNER_ID,
        },
        TEST_APP_ID,
      );

      expect(response.body).toEqual({
        hid,
      });
    });

    it('link account for seaware', async () => {
      const hid = nextHID();
      const response = await linkAccount(
        {
          hid,
          id: nextSeaWareClientId(),
          idType: IdType.SEAWARE_CLIENT_ID,
        },
        TEST_APP_ID,
      );

      expect(response.body).toEqual({
        hid,
      });
    });
  });

  describe('error handling', () => {
    it('invalid hid (INVALID_HID_PROVIDED | BAD_REQUEST)', async () => {
      const response = await linkAccount(
        {
          hid: 'invalid',
          id: 123,
          idType: IdType.OWNER_ID,
        },
        TEST_APP_ID,
      );

      expect(response.body).toEqual({
        code: 'VALIDATION',
        message: 'body/hid must match pattern "[A-Z0-9]{12}"',
        statusCode: 400,
      });
      expect(response).toBeBadRequest();
    });

    it('missing hid (NO_HID_PROVIDED | BAD_REQUEST)', async () => {
      const response = await linkAccount(
        {
          id: 123,
          idType: IdType.OWNER_ID,
        } as never,
        TEST_APP_ID,
      );

      expect(response.body).toEqual({
        code: 'VALIDATION',
        message: "body must have required property 'hid'",
        statusCode: 400,
      });
      expect(response).toBeBadRequest();
    });

    it('invalid id (INVALID_ID_PROVIDED | BAD REQUEST)', async () => {
      const response = await linkAccount(
        {
          hid: nextHID(),
          id: 'invalid',
          idType: IdType.OWNER_ID,
        } as never,
        TEST_APP_ID,
      );

      expect(response).toBeBadRequest();
      expect(response.body).toEqual({
        code: 'VALIDATION',
        message: 'body/id must be number',
        statusCode: 400,
      });
    });

    it('missing id (NO_ID_PROVIDED | BAD REQUEST)', async () => {
      const response = await linkAccount(
        {
          hid: nextHID(),
          idType: IdType.OWNER_ID,
        } as never,
        TEST_APP_ID,
      );

      expect(response).toBeBadRequest();
      expect(response.body).toEqual({
        code: 'VALIDATION',
        message: "body must have required property 'id'",
        statusCode: 400,
      });
    });

    it('missing idtype (NO_ID_TYPE_PROVIDED | BAD REQUEST)', async () => {
      const response = await linkAccount(
        {
          hid: nextHID(),
          id: 123,
        } as never,
        TEST_APP_ID,
      );

      expect(response).toBeBadRequest();
      expect(response.body).toEqual({
        code: 'VALIDATION',
        message: "body must have required property 'idType'",
        statusCode: 400,
      });
    });

    it('invalid idtype generates validation error (VALIDATION | BAD REQUEST)', async () => {
      const response = await linkAccount(
        {
          hid: nextHID(),
          id: 123,
          idType: 'invalid' as never,
        },
        TEST_APP_ID,
      );

      expect(response).toBeBadRequest();
      expect(response.body).toEqual({
        code: 'VALIDATION',
        message: 'body/idType must be equal to one of the allowed values',
        statusCode: 400,
      });
    });

    it('runtime error returns 500', async () => {
      const spy = vi.spyOn(mockPersistence, 'linkHIDToPlot');
      spy.mockImplementationOnce(() => {
        throw new Error();
      });

      const response = await linkAccount(
        {
          hid: nextHID(),
          id: nextPlotOwnerId(),
          idType: IdType.OWNER_ID,
        },
        TEST_APP_ID,
      );

      expect(response.body).toEqual({
        code: 'ERROR',
        message: 'Unexpected error',
        statusCode: 500,
      });
    });
  });

  describe('unsupported methods', () => {
    it.each(['GET', 'PUT', 'OPTIONS', 'TRACE', 'PATCH'])(
      `unsupported method [%s] (NOT_FOUND)`,
      async (method: string) => {
        const response = await request(method, url)
          .ok((_) => true)
          .send();
        expect(response).toBeNotFound();
        expect(response.body).toEqual({
          code: 'NOT_FOUND',
          message: `Route ${method}:/hid/account not found`,
          statusCode: 404,
        });
      },
    );
  });

  describe('app-id check', () => {
    it('no APP ID (NO_APP_ID | UNAUTHORIZED)', async () => {
      const response = await linkAccount({} as never, undefined);
      expect(response).toBeUnauthorised();
      expect(response.body).toEqual({
        code: 'FORBIDDEN',
        message: 'x-app-id header is missing',
        statusCode: 403,
      });
    });

    it('empty APP ID (NO_APP_ID | UNAUTHORIZED)', async () => {
      const response = await linkAccount({} as never, '');
      expect(response).toBeUnauthorised();
      expect(response.body).toEqual({
        code: 'FORBIDDEN',
        message: 'x-app-id header is missing',
        statusCode: 403,
      });
    });
  });
});
