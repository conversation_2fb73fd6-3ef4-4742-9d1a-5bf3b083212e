import { afterAll, beforeAll, beforeEach, describe, expect, it, Mock, vi } from 'vitest';
import request, { Response } from 'superagent';
import { Server } from '../../mock/server.js';
import { X_APP_ID_HEADER, X_APP_KEY_HEADER } from '../../../src/hid/header.js';
import {
  HavenServiceAccount,
  HavenServiceAccountIdType as IdType,
  HID,
  inMemoryHIDDomain,
  HavenIdentity,
} from 'haven-hid-domain';
import { nextHID, nextHIDToEmail, nextSeaWareClientWithEmail } from 'haven-hid-domain/mock.js';
import {
  PgHavenIdentityTransactionalStore,
  PgHIDForEmailPlotAndSeaWare,
} from 'haven-hid-pgsql-adapter/adapter.js';
import { jwtVerify } from 'jose';
import { setupIdentity, setupLiteIdentity } from '../../helper.js';
import { bcryptHash } from 'haven-identity-password-util';

vi.mock('haven-hid-pgsql-adapter/adapter.js');

const domain = inMemoryHIDDomain();
(PgHavenIdentityTransactionalStore as Mock).mockImplementation(() => domain.identityStore);
(PgHIDForEmailPlotAndSeaWare as Mock).mockImplementation(() => domain.hidForEmailPlotAndSeaWare);

vi.mock('jose');
const mockVerify = jwtVerify as Mock;

const mocks = vi.hoisted(() => ({
  sendPasswordChangedEmail: vi.fn(),
}));
vi.mock('haven-hid-domain', async (original) => {
  const mod: Object = await original();
  return {
    ...mod,
    sendPasswordChangedEmail: mocks.sendPasswordChangedEmail,
  };
});

const server = new Server();

const TEST_APP_ID = 'TEST_APP';
const TEST_MANAGEMENT_KEY = 'MANAGEMENT-KEY';
const accessToken = '<jwt-token>';
const headers = {
  appId: TEST_APP_ID,
  appKey: TEST_MANAGEMENT_KEY,
  jwt: accessToken,
};

const BAD_PASSWORD = 'test';
const GOOD_PASSWORD = 'newPassword1234test';
const GOOD_PASSWORD_HASH = await bcryptHash(GOOD_PASSWORD);

describe('Update Lite Password', () => {
  const getUrl = (hid: string): string => `${server.baseUrl}/identity/lite/${hid}/password`;
  const makeRequest = async (
    path: string,
    headers: {
      appId?: string;
      appKey?: string;
      jwt?: string | undefined;
    },
    body: {
      password: string;
      currentPassword?: string;
    },
  ): Promise<Response> => {
    const put = request.put(path).ok(() => true);
    if (headers.appKey) {
      put.set(X_APP_KEY_HEADER, headers.appKey);
    }
    if (headers.appId) {
      put.set(X_APP_ID_HEADER, headers.appId);
    }
    if (headers.jwt) {
      put.set('Authorization', `Bearer ${headers.jwt}`);
    }
    return put.send(body);
  };

  beforeAll(async () => {
    await server.start();
  });

  afterAll(async () => {
    await server.stop();
  });

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('existing user', async () => {
    let hid: HID;
    let url: string;
    let payload: {
      password: string;
    };
    let liteIdentity: HavenIdentity;

    beforeEach(async () => {
      payload = {
        password: GOOD_PASSWORD,
      };
      const email = nextHIDToEmail();
      liteIdentity = await setupLiteIdentity(email.email, {
        passwordHash: GOOD_PASSWORD_HASH,
      });
      hid = liteIdentity.hid;
      url = getUrl(hid);
      mockMigratedToken(hid, []);
    });

    it('returns 200 response', async () => {
      const response = await makeRequest(url, headers, payload);

      expect(mocks.sendPasswordChangedEmail).toHaveBeenCalledWith({
        email: liteIdentity.email.email,
        templateId: process.env.PASSWORD_CHANGED_TEMPLATE_ID || '',
        name: `${liteIdentity.firstName} ${liteIdentity.lastName}`,
      });
      expect(response.body).toEqual({ hid });
      expect(response.status).toBe(200);
    });

    it('returns 422 response for missing user', async () => {
      const unknownHID = nextHID();
      const url = getUrl(unknownHID);
      mockMigratedToken(unknownHID, []);

      const response = await makeRequest(url, headers, payload);

      expect(mocks.sendPasswordChangedEmail).not.toHaveBeenCalled();
      expect(response.body).toEqual({
        code: 'INVALID_HID',
        message: 'No such HID',
        statusCode: 422,
      });
      expect(response.status).toBe(422);
    });

    it('returns 422 response for not a lite user', async () => {
      const seawareClient = nextSeaWareClientWithEmail();
      const identity = await setupIdentity(seawareClient);
      const hid = identity.hid;
      const url = getUrl(hid);
      mockMigratedToken(hid, [
        { id: seawareClient.seaWareClientId, type: IdType.SEAWARE_CLIENT_ID },
      ]);

      const response = await makeRequest(url, headers, payload);

      expect(mocks.sendPasswordChangedEmail).not.toHaveBeenCalled();
      expect(response.body).toEqual({
        code: 'INVALID_HID',
        message: 'No such Lite HID',
        statusCode: 422,
      });
      expect(response.status).toBe(422);
    });

    it('returns access denied if missing MANAGEMENT_KEY', async () => {
      const { status, body } = await makeRequest(url, { ...headers, appKey: '' }, payload);
      expect(status).toBe(401);
      expect(body.code).toBe('NOT_AUTHORISED');
      expect(body.message).toBe('x-app-key header is required');
    });

    it('returns access denied if incorrect MANAGEMENT_KEY', async () => {
      const { status, body } = await makeRequest(url, { ...headers, appKey: 'Invalid' }, payload);
      expect(status).toBe(401);
      expect(body.code).toBe('NOT_AUTHORISED');
      expect(body.message).toBe('invalid x-app-key header');
    });

    it('returns 400 for request with missing data', async () => {
      // @ts-ignore
      const { status, body } = await makeRequest(url, headers, {});
      expect(mocks.sendPasswordChangedEmail).not.toHaveBeenCalled();
      expect(status).toBe(400);
      expect(body.code).toBe('VALIDATION');
      expect(body.message).toBe(`body must have required property 'password'`);
    });

    it('returns 400 for bad HID', async () => {
      const url = `${server.baseUrl}/identity/aaaaa/password`;
      const { status, body } = await makeRequest(url, headers, payload);
      expect(mocks.sendPasswordChangedEmail).not.toHaveBeenCalled();
      expect(status).toBe(400);
      expect(body.code).toBe('VALIDATION');
      expect(body.message).toBe('params/hid must match pattern "[A-Z0-9]{12}"');
    });

    it('returns 400 for a password that does not pass validation', async () => {
      const { status, body } = await makeRequest(url, headers, {
        ...payload,
        password: BAD_PASSWORD,
      });
      expect(mocks.sendPasswordChangedEmail).not.toHaveBeenCalled();
      expect(status).toBe(400);
      expect(body.code).toBe('VALIDATION');
    });

    it('returns 401 response for a missing idtoken', async () => {
      const { status } = await makeRequest(url, { ...headers, jwt: undefined }, payload);
      expect(status).toBe(401);
    });

    it('returns 401 response for a bad idtoken', async () => {
      mockVerify.mockRejectedValue(new Error('Bad'));
      const { status } = await makeRequest(url, headers, payload);
      expect(status).toBe(401);
    });

    it('returns 200 for a good current password', async () => {
      const { status } = await makeRequest(url, headers, {
        ...payload,
        currentPassword: GOOD_PASSWORD,
      });
      expect(status).toBe(200);
    });

    it('returns 401 for a bad current password', async () => {
      const { status, body } = await makeRequest(url, headers, {
        ...payload,
        currentPassword: 'BAD',
      });
      expect(mocks.sendPasswordChangedEmail).not.toHaveBeenCalled();
      expect(status).toBe(401);
      expect(body.code).toBe('UPDATE_PASSWORD_NOT_AUTHORISED');
    });

    it('returns 401 for a empty current password', async () => {
      const { status, body } = await makeRequest(url, headers, {
        ...payload,
        currentPassword: '',
      });
      expect(mocks.sendPasswordChangedEmail).not.toHaveBeenCalled();
      expect(status).toBe(401);
      expect(body.code).toBe('UPDATE_PASSWORD_NOT_AUTHORISED');
    });

    it('returns 403 for idtoken that does not match the path HID', async () => {
      mockVerify.mockResolvedValue({
        payload: { hid: 'not-this-x' },
      });
      const { status } = await makeRequest(url, headers, payload);
      expect(mocks.sendPasswordChangedEmail).not.toHaveBeenCalled();
      expect(status).toBe(403);
    });
  });

  const mockMigratedToken = (hid: HID, accounts: HavenServiceAccount[]) => {
    mockVerify.mockResolvedValue({
      payload: {
        sub: hid,
        hid: hid,
        accounts: accounts,
      },
    });
  };
});
