import { afterAll, beforeAll, beforeEach, describe, expect, it, Mock, vi } from 'vitest';
import request, { Response } from 'superagent';
import { Server } from '../../mock/server.js';
import { X_APP_ID_HEADER, X_APP_KEY_HEADER } from '../../../src/hid/header.js';
import {
  HavenServiceAccount,
  HavenServiceAccountIdType,
  HavenServiceAccountIdType as IdType,
  HID,
  inMemoryHIDDomain,
  ValidEmail,
  HavenIdentity,
} from 'haven-hid-domain';
import {
  nextHID,
  nextPlotOwnerId,
  nextPlotOwnerWithEmail,
  nextSeaWareClientId,
  nextSeaWareClientWithEmail,
  nextTx,
} from 'haven-hid-domain/mock.js';
import {
  PgHavenIdentityTransactionalStore,
  PgHIDForEmailPlotAndSeaWare,
} from 'haven-hid-pgsql-adapter/adapter.js';
import { jwtVerify } from 'jose';
import casual from 'casual';
import { AxiosError } from 'axios';
import { BLAPI, BlapiClientError } from 'haven-identity-blapi-client';
import { Plot } from 'haven-identity-plot-client';
import { bcryptHash } from 'haven-identity-password-util';

vi.mock('haven-hid-pgsql-adapter/adapter.js');
vi.mock('haven-identity-plot-client', async () => {
  const module = await vi.importActual<typeof import('haven-identity-plot-client')>(
    'haven-identity-plot-client',
  );
  const Plot = vi.fn();
  Plot.prototype.validatePassword = vi.fn();
  Plot.prototype.updatePassword = vi.fn();
  Plot.prototype.updateProfile = vi.fn();
  return {
    ...module,
    Plot,
  };
});
vi.mock('haven-identity-blapi-client', async () => {
  const module = await vi.importActual<typeof import('haven-identity-blapi-client')>(
    'haven-identity-blapi-client',
  );
  const BLAPI = vi.fn();
  BLAPI.prototype.validatePassword = vi.fn();
  BLAPI.prototype.updatePassword = vi.fn();
  BLAPI.prototype.updateProfile = vi.fn();

  return { ...module, BLAPI };
});

const mockPlot = {
  validatePassword: Plot.prototype.validatePassword as Mock,
  updatePassword: Plot.prototype.updatePassword as Mock,
};
const mockBlapi = {
  validatePassword: BLAPI.prototype.validatePassword as Mock,
  updatePassword: BLAPI.prototype.updatePassword as Mock,
};

const domain = inMemoryHIDDomain();
(PgHavenIdentityTransactionalStore as Mock).mockImplementation(() => domain.identityStore);
(PgHIDForEmailPlotAndSeaWare as Mock).mockImplementation(() => domain.hidForEmailPlotAndSeaWare);

vi.mock('jose');
const mockVerify = jwtVerify as Mock;

const mocks = vi.hoisted(() => ({
  sendPasswordChangedEmail: vi.fn(),
}));
vi.mock('haven-hid-domain', async (original) => {
  const mod: Object = await original();
  return {
    ...mod,
    sendPasswordChangedEmail: mocks.sendPasswordChangedEmail,
  };
});

const server = new Server();

const TEST_APP_ID = 'TEST_APP';
const TEST_MANAGEMENT_KEY = 'MANAGEMENT-KEY';
const accessToken = '<jwt-token>';
const headers = {
  appId: TEST_APP_ID,
  appKey: TEST_MANAGEMENT_KEY,
  jwt: accessToken,
};

const BAD_PASSWORD = 'test';
const GOOD_PASSWORD = 'newPassword1234test';
const GOOD_PASSWORD_HASH = await bcryptHash(GOOD_PASSWORD);

const GOOD_BLAPI_PASSWORD = 'Password123!';

describe('Update Password', () => {
  const getUrl = (hid: string): string => `${server.baseUrl}/identity/${hid}/password`;
  const makeRequest = async (
    path: string,
    headers: {
      appId?: string;
      appKey?: string;
      jwt?: string | undefined;
    },
    body: {
      idType?: IdType;
      id?: number;
      password: string;
      currentPassword?: string;
    },
  ): Promise<Response> => {
    const put = request.put(path).ok(() => true);
    if (headers.appKey) {
      put.set(X_APP_KEY_HEADER, headers.appKey);
    }
    if (headers.appId) {
      put.set(X_APP_ID_HEADER, headers.appId);
    }
    if (headers.jwt) {
      put.set('Authorization', `Bearer ${headers.jwt}`);
    }
    return put.send(body);
  };

  beforeAll(async () => {
    await server.start();
  });

  afterAll(async () => {
    await server.stop();
  });

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('existing user', async () => {
    let hid: HID;
    let url: string;
    let payload: {
      password: string;
      id: number;
      idType: IdType;
    };
    let identity: HavenIdentity;

    beforeEach(async () => {
      payload = {
        password: GOOD_PASSWORD,
        id: nextPlotOwnerId(),
        idType: IdType.OWNER_ID,
      };
      identity = await setupIdentity(nextSeaWareClientWithEmail());
      hid = identity.hid;
      url = getUrl(hid);
      mockMigratedToken(hid, [{ id: payload.id, type: payload.idType }]);
    });

    it('returns 200 response', async () => {
      const response = await makeRequest(url, headers, payload);

      expect(mocks.sendPasswordChangedEmail).toHaveBeenCalledWith({
        email: identity.email.email,
        templateId: process.env.PASSWORD_CHANGED_TEMPLATE_ID || '',
        name: `${identity.firstName} ${identity.lastName}`,
      });
      expect(response.body).toEqual({ hid });
      expect(response.status).toBe(200);
    });

    it('returns 422 response for missing user', async () => {
      const unknownHID = nextHID();
      const url = getUrl(unknownHID);
      mockMigratedToken(unknownHID, [{ id: payload.id, type: payload.idType }]);

      const response = await makeRequest(url, headers, payload);

      expect(mocks.sendPasswordChangedEmail).not.toHaveBeenCalled();
      expect(response.body).toEqual({
        code: 'INVALID_HID',
        message: 'No such HID',
        statusCode: 422,
      });
      expect(response.status).toBe(422);
    });

    it('returns access denied if missing MANAGEMENT_KEY', async () => {
      const { status, body } = await makeRequest(url, { ...headers, appKey: '' }, payload);
      expect(status).toBe(401);
      expect(body.code).toBe('NOT_AUTHORISED');
      expect(body.message).toBe('x-app-key header is required');
    });

    it('returns access denied if incorrect MANAGEMENT_KEY', async () => {
      const { status, body } = await makeRequest(url, { ...headers, appKey: 'Invalid' }, payload);
      expect(status).toBe(401);
      expect(body.code).toBe('NOT_AUTHORISED');
      expect(body.message).toBe('invalid x-app-key header');
    });

    it('returns 400 for request with missing data', async () => {
      // @ts-ignore
      const { status, body } = await makeRequest(url, headers, {});
      expect(mocks.sendPasswordChangedEmail).not.toHaveBeenCalled();
      expect(status).toBe(400);
      expect(body.code).toBe('VALIDATION');
      expect(body.message).toBe(`body must have required property 'password'`);
    });

    it('returns 400 for request with id/type not on identity', async () => {
      // @ts-ignore
      const { status, body } = await makeRequest(url, headers, {
        id: 666,
        idType: HavenServiceAccountIdType.SEAWARE_CLIENT_ID,
        password: payload.password,
      });
      expect(mocks.sendPasswordChangedEmail).not.toHaveBeenCalled();
      expect(body.message).toBe(`Access token accounts do not match request`);
      expect(body.code).toBe('FORBIDDEN');
      expect(status).toBe(403);
    });

    it('returns 400 for request with no id but idType is there', async () => {
      // @ts-ignore
      const { status, body } = await makeRequest(url, headers, {
        idType: HavenServiceAccountIdType.SEAWARE_CLIENT_ID,
        password: payload.password,
      });
      expect(mocks.sendPasswordChangedEmail).not.toHaveBeenCalled();
      expect(status).toBe(400);
      expect(body.code).toBe('VALIDATION');
      expect(body.message).toBe(`Missing id for idType="seaware_client_id"`);
    });

    it('returns 400 for request with no idType but id is there', async () => {
      // @ts-ignore
      const { status, body } = await makeRequest(url, headers, {
        id: 1000,
        password: payload.password,
      });
      expect(mocks.sendPasswordChangedEmail).not.toHaveBeenCalled();
      expect(status).toBe(400);
      expect(body.code).toBe('VALIDATION');
      expect(body.message).toBe(`Missing idType for id="1000"`);
    });

    it('returns 400 for bad HID', async () => {
      const url = `${server.baseUrl}/identity/aaaaa/password`;
      const { status, body } = await makeRequest(url, headers, payload);
      expect(mocks.sendPasswordChangedEmail).not.toHaveBeenCalled();
      expect(status).toBe(400);
      expect(body.code).toBe('VALIDATION');
      expect(body.message).toBe('params/hid must match pattern "[A-Z0-9]{12}"');
    });

    it('returns 400 for a password that does not pass validation', async () => {
      const { status, body } = await makeRequest(url, headers, {
        ...payload,
        password: BAD_PASSWORD,
      });
      expect(mocks.sendPasswordChangedEmail).not.toHaveBeenCalled();
      expect(status).toBe(400);
      expect(body.code).toBe('VALIDATION');
    });

    it('returns 400 for an unknown id type', async () => {
      const { status, body } = await makeRequest(url, headers, {
        ...payload,
        idType: 'xxx' as IdType,
      });
      expect(mocks.sendPasswordChangedEmail).not.toHaveBeenCalled();
      expect(status).toBe(400);
      expect(body.code).toBe('VALIDATION');
      expect(body.message).toBe('body/idType must be equal to one of the allowed values');
    });

    it('returns 401 response for a missing idtoken', async () => {
      const { status } = await makeRequest(url, { ...headers, jwt: undefined }, payload);
      expect(status).toBe(401);
    });

    it('returns 401 response for a bad idtoken', async () => {
      mockVerify.mockRejectedValue(new Error('Bad'));
      const { status } = await makeRequest(url, headers, payload);
      expect(status).toBe(401);
    });

    it('returns 200 for a good current password', async () => {
      const { status, body } = await makeRequest(url, headers, {
        ...payload,
        currentPassword: GOOD_PASSWORD,
      });
      expect(status).toBe(200);
    });

    it('returns 401 for a bad current password', async () => {
      const { status, body } = await makeRequest(url, headers, {
        ...payload,
        currentPassword: 'BAD',
      });
      expect(mocks.sendPasswordChangedEmail).not.toHaveBeenCalled();
      expect(status).toBe(401);
      expect(body.code).toBe('UPDATE_PASSWORD_NOT_AUTHORISED');
    });

    it('returns 401 for a empty current password', async () => {
      const { status, body } = await makeRequest(url, headers, {
        ...payload,
        currentPassword: '',
      });
      expect(mocks.sendPasswordChangedEmail).not.toHaveBeenCalled();
      expect(status).toBe(401);
      expect(body.code).toBe('UPDATE_PASSWORD_NOT_AUTHORISED');
    });

    it('returns 403 for idtoken that does not match the path HID', async () => {
      mockVerify.mockResolvedValue({
        payload: { hid: 'not-this-x' },
      });
      const { status } = await makeRequest(url, headers, payload);
      expect(mocks.sendPasswordChangedEmail).not.toHaveBeenCalled();
      expect(status).toBe(403);
    });

    it('returns 403 for idtoken where owner id does not match request', async () => {
      mockVerify.mockResolvedValue({
        payload: { hid, accounts: [] },
      });
      const { status } = await makeRequest(url, headers, payload);
      expect(mocks.sendPasswordChangedEmail).not.toHaveBeenCalled();
      expect(status).toBe(403);
    });
  });

  describe('PLOT proxy authenticated user tests', async () => {
    const PLOT_ID_PREFIX = 'PLOT:';
    let hid: HID;
    let url: string;
    let payload: {
      password: string;
      id: number;
      idType: IdType;
    };

    beforeEach(async () => {
      payload = {
        password: GOOD_PASSWORD,
        id: nextPlotOwnerId(),
        idType: IdType.OWNER_ID,
      };
      const indentity = await setupIdentity(nextPlotOwnerWithEmail());
      hid = indentity.hid;
      url = getUrl(hid);
    });

    it('returns 200 response for owner and only updates PLOT', async () => {
      mockPlot.validatePassword.mockReturnValue(undefined);
      mockProxyAuthenticatedToken(PLOT_ID_PREFIX, hid, payload.id, [
        {
          id: payload.id,
          type: payload.idType,
        },
      ]);

      const response = await makeRequest(url, headers, payload);
      expect(mockPlot.validatePassword).toHaveBeenCalledWith(GOOD_PASSWORD);
      expect(mockPlot.updatePassword).toHaveBeenCalledWith(payload.id, GOOD_PASSWORD);
      expect(response.status).toBe(200);
      expect(mockBlapi.validatePassword).not.toHaveBeenCalled();
    });

    it('returns 400 for a password that does not pass validation', async () => {
      const ValidationError = 'Password must be between 8 and 30 characters long';
      mockPlot.validatePassword.mockReturnValue(ValidationError);
      mockProxyAuthenticatedToken(PLOT_ID_PREFIX, hid, payload.id, [
        {
          id: payload.id,
          type: payload.idType,
        },
      ]);

      const { status, body } = await makeRequest(url, headers, {
        ...payload,
        password: 'test',
      });

      expect(status).toBe(400);
      expect(body.code).toBe('VALIDATION');
      expect(body.message).toBe('body/password is not valid password');
      expect(body.strength).toBe('unknown');
      expect(body.warning).toBe('Password does not match the requirements');
      expect(body.suggestions).toStrictEqual([ValidationError]);
    });

    it('returns 400 response for missing id/type', async () => {
      mockPlot.validatePassword.mockReturnValue(undefined);
      mockProxyAuthenticatedToken(PLOT_ID_PREFIX, hid, payload.id, [
        {
          id: payload.id,
          type: payload.idType,
        },
      ]);

      const response = await makeRequest(url, headers, { password: payload.password });
      expect(mockPlot.validatePassword).not.toHaveBeenCalled();
      expect(mockPlot.updatePassword).not.toHaveBeenCalled();
      expect(mockBlapi.validatePassword).not.toHaveBeenCalled();
      expect(response.status).toBe(400);
      expect(response.body.message).toEqual('Unmigrated user requires an id and idType');
    });
  });

  describe('SEAWARE proxy authenticated user tests', async () => {
    const SEAWARE_ID_PREFIX = 'SEAWARE:';
    let hid: HID;
    let url: string;
    let payload: {
      password: string;
      id: number;
      idType: IdType;
      currentPassword: string;
    };

    beforeEach(async () => {
      payload = {
        password: GOOD_BLAPI_PASSWORD,
        id: nextSeaWareClientId(),
        idType: IdType.SEAWARE_CLIENT_ID,
        currentPassword: GOOD_BLAPI_PASSWORD,
      };
      const indentity = await setupIdentity(nextSeaWareClientWithEmail());
      hid = indentity.hid;
      url = getUrl(hid);
    });

    it('returns 200 response and only updates seaware', async () => {
      mockBlapi.validatePassword.mockReturnValue(undefined);
      mockProxyAuthenticatedToken(SEAWARE_ID_PREFIX, hid, payload.id, [
        {
          id: payload.id,
          type: payload.idType,
        },
      ]);

      const response = await makeRequest(url, headers, payload);
      expect(mockBlapi.validatePassword).toHaveBeenCalledWith(GOOD_BLAPI_PASSWORD);
      expect(mockBlapi.updatePassword).toHaveBeenCalledWith(accessToken, {
        currentPassword: GOOD_BLAPI_PASSWORD,
        newPassword: GOOD_BLAPI_PASSWORD,
      });
      expect(response.status).toBe(200);
      expect(mockPlot.validatePassword).not.toHaveBeenCalled();
    });

    it('returns 400 for a password that does not pass validation', async () => {
      const ValidationError = 'Password must be between 8 and 15 characters long';
      mockBlapi.validatePassword.mockReturnValue(ValidationError);
      mockProxyAuthenticatedToken(SEAWARE_ID_PREFIX, hid, payload.id, [
        {
          id: payload.id,
          type: payload.idType,
        },
      ]);

      const { status, body } = await makeRequest(url, headers, {
        ...payload,
        password: 'test',
      });

      expect(status).toBe(400);
      expect(body.code).toBe('VALIDATION');
      expect(body.message).toBe('body/password is not valid password');
      expect(body.strength).toBe('unknown');
      expect(body.warning).toBe('Password does not match the requirements');
      expect(body.suggestions).toStrictEqual([ValidationError]);
    });

    it('returns 400 if a current password not provided', async () => {
      const { currentPassword, ...payloadWithoutCurrentPassword } = payload;
      mockProxyAuthenticatedToken(SEAWARE_ID_PREFIX, hid, payload.id, [
        {
          id: payload.id,
          type: payload.idType,
        },
      ]);

      const { status, body } = await makeRequest(url, headers, payloadWithoutCurrentPassword);

      expect(status).toBe(400);
      expect(body.code).toBe('VALIDATION');
      expect(body.message).toBe('body/currentPassword is required');
    });

    it('returns 400 response for missing id/type', async () => {
      mockPlot.validatePassword.mockReturnValue(undefined);
      mockProxyAuthenticatedToken(SEAWARE_ID_PREFIX, hid, payload.id, [
        {
          id: payload.id,
          type: payload.idType,
        },
      ]);

      const response = await makeRequest(url, headers, { password: payload.password });
      expect(mockPlot.validatePassword).not.toHaveBeenCalled();
      expect(mockPlot.updatePassword).not.toHaveBeenCalled();
      expect(mockBlapi.validatePassword).not.toHaveBeenCalled();
      expect(response.status).toBe(400);
      expect(response.body.message).toEqual('Unmigrated user requires an id and idType');
    });

    it('returns 401 if blapi update fails with update authentication error', async () => {
      mockBlapi.validatePassword.mockReturnValue(undefined);
      mockProxyAuthenticatedToken(SEAWARE_ID_PREFIX, hid, payload.id, [
        {
          id: payload.id,
          type: payload.idType,
        },
      ]);
      mockBlapi.updatePassword.mockImplementation(() => {
        throw BlapiClientError.updatePasswordAuthorisationError(new Error() as AxiosError);
      });

      const response = await makeRequest(url, headers, payload);
      expect(mockBlapi.validatePassword).toHaveBeenCalled();
      expect(mockBlapi.updatePassword).toHaveBeenCalled();
      expect(response.status).toBe(401);
      expect(response.body).toEqual({
        code: 'UPDATE_PASSWORD_NOT_AUTHORISED',
        message: 'Seaware update password authorization failed',
        statusCode: 401,
      });
    });
  });

  const mockMigratedToken = (hid: HID, accounts: HavenServiceAccount[]) => {
    mockVerify.mockResolvedValue({
      payload: {
        sub: hid,
        hid: hid,
        accounts: accounts,
      },
    });
  };

  const setupIdentity = async (
    existing:
      | {
          email: ValidEmail;
          plotOwnerId: number;
          seaWareClientId?: undefined;
        }
      | {
          email: ValidEmail;
          plotOwnerId?: undefined;
          seaWareClientId: number;
        }
      | {
          email: ValidEmail;
          plotOwnerId: number;
          seaWareClientId: number;
        },
  ) => {
    const accounts = [];
    if (existing.plotOwnerId) {
      accounts.push({
        id: existing.plotOwnerId,
        type: IdType.OWNER_ID,
      });
    }
    if (existing.seaWareClientId) {
      accounts.push({
        id: existing.seaWareClientId,
        type: IdType.SEAWARE_CLIENT_ID,
      });
    }
    const created = await domain.identityStore.createIdentity(
      {
        name: casual.name,
        title: casual.title,
        firstName: casual.first_name,
        lastName: casual.last_name,
        email: existing.email,
        emailVerified: false,
        accounts: accounts,
        passwordHash: GOOD_PASSWORD_HASH,
      },
      nextTx(),
    );
    return created;
  };

  const mockProxyAuthenticatedToken = (
    subjectPrefix: string,
    hid: HID,
    id: number,
    accounts: HavenServiceAccount[],
  ) => {
    mockVerify.mockResolvedValue({
      payload: {
        sub: `${subjectPrefix}${id}`,
        hid: hid,
        accounts: accounts,
      },
    });
  };
});
