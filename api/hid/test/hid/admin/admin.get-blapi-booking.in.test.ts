import request from 'superagent';
import { afterAll, beforeAll, beforeEach, describe, expect, it, Mock, vi } from 'vitest';
import { X_APP_ID_HEADER, X_APP_KEY_HEADER } from '../../../src/hid/header.js';
import { Server } from '../../mock/server.js';

import { identityLookup } from '../../../src/hid/admin/identity-lookup.js';
import { generateBlapiBearerToken } from '../../../src/access/admin-blapi-access.js';

const TEST_APP_ID = 'TEST_APP';
const TEST_ADMIN_KEY = 'ADMIN-KEY';

const mockBlapi = {
  getBooking: vi.fn(),
};

vi.mock('../../../src/hid/admin/identity-lookup.js');
vi.mock('../../../src/access/admin-blapi-access.js');
vi.mock('haven-hid-pgsql-adapter/adapter.js');
vi.mock('haven-identity-blapi-client', async () => {
  const mod = await vi.importActual<typeof import('haven-identity-blapi-client')>(
    'haven-identity-blapi-client',
  );
  return {
    ...mod,
    BLAPI: vi.fn().mockImplementation(() => mockBlapi),
  };
});

const server = new Server();

describe('admin.get-blapi-booking.in Tests', () => {
  const makeRequest = async (path: string, appId: string, adminKey: string) =>
    await request
      .get(path)
      .set(X_APP_ID_HEADER, appId)
      .set(X_APP_KEY_HEADER, adminKey)
      .ok(() => true);

  beforeAll(async () => {
    await server.start();
  });

  afterAll(async () => {
    await server.stop();
  });

  beforeEach(() => {
    vi.resetAllMocks();
  });

  it('should return a single booking for a given bookingRef', async () => {
    const hid = 'ABCDEFGH1234';
    const bookingRef = '123';
    const booking = { id: bookingRef };

    (identityLookup as Mock).mockResolvedValue({ accounts: [{ type: 'seaware_client_id' }] });
    mockBlapi.getBooking.mockReturnValue(booking);

    const { body } = await makeRequest(
      `${server.baseUrl}/admin/identity/${hid}/blapi-booking/${bookingRef}`,
      TEST_APP_ID,
      TEST_ADMIN_KEY,
    );

    expect(body).toEqual(booking);
  });

  it('should return an empty object when the found identity does not have a seaware account', async () => {
    const hid = 'ABCDEFGH1234';
    const bookingRef = '123';
    const booking = { id: bookingRef };

    (identityLookup as Mock).mockResolvedValue({ accounts: [] });
    mockBlapi.getBooking.mockReturnValue(booking);

    const { body } = await makeRequest(
      `${server.baseUrl}/admin/identity/${hid}/blapi-booking/${bookingRef}`,
      TEST_APP_ID,
      TEST_ADMIN_KEY,
    );

    expect(body).toEqual({});
  });

  it('should log and throw an error when an error occurres.', async () => {
    const hid = 'ABCDEFGH1234';
    const bookingRef = '123';
    const error = new Error(
      `He's madder than Mad Jack McMad, the winner of last year's Mr. Madman competition`,
    );

    (identityLookup as Mock).mockRejectedValue(error);

    const { body } = await makeRequest(
      `${server.baseUrl}/admin/identity/${hid}/blapi-booking/${bookingRef}`,
      TEST_APP_ID,
      TEST_ADMIN_KEY,
    );

    expect(body.message).toEqual(error.message);
  });
});
