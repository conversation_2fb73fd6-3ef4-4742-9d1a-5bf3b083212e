import { vi, Mock, afterAll, beforeAll, describe, expect, it, beforeEach } from 'vitest';
import request from 'superagent';

import { Server } from '../../mock/server.js';
import {
  AUTHORIZATION_HEADER,
  X_APP_ID_HEADER,
  X_APP_KEY_HEADER,
} from '../../../src/hid/header.js';
import { HavenServiceAccountIdType as IdType, inMemoryHIDDomain } from 'haven-hid-domain';
import {
  nextPlotOwnerId,
  nextSeaWareClientId,
  nextHID,
  nextValidEmail,
  nextTx,
} from 'haven-hid-domain/mock.js';
import { PgHIDForEmailPlotAndSeaWare } from 'haven-hid-pgsql-adapter/adapter.js';
import { jwtVerify } from 'jose';
import { URI as path } from '../../../src/hid/admin/admin.unlink-account.in.js';
import { lookupPlotAudits, lookupSeawareAudits } from '../../helper.js';

const mockPersistence = inMemoryHIDDomain().hidForEmailPlotAndSeaWare;
(PgHIDForEmailPlotAndSeaWare as Mock).mockImplementation(() => mockPersistence);

vi.mock('haven-hid-pgsql-adapter/adapter.js');
vi.mock('jose');

describe('Unlink Service Account ID From Identity', () => {
  const TEST_APP_ID = 'TEST_APP';
  const server = new Server();
  let url: string;

  beforeAll(async () => {
    await server.start();
    url = `${server.baseUrl}${path}`;
  });

  afterAll(async () => {
    await server.stop();
  });

  describe('handle unlink request', () => {
    beforeEach(() => {
      (jwtVerify as Mock).mockResolvedValue({ payload: {} });
    });

    it('should respond with 200 and hid when unlinking account for plot ownerId and create audit', async () => {
      const plotOwnerId = nextPlotOwnerId();
      const email = nextValidEmail();
      const tx = nextTx('test');
      const hid = await mockPersistence.getHIDForEmailAndPlot(email, undefined, plotOwnerId, tx);

      const { body, status } = await request
        .delete(url)
        .set(X_APP_ID_HEADER, 'appId')
        .set(X_APP_KEY_HEADER, 'MANAGEMENT-KEY')
        .set(AUTHORIZATION_HEADER, `Bearer <jwt-token>`)
        .ok(() => true)
        .send({
          hid,
          id: plotOwnerId,
          idType: IdType.OWNER_ID,
        });

      const audit = await lookupPlotAudits(tx.id, tx.source);

      expect(audit).toEqual([
        expect.objectContaining({
          source: tx.source,
          tx: tx.id,
          type: 'plot_owner',
        }),
      ]);

      expect(status).toBe(200);
      expect(body).toEqual({ hid });
      expect(await mockPersistence.getHIDAndEmailForPlot(plotOwnerId)).toBeUndefined();
    });

    it('should respond with 200 and hid when unlinking account for seaware clientId and create audit', async () => {
      const clientId = nextSeaWareClientId();
      const email = nextValidEmail();
      const tx = nextTx('test');
      const hid = await mockPersistence.getHIDForEmailAndSeaWare(email, undefined, clientId, tx);

      const { body, status } = await request
        .delete(url)
        .set(X_APP_ID_HEADER, 'appId')
        .set(X_APP_KEY_HEADER, 'MANAGEMENT-KEY')
        .set(AUTHORIZATION_HEADER, `Bearer <jwt-token>`)
        .ok(() => true)
        .send({
          hid,
          id: clientId,
          idType: IdType.SEAWARE_CLIENT_ID,
        });

      const audit = await lookupSeawareAudits(tx.id, tx.source);

      expect(audit).toEqual([
        expect.objectContaining({
          source: tx.source,
          tx: tx.id,
          type: 'seaware_client',
        }),
      ]);

      expect(status).toBe(200);
      expect(body).toEqual({ hid });
      expect(await mockPersistence.getHIDAndEmailForSeaWare(clientId)).toBeUndefined();
    });

    it('should respond with 400 when supplied hid does not match ownerId hid', async () => {
      const plotOwnerId = nextPlotOwnerId();
      const email = nextValidEmail();
      await mockPersistence.getHIDForEmailAndPlot(email, undefined, plotOwnerId, nextTx('test'));

      const { body } = await request
        .delete(url)
        .set(X_APP_ID_HEADER, 'appId')
        .set(X_APP_KEY_HEADER, 'MANAGEMENT-KEY')
        .set(AUTHORIZATION_HEADER, `Bearer <jwt-token>`)
        .ok(() => true)
        .send({
          hid: nextHID(),
          id: plotOwnerId,
          idType: IdType.OWNER_ID,
        });

      expect(body).toEqual({
        code: 'VALIDATION',
        statusCode: 400,
        message: expect.stringContaining('Cannot unlink HID'),
      });
    });

    it('should respond with 400 when supplied hid does not match clientId hid', async () => {
      const clientId = nextSeaWareClientId();
      const email = nextValidEmail();
      await mockPersistence.getHIDForEmailAndSeaWare(email, undefined, clientId, nextTx('test'));

      const { body, status } = await request
        .delete(url)
        .set(X_APP_ID_HEADER, 'appId')
        .set(X_APP_KEY_HEADER, 'MANAGEMENT-KEY')
        .set(AUTHORIZATION_HEADER, `Bearer <jwt-token>`)
        .ok(() => true)
        .send({
          hid: nextHID(),
          id: clientId,
          idType: IdType.SEAWARE_CLIENT_ID,
        });

      expect(status).toBe(400);
      expect(body).toEqual({
        code: 'VALIDATION',
        statusCode: 400,
        message: expect.stringContaining('Cannot unlink HID'),
      });
    });

    it('should respond with 400 when hid is invalid', async () => {
      const { body, status } = await request
        .delete(url)
        .set(X_APP_ID_HEADER, 'appId')
        .set(X_APP_KEY_HEADER, 'MANAGEMENT-KEY')
        .set(AUTHORIZATION_HEADER, `Bearer <jwt-token>`)
        .ok(() => true)
        .send({
          hid: 'invalid',
          id: 123,
          idType: IdType.SEAWARE_CLIENT_ID,
        });

      expect(status).toBe(400);
      expect(body).toEqual({
        code: 'VALIDATION',
        message: 'body/hid must match pattern "[A-Z0-9]{12}"',
        statusCode: 400,
      });
    });

    it('should respond with 400 when hid is missing', async () => {
      const { body, status } = await request
        .delete(url)
        .set(X_APP_ID_HEADER, 'appId')
        .set(X_APP_KEY_HEADER, 'MANAGEMENT-KEY')
        .set(AUTHORIZATION_HEADER, `Bearer <jwt-token>`)
        .ok(() => true)
        .send({
          id: 123,
          idType: IdType.SEAWARE_CLIENT_ID,
        });

      expect(status).toBe(400);
      expect(body).toEqual({
        code: 'VALIDATION',
        message: "body must have required property 'hid'",
        statusCode: 400,
      });
    });

    it('should respond with 400 when id is invalid', async () => {
      const { body, status } = await request
        .delete(url)
        .set(X_APP_ID_HEADER, 'appId')
        .set(X_APP_KEY_HEADER, 'MANAGEMENT-KEY')
        .set(AUTHORIZATION_HEADER, `Bearer <jwt-token>`)
        .ok(() => true)
        .send({
          hid: nextHID(),
          id: 'invalid',
          idType: IdType.SEAWARE_CLIENT_ID,
        });

      expect(status).toBe(400);
      expect(body).toEqual({
        code: 'VALIDATION',
        message: 'body/id must be number',
        statusCode: 400,
      });
    });

    it('should respond with 400 when id is missing', async () => {
      const { body, status } = await request
        .delete(url)
        .set(X_APP_ID_HEADER, 'appId')
        .set(X_APP_KEY_HEADER, 'MANAGEMENT-KEY')
        .set(AUTHORIZATION_HEADER, `Bearer <jwt-token>`)
        .ok(() => true)
        .send({
          hid: nextHID(),
          idType: IdType.SEAWARE_CLIENT_ID,
        });

      expect(status).toBe(400);
      expect(body).toEqual({
        code: 'VALIDATION',
        message: "body must have required property 'id'",
        statusCode: 400,
      });
    });

    it('should respond with 400 when idType is missing', async () => {
      const { body, status } = await request
        .delete(url)
        .set(X_APP_ID_HEADER, 'appId')
        .set(X_APP_KEY_HEADER, 'MANAGEMENT-KEY')
        .set(AUTHORIZATION_HEADER, `Bearer <jwt-token>`)
        .ok(() => true)
        .send({
          hid: nextHID(),
          id: 123,
        });

      expect(status).toBe(400);
      expect(body).toEqual({
        code: 'VALIDATION',
        message: "body must have required property 'idType'",
        statusCode: 400,
      });
    });

    it('should respond with 400 when idType is invalid', async () => {
      const { body, status } = await request
        .delete(url)
        .set(X_APP_ID_HEADER, 'appId')
        .set(X_APP_KEY_HEADER, 'MANAGEMENT-KEY')
        .set(AUTHORIZATION_HEADER, `Bearer <jwt-token>`)
        .ok(() => true)
        .send({
          hid: nextHID(),
          id: 123,
          idType: 'invalid',
        });

      expect(status).toBe(400);
      expect(body).toEqual({
        code: 'VALIDATION',
        message: 'body/idType must be equal to one of the allowed values',
        statusCode: 400,
      });
    });

    it('should respond with 500 when runtime error occurs', async () => {
      vi.spyOn(mockPersistence, 'unlinkHIDFromPlot').mockRejectedValue(new Error('Runtime error'));

      const { body, status } = await request
        .delete(url)
        .set(X_APP_ID_HEADER, 'appId')
        .set(X_APP_KEY_HEADER, 'MANAGEMENT-KEY')
        .set(AUTHORIZATION_HEADER, `Bearer <jwt-token>`)
        .ok(() => true)
        .send({
          hid: nextHID(),
          id: nextPlotOwnerId(),
          idType: IdType.OWNER_ID,
        });

      expect(status).toBe(500);
      expect(body).toEqual({
        code: 'ERROR',
        message: 'Runtime error',
        statusCode: 500,
      });
    });

    it('should respond with 403 when x-app-id header is missing', async () => {
      const { body, status } = await request
        .delete(url)
        .set(X_APP_KEY_HEADER, 'MANAGEMENT-KEY')
        .set(AUTHORIZATION_HEADER, `Bearer <jwt-token>`)
        .ok(() => true)
        .send({
          hid: nextHID(),
          id: nextPlotOwnerId(),
          idType: IdType.OWNER_ID,
        });

      expect(status).toBe(403);
      expect(body).toEqual({
        code: 'FORBIDDEN',
        message: 'x-app-id header is missing',
        statusCode: 403,
      });
    });

    it('should respond with 403 when x-app-id header is empty string', async () => {
      const { body, status } = await request
        .delete(url)
        .set(X_APP_ID_HEADER, '')
        .set(X_APP_KEY_HEADER, 'MANAGEMENT-KEY')
        .set(AUTHORIZATION_HEADER, `Bearer <jwt-token>`)
        .ok(() => true)
        .send({
          hid: nextHID(),
          id: nextPlotOwnerId(),
          idType: IdType.OWNER_ID,
        });

      expect(status).toBe(403);
      expect(body).toEqual({
        code: 'FORBIDDEN',
        message: 'x-app-id header is missing',
        statusCode: 403,
      });
    });

    it('should respond with 401 when x-app-key header is missing', async () => {
      const { body, status } = await request
        .delete(url)
        .set(X_APP_ID_HEADER, 'appId')
        .set(AUTHORIZATION_HEADER, `Bearer <jwt-token>`)
        .ok(() => true)
        .send({
          hid: nextHID(),
          id: nextPlotOwnerId(),
          idType: IdType.OWNER_ID,
        });

      expect(status).toBe(401);
      expect(body).toEqual({
        code: 'NOT_AUTHORISED',
        message: 'x-app-key header is required',
        statusCode: 401,
      });
    });

    it('should respond with 401 when x-app-key header is empty string', async () => {
      const { body, status } = await request
        .delete(url)
        .set(X_APP_ID_HEADER, 'appId')
        .set(X_APP_KEY_HEADER, '')
        .set(AUTHORIZATION_HEADER, `Bearer <jwt-token>`)
        .ok(() => true)
        .send({
          hid: nextHID(),
          id: nextPlotOwnerId(),
          idType: IdType.OWNER_ID,
        });

      expect(status).toBe(401);
      expect(body).toEqual({
        code: 'NOT_AUTHORISED',
        message: 'x-app-key header is required',
        statusCode: 401,
      });
    });

    it('should respond with 401 when authorization header is missing', async () => {
      const { body, status } = await request
        .delete(url)
        .set(X_APP_ID_HEADER, 'appId')
        .set(X_APP_KEY_HEADER, 'MANAGEMENT-KEY')
        .ok(() => true)
        .send({
          hid: nextHID(),
          id: nextPlotOwnerId(),
          idType: IdType.OWNER_ID,
        });

      expect(status).toBe(401);
      expect(body).toEqual({
        code: 'NOT_AUTHORISED',
        message: 'No bearer token for identity',
        statusCode: 401,
      });
    });
  });

  describe('unsupported methods', () => {
    it.each(['GET', 'PUT', 'OPTIONS', 'TRACE', 'PATCH'])(
      `should respond with 404 when method is [%S]`,
      async (method: string) => {
        const { body, status } = await request(method, url)
          .ok(() => true)
          .send();
        expect(status).toBe(404);
        expect(body).toEqual({
          code: 'NOT_FOUND',
          message: `Route ${method}:${path} not found`,
          statusCode: 404,
        });
      },
    );
  });
});
