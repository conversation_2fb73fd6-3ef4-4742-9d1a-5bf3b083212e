import { identityLookup } from '../../../src/hid/admin/identity-lookup.js';
import { describe, it, vi, expect, Mock, beforeEach } from 'vitest';
import { AllHIDData, HavenIdentity } from 'haven-hid-domain';
import { getDependencies } from '../../../src/dependencies/dependencies.js';
import { nextValidEmail } from 'haven-hid-domain/mock.js';

vi.mock('../../../src/dependencies/dependencies.js');

describe('identityLookup function', () => {
  const mockFindById = vi.fn();
  const mockFindAllForHID = vi.fn();

  beforeEach(() => {
    (getDependencies as Mock).mockReturnValue({
      identityManagement: {
        findById: mockFindById,
      },
      hidStore: {
        findAllForHID: mockFindAllForHID,
      },
    });
    mockFindAllForHID.mockReturnValue(new AllHIDData());
  });

  it('should return the migrated identity for a given HID', async () => {
    const mockIdentity = {
      hid: 'testhid',
    } as HavenIdentity;
    mockFindById.mockResolvedValueOnce(mockIdentity);

    const result = await identityLookup(mockIdentity.hid);
    expect(result).toEqual({
      ...mockIdentity,
      migrated: true,
    });
  });

  it('should return the migrated identity for a given HID', async () => {
    const hid = 'testhid';
    const validEmail = nextValidEmail();
    const mockIdentity = new AllHIDData();
    mockIdentity.hid = hid;
    mockIdentity.seaWareClientId = 1234;
    mockIdentity.plotOwnerId = 5678;
    mockIdentity.email = validEmail;
    mockFindById.mockResolvedValueOnce(undefined);
    mockFindAllForHID.mockReturnValue(mockIdentity);

    const result = await identityLookup(hid);
    expect(result).toEqual({
      hid: hid,
      email: validEmail,
      migrated: false,
      accounts: [
        {
          id: 1234,
          type: 'seaware_client_id',
        },
        {
          id: 5678,
          type: 'owner_id',
        },
      ],
    });
  });

  it('should throw UnknownIdentityError for a HID without email', async () => {
    const hid = 'noemailhid';
    const validEmail = nextValidEmail();
    const mockIdentity = new AllHIDData();
    mockIdentity.hid = hid;
    mockFindById.mockResolvedValueOnce(undefined);
    mockFindAllForHID.mockReturnValue(mockIdentity);

    await expect(identityLookup(hid)).rejects.toThrow('No such HID: noemailhid');
  });

  it('should throw UnknownIdentityError for a non-existent HID', async () => {
    mockFindById.mockResolvedValueOnce(null);
    await expect(identityLookup('nonExistentHID')).rejects.toThrow('No such HID: nonExistentHID');
  });
});
