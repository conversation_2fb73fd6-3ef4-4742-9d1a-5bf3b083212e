import request from 'superagent';

import { afterAll, beforeAll, describe, expect, it, Mock, vi } from 'vitest';
import { Server } from '../../mock/server.js';
import { X_APP_ID_HEADER, X_APP_KEY_HEADER } from '../../../src/hid/header.js';
import { URI } from '../../../src/hid/admin/admin.search.in.js';
import { AllHIDData, HavenServiceAccountIdType } from 'haven-hid-domain';
import {
  nextHIDToEmail,
  nextHIDToPlotOwner,
  nextHIDToSeaWareClient,
} from 'haven-hid-domain/mock.js';
import { PgHidStore } from 'haven-hid-pgsql-adapter';

const server = new Server();

const appId = 'TEST_APP';
const key = 'ADMIN-KEY';
const body = { email: '<EMAIL>' };
const url = `${server.baseUrl}${URI}`;

const mockHidStore = { findAllForPlotOwnerId: vi.fn(), findAllForSeaWareClientId: vi.fn() };

vi.mock('haven-hid-pgsql-adapter/adapter.js');
(PgHidStore as Mock).mockImplementation(() => mockHidStore);

describe('POST /identity/search Endpoint Tests', () => {
  const makeRequest = async (path: string, appId: string, key: string, body: object) =>
    await request
      .post(path)
      .set(X_APP_ID_HEADER, appId)
      .set(X_APP_KEY_HEADER, key)
      .send(body)
      .ok(() => true);

  beforeAll(async () => {
    await server.start();
  });

  afterAll(async () => {
    await server.stop();
  });

  describe('Search By Account Tests', () => {
    it('should respond with 200 and array of found identities when searching by account', async () => {
      const hidToEmail = nextHIDToEmail();
      const { seaWareClientId } = nextHIDToSeaWareClient({ hid: hidToEmail.hid });
      const { plotOwnerId } = nextHIDToPlotOwner({ hid: hidToEmail.hid });

      const seawareAccount = {
        type: HavenServiceAccountIdType.SEAWARE_CLIENT_ID,
        id: seaWareClientId,
      };
      const ownerAccount = {
        type: HavenServiceAccountIdType.OWNER_ID,
        id: plotOwnerId,
      };

      const accountData = new AllHIDData(hidToEmail.hid);
      accountData.email = hidToEmail.email;
      accountData.plotOwnerId = plotOwnerId;
      accountData.seaWareClientId = seaWareClientId;

      mockHidStore.findAllForPlotOwnerId.mockResolvedValue(accountData);
      mockHidStore.findAllForSeaWareClientId.mockResolvedValue(accountData);

      const { status, body } = await makeRequest(url, appId, key, {
        accounts: [seawareAccount, ownerAccount],
      });

      expect(body).toEqual([
        {
          accounts: [{ id: seaWareClientId, type: HavenServiceAccountIdType.SEAWARE_CLIENT_ID }],
          email: hidToEmail.email.toString(),
          hid: hidToEmail.hid.toString(),
          migrated: false,
        },
        {
          accounts: [{ id: plotOwnerId, type: HavenServiceAccountIdType.OWNER_ID }],
          email: hidToEmail.email.toString(),
          hid: hidToEmail.hid.toString(),
          migrated: false,
        },
      ]);
      expect(status).toBe(200);
    });

    it('should respond with 200 and array of found identities when searching by account and an account is not found', async () => {
      const hidToEmail = nextHIDToEmail();
      const { seaWareClientId } = nextHIDToSeaWareClient({ hid: hidToEmail.hid });
      const { plotOwnerId } = nextHIDToPlotOwner({ hid: hidToEmail.hid });

      const guestAccount = {
        type: HavenServiceAccountIdType.SEAWARE_CLIENT_ID,
        id: seaWareClientId,
      };
      const ownerAccount = {
        type: HavenServiceAccountIdType.OWNER_ID,
        id: plotOwnerId - 1,
      };

      const seawareAccount = new AllHIDData(hidToEmail.hid);
      seawareAccount.email = hidToEmail.email;
      seawareAccount.plotOwnerId = plotOwnerId;
      seawareAccount.seaWareClientId = seaWareClientId;

      const plotAccount = new AllHIDData();
      plotAccount.email = undefined;
      plotAccount.plotOwnerId = plotOwnerId - 1;
      plotAccount.seaWareClientId = undefined;

      mockHidStore.findAllForPlotOwnerId.mockResolvedValue(plotAccount);
      mockHidStore.findAllForSeaWareClientId.mockResolvedValue(seawareAccount);

      const { status, body } = await makeRequest(url, appId, key, {
        accounts: [guestAccount, ownerAccount],
      });

      expect(body).toEqual([
        {
          accounts: [{ id: seaWareClientId, type: HavenServiceAccountIdType.SEAWARE_CLIENT_ID }],
          email: hidToEmail.email.toString(),
          hid: hidToEmail.hid.toString(),
          migrated: false,
        },
      ]);
      expect(status).toBe(200);
    });

    it('should respond with 500 and error details when account lookup throws', async () => {
      const hidToEmail = nextHIDToEmail();
      const { seaWareClientId } = nextHIDToSeaWareClient({ hid: hidToEmail.hid });
      const { plotOwnerId } = nextHIDToPlotOwner({ hid: hidToEmail.hid });

      const guestAccount = {
        type: HavenServiceAccountIdType.SEAWARE_CLIENT_ID,
        id: seaWareClientId,
      };
      const ownerAccount = {
        type: HavenServiceAccountIdType.OWNER_ID,
        id: plotOwnerId - 1,
      };

      const error = new Error('not found');

      const seawareAccount = new AllHIDData(hidToEmail.hid);
      seawareAccount.email = hidToEmail.email;
      seawareAccount.plotOwnerId = plotOwnerId;
      seawareAccount.seaWareClientId = seaWareClientId;

      mockHidStore.findAllForSeaWareClientId.mockResolvedValue(seawareAccount);
      mockHidStore.findAllForPlotOwnerId.mockImplementation(() => Promise.reject(error));

      const { status, body } = await makeRequest(url, appId, key, {
        accounts: [guestAccount, ownerAccount],
      });

      expect(body).toEqual({
        code: 'ERROR',
        message: 'not found',
        statusCode: 500,
      });
      expect(status).toBe(500);
    });

    it('should respond with 401 access denied if missing ADMIN_KEY', async () => {
      const key = '';
      const { status } = await makeRequest(url, appId, key, body);
      expect(status).toBe(401);
    });

    it('should respond with 401 access denied if incorrect ADMIN_KEY', async () => {
      const key = 'incorrect_key';
      const { status } = await makeRequest(url, appId, key, body);
      expect(status).toBe(401);
    });

    it('should respond with 400 when invalid parameters are included in the request', async () => {
      const { status } = await makeRequest(url, appId, key, { invalidParameter: 'llll' });
      expect(status).toBe(400);
    });

    it('should respond with 400 when both email and accounts are included in the request', async () => {
      const { status } = await makeRequest(url, appId, key, {
        email: '<EMAIL>',
        accounts: [
          {
            type: HavenServiceAccountIdType.SEAWARE_CLIENT_ID,
            id: 1234,
          },
        ],
      });
      expect(status).toBe(400);
    });

    it('should respond with 400 when request body is empty', async () => {
      const { status, body } = await makeRequest(url, appId, key, {});
      expect(status).toBe(400);
    });
  });
});
