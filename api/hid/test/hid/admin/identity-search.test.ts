import { beforeEach, describe, expect, it, Mock, vi } from 'vitest';

import {
  identitySearchByAccount,
  identitySearchByEmail,
} from '../../../src/hid/admin/identity-search.js';
import { getDependencies } from '../../../src/dependencies/dependencies.js';
import { AllHIDData, HavenServiceAccountIdType, ValidEmail } from 'haven-hid-domain';
import { nextValidEmail } from 'haven-hid-domain/mock.js';
import { mockHidStore, mockIdentityManagement } from '../../helper.js';

vi.mock('../../../src/dependencies/dependencies');

describe('identitySearch', () => {
  beforeEach(() => {
    vi.resetAllMocks();
    (getDependencies as Mock).mockReturnValue({
      identityManagement: mockIdentityManagement,
      hidStore: mockHidStore,
    });
  });

  describe('identitySearchByEmail tests', () => {
    it('should return filtered and mapped results', async () => {
      const validEmail = nextValidEmail();
      const hid = 'QWE123ASD';

      mockIdentityManagement.search.mockResolvedValue([
        {
          migrated: false,
          accounts: [],
          hid,
          email: validEmail,
        },
        {
          migrated: true,
          accounts: [],
          hid,
          email: validEmail,
        },
        {
          migrated: false,
          accounts: [{ id: 1, type: 'owner_id' }],
          hid,
          email: validEmail,
        },
      ]);

      const results = await identitySearchByEmail(validEmail);
      expect(results).toEqual([
        {
          migrated: true,
          accounts: [],
          email: validEmail.email,
          hid,
        },
        {
          migrated: false,
          accounts: [
            {
              id: 1,
              type: HavenServiceAccountIdType.OWNER_ID,
            },
          ],
          email: validEmail.email,
          hid,
        },
      ]);
    });

    it('should return empty array if no results match the filter', async () => {
      const validEmail = nextValidEmail();
      const hid = 'QWE123ASD';

      mockIdentityManagement.search.mockResolvedValue([
        {
          migrated: false,
          accounts: [],
          validEmail,
          hid,
        },
      ]);

      const results = await identitySearchByEmail(validEmail);
      expect(results).toEqual([]);
    });
  });

  describe('identitySearchByAccount tests', () => {
    const hid = 'ABCD1234';
    const email = '<EMAIL>';
    const identityExists = true;

    const plotAccount = { type: HavenServiceAccountIdType.OWNER_ID, id: 1234 };
    const seawareAccount = { type: HavenServiceAccountIdType.SEAWARE_CLIENT_ID, id: 5678 };
    const accounts = [plotAccount, seawareAccount];

    const allHidData = new AllHIDData(hid);
    allHidData.email = ValidEmail.newFromSafeInput(email);
    allHidData.identityExists = identityExists;

    it('should return an empty array when identities are not found', async () => {
      mockHidStore.findAllForPlotOwnerId.mockReturnValue(undefined);
      mockHidStore.findAllForSeaWareClientId.mockReturnValue(undefined);

      const accounts = [{ type: HavenServiceAccountIdType.OWNER_ID, id: 1234 }];

      expect(await identitySearchByAccount(accounts)).toEqual([]);
    });

    it('should return an array when identities are found', async () => {
      mockHidStore.findAllForPlotOwnerId.mockReturnValue(allHidData);
      mockHidStore.findAllForSeaWareClientId.mockReturnValue(allHidData);

      expect(await identitySearchByAccount(accounts)).toEqual([
        { accounts: [plotAccount], email, hid, migrated: identityExists },
        { accounts: [seawareAccount], email, hid, migrated: identityExists },
      ]);
    });

    it('should throw an error when account lookup throws', async () => {
      const error = new Error('Error finding all for plot owner id');
      mockHidStore.findAllForPlotOwnerId.mockImplementation(() => Promise.reject(error));
      mockHidStore.findAllForSeaWareClientId.mockReturnValue(allHidData);

      expect(() => identitySearchByAccount(accounts)).rejects.toThrowError(error);
    });

    it('should filter out falsy values', async () => {
      mockHidStore.findAllForPlotOwnerId.mockReturnValue(undefined);
      mockHidStore.findAllForSeaWareClientId.mockReturnValue(allHidData);

      expect(await identitySearchByAccount(accounts)).toEqual([
        { accounts: [seawareAccount], email, hid, migrated: identityExists },
      ]);
    });
  });
});
