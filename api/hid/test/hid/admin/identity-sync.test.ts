import {
  AccountState,
  identitySync,
  SyncedHavenIdentity,
} from '../../../src/hid/admin/identity-sync.js';
import { beforeEach, describe, expect, it, Mock, test, vi } from 'vitest';
import {
  AllHIDData,
  HavenServiceAccountIdType,
  MigratedHavenIdentity,
  UnmigratedHavenIdentity,
} from 'haven-hid-domain';
import { getDependencies } from '../../../src/dependencies/dependencies.js';
import {
  mockBlapi,
  mockBlapiAccount,
  mockHidStore,
  mockPlot,
  NO_HID_DATA,
  sampleGuest,
  sampleMigratedIdentity,
  sampleOwner,
  sampleUnmigratedIdentity,
} from '../../helper.js';
import { Guest } from 'haven-identity-blapi-client';
import { Owner } from 'haven-identity-plot-client';
import { nextValidEmail } from 'haven-hid-domain/mock.js';

vi.mock('../../../src/dependencies/dependencies.js');

describe('identitySync function', () => {
  beforeEach(() => {
    (getDependencies as Mock).mockReturnValue({
      blapi: mockBlapi,
      blapiAccount: mockBlapiAccount,
      plot: mockPlot,
      hidStore: mockHidStore,
    });
    mockPlot.fetchProfile.mockResolvedValue(undefined);
    mockBlapi.fetchProfile.mockResolvedValue(undefined);
    mockHidStore.findAllForSeaWareClientId.mockReturnValue(NO_HID_DATA);
    mockHidStore.findAllForPlotOwnerId.mockReturnValue(NO_HID_DATA);
  });

  it('should return MATCHED for a validated Haven identity', async () => {
    const email = nextValidEmail();
    const seawareGuest = sampleGuest(email);
    mockBlapi.fetchProfile.mockResolvedValue(seawareGuest);

    const plotOwner = sampleOwner(email);
    mockPlot.fetchProfile.mockResolvedValue(plotOwner);

    const testIdentity: MigratedHavenIdentity = sampleMigratedIdentity({
      email,
      plotOwner,
      seawareGuest,
    });

    const result = await identitySync(testIdentity);

    const expectedIdentity: SyncedHavenIdentity = {
      migrated: true,
      hid: testIdentity.hid,
      email: testIdentity.email.email,
      title: testIdentity.title,
      firstName: testIdentity.firstName,
      lastName: testIdentity.lastName,
      phoneNumber: testIdentity.phoneNumber,
      passwordStrength: testIdentity.passwordStrength,
      accounts: [
        {
          id: 1234,
          type: HavenServiceAccountIdType.OWNER_ID,
          state: AccountState.MATCHED,
        },
        {
          id: 5678,
          type: HavenServiceAccountIdType.SEAWARE_CLIENT_ID,
          state: AccountState.MATCHED,
        },
      ],
      address: undefined,
      ownerLegalData: {
        address: {
          address1: plotOwner.address?.address1 || '',
          address2: plotOwner.address?.address2 || '',
          address3: plotOwner.address?.address3 || '',
          address4: plotOwner.address?.address4 || '',
          postcode: plotOwner.address?.postcode || '',
        },
        firstName: plotOwner.firstName,
        lastName: plotOwner.lastName,
        name: plotOwner.name,
        title: plotOwner.title,
      },
    };
    expect(result).toEqual(expectedIdentity);
  });

  it('should return UNLINKED_IN_IDENTITY for missing records in identity', async () => {
    const email = nextValidEmail();
    const seawareGuest = sampleGuest();
    const plotOwner = sampleOwner(email);
    mockBlapi.fetchProfile.mockResolvedValue(seawareGuest);
    mockPlot.fetchProfile.mockResolvedValue(plotOwner);
    const testIdentity: MigratedHavenIdentity = sampleMigratedIdentity({ email });

    const result = await identitySync(testIdentity);

    expect(result.accounts).toEqual([
      {
        id: 1234,
        type: HavenServiceAccountIdType.OWNER_ID,
        state: AccountState.UNLINKED_IN_IDENTITY,
      },
      {
        id: 5678,
        type: HavenServiceAccountIdType.SEAWARE_CLIENT_ID,
        state: AccountState.UNLINKED_IN_IDENTITY,
      },
    ]);
  });

  it('should return OWNED_BY_OTHER_IDENTITY for records with a different email to the identity seaware account', async () => {
    const email = nextValidEmail();
    const seawareGuest = sampleGuest();
    const otherEmail = nextValidEmail();
    const otherIdentity: MigratedHavenIdentity = sampleMigratedIdentity({
      email: otherEmail,
      seawareGuest,
    });
    const testIdentity: MigratedHavenIdentity = sampleMigratedIdentity({ email });
    mockBlapi.fetchProfile.mockResolvedValue(seawareGuest);
    mockPlot.fetchProfile.mockResolvedValue(undefined);
    mockHidStore.findAllForSeaWareClientId.mockImplementation(
      () => new AllHIDData(otherIdentity.hid),
    );

    const result = await identitySync(testIdentity);

    expect(result.accounts).toEqual([
      {
        id: seawareGuest.clientId,
        type: HavenServiceAccountIdType.SEAWARE_CLIENT_ID,
        state: AccountState.OWNED_BY_OTHER_IDENTITY,
        hid: otherIdentity.hid,
      },
    ]);
  });

  it('should return OWNED_BY_OTHER_IDENTITY for records with a different email to the identity plot account', async () => {
    const email = nextValidEmail();
    const plotOwner = sampleOwner();
    const otherEmail = nextValidEmail();
    const otherIdentity: MigratedHavenIdentity = sampleMigratedIdentity({
      email: otherEmail,
      plotOwner,
    });
    const testIdentity: MigratedHavenIdentity = sampleMigratedIdentity({ email });
    mockBlapi.fetchProfile.mockResolvedValue(undefined);
    mockPlot.fetchProfile.mockResolvedValue(plotOwner);
    mockHidStore.findAllForPlotOwnerId.mockImplementation(() => new AllHIDData(otherIdentity.hid));

    const result = await identitySync(testIdentity);

    expect(result.accounts).toEqual([
      {
        id: plotOwner.ownerId,
        type: HavenServiceAccountIdType.OWNER_ID,
        state: AccountState.OWNED_BY_OTHER_IDENTITY,
        hid: otherIdentity.hid,
      },
    ]);
  });

  it('should return UNREGISTERED for missing records in owner', async () => {
    const plotOwner = sampleOwner();
    const testIdentity: MigratedHavenIdentity = sampleMigratedIdentity({ plotOwner });
    mockBlapi.fetchProfile.mockResolvedValue(undefined);
    mockPlot.fetchProfile.mockResolvedValue(undefined);
    mockPlot.fetchProfileById.mockResolvedValue(undefined);

    const result = await identitySync(testIdentity);

    expect(result.accounts).toEqual([
      {
        id: 1234,
        type: HavenServiceAccountIdType.OWNER_ID,
        state: AccountState.UNREGISTERED,
      },
    ]);
  });

  it('should return EMAIL_CHANGED for different email in owner', async () => {
    const email = nextValidEmail();
    const otherEmail = nextValidEmail();
    const plotOwner = sampleOwner(otherEmail);
    const testIdentity: MigratedHavenIdentity = sampleMigratedIdentity({
      email,
      plotOwner,
    });
    mockBlapi.fetchProfile.mockResolvedValue(undefined);
    mockPlot.fetchProfile.mockResolvedValue(undefined);
    mockPlot.fetchProfileById.mockResolvedValue(plotOwner);

    const result = await identitySync(testIdentity);

    expect(result.accounts).toEqual([
      {
        id: 1234,
        type: HavenServiceAccountIdType.OWNER_ID,
        state: AccountState.EMAIL_CHANGED,
      },
    ]);
  });

  it('should return UNREGISTERED for missing records in seaware', async () => {
    const seawareGuest = sampleGuest();
    const testIdentity: MigratedHavenIdentity = sampleMigratedIdentity({ seawareGuest });
    mockPlot.fetchProfile.mockResolvedValue(undefined);
    mockBlapi.fetchProfile.mockResolvedValue(undefined);
    mockBlapiAccount.fetchProfileById.mockResolvedValue(undefined);

    const result = await identitySync(testIdentity);

    expect(result.accounts).toEqual([
      {
        id: 5678,
        type: HavenServiceAccountIdType.SEAWARE_CLIENT_ID,
        state: AccountState.UNREGISTERED,
      },
    ]);
  });

  it('should return EMAIL_CHANGED for different email in seaware', async () => {
    const email = nextValidEmail();
    const otherEmail = nextValidEmail();
    const seawareGuest = sampleGuest(otherEmail);
    const testIdentity: MigratedHavenIdentity = sampleMigratedIdentity({
      email,
      seawareGuest,
    });
    mockPlot.fetchProfile.mockResolvedValue(undefined);
    mockBlapi.fetchProfile.mockResolvedValue(undefined);
    mockBlapiAccount.fetchProfileById.mockResolvedValue(seawareGuest);

    const result = await identitySync(testIdentity);

    expect(result.accounts).toEqual([
      {
        id: 5678,
        type: HavenServiceAccountIdType.SEAWARE_CLIENT_ID,
        state: AccountState.EMAIL_CHANGED,
      },
    ]);
  });

  it('should return MISMATCH for records in owner/seaware that do not match identity', async () => {
    const email = nextValidEmail();
    const seawareGuest = sampleGuest(email);
    const plotOwner = sampleOwner(email);
    const testIdentity: MigratedHavenIdentity = sampleMigratedIdentity({
      email,
      plotOwner,
      seawareGuest,
    });
    mockBlapi.fetchProfile.mockResolvedValue({
      clientId: 5677,
      email: testIdentity.email.email,
    });
    mockPlot.fetchProfile.mockResolvedValue({
      ownerId: 1235,
      email: testIdentity.email.email,
    });

    const result = await identitySync(testIdentity);

    expect(result.accounts).toEqual([
      {
        id: 1234,
        type: HavenServiceAccountIdType.OWNER_ID,
        state: AccountState.MISMATCH,
      },
      {
        id: 5678,
        type: HavenServiceAccountIdType.SEAWARE_CLIENT_ID,
        state: AccountState.MISMATCH,
      },
    ]);
  });

  it('should return ERROR if lookups failed', async () => {
    const plotOwner: Owner = sampleOwner();
    const seawareGuest: Guest = sampleGuest();
    const testIdentity: UnmigratedHavenIdentity = sampleUnmigratedIdentity({
      plotOwner,
      seawareGuest,
    });
    mockPlot.fetchProfile.mockRejectedValue(new Error('Error looking up owner'));
    mockBlapi.fetchProfile.mockRejectedValue(new Error('Error looking up guest'));

    const result = await identitySync(testIdentity);

    const expectedIdentity: SyncedHavenIdentity = {
      migrated: false,
      hid: testIdentity.hid,
      email: testIdentity.email.email,
      title: undefined,
      firstName: undefined,
      lastName: undefined,
      phoneNumber: undefined,
      passwordStrength: undefined,
      accounts: [
        {
          id: plotOwner.ownerId,
          type: HavenServiceAccountIdType.OWNER_ID,
          state: AccountState.ERROR,
        },
        {
          id: seawareGuest.clientId,
          type: HavenServiceAccountIdType.SEAWARE_CLIENT_ID,
          state: AccountState.ERROR,
        },
      ],
      address: undefined,
    };
    expect(result).toEqual(expectedIdentity);
  });

  it('should handle errors even if lookups failed for no accounts in identity', async () => {
    mockPlot.fetchProfile.mockRejectedValue(new Error('Error looking up owner'));
    mockBlapi.fetchProfile.mockRejectedValue(new Error('Error looking up guest'));

    const testIdentity: UnmigratedHavenIdentity = sampleUnmigratedIdentity();

    const result = await identitySync(testIdentity);

    const expectedIdentity: SyncedHavenIdentity = {
      migrated: false,
      hid: testIdentity.hid,
      email: testIdentity.email.email,
      title: undefined,
      firstName: undefined,
      lastName: undefined,
      phoneNumber: undefined,
      passwordStrength: undefined,
      accounts: [
        {
          id: -1,
          type: HavenServiceAccountIdType.OWNER_ID,
          state: AccountState.ERROR,
        },
        {
          id: -1,
          type: HavenServiceAccountIdType.SEAWARE_CLIENT_ID,
          state: AccountState.ERROR,
        },
      ],
      address: undefined,
    };
    expect(result).toEqual(expectedIdentity);
  });

  it('should return populate details for unmigrated identity from seaware rather than owners', async () => {
    const email = nextValidEmail();
    const seawareGuest: Guest = sampleGuest(email);
    const plotOwner: Owner = sampleOwner(email);
    const testIdentity: UnmigratedHavenIdentity = sampleUnmigratedIdentity({
      email,
      plotOwner,
      seawareGuest,
    });
    mockBlapi.fetchProfile.mockResolvedValue(seawareGuest);
    mockPlot.fetchProfile.mockResolvedValue({
      ...plotOwner,
      email: testIdentity.email.email,
    });

    const result = await identitySync(testIdentity);

    const expectedIdentity: SyncedHavenIdentity = {
      migrated: false,
      hid: testIdentity.hid,
      email: testIdentity.email.email,
      title: seawareGuest.title,
      firstName: seawareGuest.firstName,
      lastName: seawareGuest.lastName,
      phoneNumber: seawareGuest.phoneNumber,
      passwordStrength: undefined,
      accounts: [
        {
          id: 1234,
          type: HavenServiceAccountIdType.OWNER_ID,
          state: AccountState.MATCHED,
        },
        {
          id: 5678,
          type: HavenServiceAccountIdType.SEAWARE_CLIENT_ID,
          state: AccountState.MATCHED,
        },
      ],
      address: undefined,
      ownerLegalData: {
        address: {
          address1: plotOwner.address?.address1 || '',
          address2: plotOwner.address?.address2 || '',
          address3: plotOwner.address?.address3 || '',
          address4: plotOwner.address?.address4 || '',
          postcode: plotOwner.address?.postcode || '',
        },
        firstName: plotOwner.firstName,
        lastName: plotOwner.lastName,
        name: plotOwner.name,
        title: plotOwner.title,
      },
    };
    expect(result).toEqual(expectedIdentity);
  });

  it('should return populate details for unmigrated identity from seaware if matches', async () => {
    const email = nextValidEmail();
    const seawareGuest: Guest = sampleGuest(email);
    const testIdentity: UnmigratedHavenIdentity = sampleUnmigratedIdentity({
      email: email,
      seawareGuest,
    });
    mockBlapi.fetchProfile.mockResolvedValue(seawareGuest);
    mockPlot.fetchProfile.mockResolvedValue(undefined);

    const result = await identitySync(testIdentity);

    const expectedIdentity: SyncedHavenIdentity = {
      migrated: false,
      hid: testIdentity.hid,
      email: testIdentity.email.email,
      title: seawareGuest.title,
      firstName: seawareGuest.firstName,
      lastName: seawareGuest.lastName,
      phoneNumber: seawareGuest.phoneNumber,
      passwordStrength: undefined,
      accounts: [
        {
          id: 5678,
          type: HavenServiceAccountIdType.SEAWARE_CLIENT_ID,
          state: AccountState.MATCHED,
        },
      ],
      address: undefined,
    };
    expect(result).toEqual(expectedIdentity);
  });

  it('should return populate details for unmigrated identity from owners', async () => {
    const plotOwner: Owner = sampleOwner();
    const testIdentity: UnmigratedHavenIdentity = sampleUnmigratedIdentity({ plotOwner });
    mockPlot.fetchProfile.mockResolvedValue({
      ...plotOwner,
      email: testIdentity.email.email,
    });
    mockBlapi.fetchProfile.mockResolvedValue(undefined);

    const result = await identitySync(testIdentity);

    const expectedIdentity: SyncedHavenIdentity = {
      migrated: false,
      hid: testIdentity.hid,
      email: testIdentity.email.email,
      title: plotOwner.title,
      firstName: plotOwner.firstName,
      lastName: plotOwner.lastName,
      phoneNumber: plotOwner.mobilePhone,
      passwordStrength: undefined,
      accounts: [
        {
          id: 1234,
          type: HavenServiceAccountIdType.OWNER_ID,
          state: AccountState.MATCHED,
        },
      ],
      address: undefined,
      ownerLegalData: {
        address: {
          address1: plotOwner.address?.address1 || '',
          address2: plotOwner.address?.address2 || '',
          address3: plotOwner.address?.address3 || '',
          address4: plotOwner.address?.address4 || '',
          postcode: plotOwner.address?.postcode || '',
        },
        firstName: plotOwner.firstName,
        lastName: plotOwner.lastName,
        name: plotOwner.name,
        title: plotOwner.title,
      },
    };
    expect(result).toEqual(expectedIdentity);
  });

  it('should return populate details for unmigrated identity from owners and use dayPhone', async () => {
    const plotOwner: Owner = sampleOwner();
    const testIdentity: UnmigratedHavenIdentity = sampleUnmigratedIdentity({ plotOwner });
    mockPlot.fetchProfile.mockResolvedValue({
      ...plotOwner,
      mobilePhone: undefined,
      email: testIdentity.email.email,
    });
    mockBlapi.fetchProfile.mockResolvedValue(undefined);

    const result = await identitySync(testIdentity);

    const expectedIdentity: SyncedHavenIdentity = {
      migrated: false,
      hid: testIdentity.hid,
      email: testIdentity.email.email,
      title: plotOwner.title,
      firstName: plotOwner.firstName,
      lastName: plotOwner.lastName,
      phoneNumber: plotOwner.dayPhone,
      passwordStrength: undefined,
      accounts: [
        {
          id: 1234,
          type: HavenServiceAccountIdType.OWNER_ID,
          state: AccountState.MATCHED,
        },
      ],
      address: undefined,
      ownerLegalData: {
        address: {
          address1: plotOwner.address?.address1 || '',
          address2: plotOwner.address?.address2 || '',
          address3: plotOwner.address?.address3 || '',
          address4: plotOwner.address?.address4 || '',
          postcode: plotOwner.address?.postcode || '',
        },
        firstName: plotOwner.firstName,
        lastName: plotOwner.lastName,
        name: plotOwner.name,
        title: plotOwner.title,
      },
    };
    expect(result).toEqual(expectedIdentity);
  });

  it('should return populate details for unmigrated identity from owners and use eveningPhone', async () => {
    const plotOwner: Owner = sampleOwner();
    const testIdentity: UnmigratedHavenIdentity = sampleUnmigratedIdentity({ plotOwner });
    mockPlot.fetchProfile.mockResolvedValue({
      ...plotOwner,
      email: testIdentity.email.email,
      mobilePhone: undefined,
      dayPhone: undefined,
    });
    mockBlapi.fetchProfile.mockResolvedValue(undefined);

    const result = await identitySync(testIdentity);

    const expectedIdentity: SyncedHavenIdentity = {
      migrated: false,
      hid: testIdentity.hid,
      email: testIdentity.email.email,
      title: plotOwner.title,
      firstName: plotOwner.firstName,
      lastName: plotOwner.lastName,
      phoneNumber: plotOwner.eveningPhone,
      passwordStrength: undefined,
      accounts: [
        {
          id: 1234,
          type: HavenServiceAccountIdType.OWNER_ID,
          state: AccountState.MATCHED,
        },
      ],
      address: undefined,
      ownerLegalData: {
        address: {
          address1: plotOwner.address?.address1 || '',
          address2: plotOwner.address?.address2 || '',
          address3: plotOwner.address?.address3 || '',
          address4: plotOwner.address?.address4 || '',
          postcode: plotOwner.address?.postcode || '',
        },
        firstName: plotOwner.firstName,
        lastName: plotOwner.lastName,
        name: plotOwner.name,
        title: plotOwner.title,
      },
    };
    expect(result).toEqual(expectedIdentity);
  });
});
