import { afterAll, beforeAll, describe, expect, it, Mock, vi } from 'vitest';

import request from 'superagent';
import { Server } from '../../mock/server.js';
import { X_APP_ID_HEADER, X_APP_KEY_HEADER } from '../../../src/hid/header.js';
import { URI } from '../../../src/hid/admin/admin.erase-identity.in.js';
import { inMemoryHIDDomain } from 'haven-hid-domain';
import { PgRightToBeForgotten } from 'haven-hid-pgsql-adapter';
import {
  nextHIDToEmail,
  nextHIDToPassword,
  nextHIDToPlotOwner,
  nextHIDToProfile,
  nextHIDToSeaWareClient,
  nextTx,
} from 'haven-hid-domain/mock.js';

const server = new Server();

const TEST_APP_ID = 'TEST_APP';
const TEST_ADMIN_KEY = 'ADMIN-KEY';
const body = { email: '<EMAIL>' };
const url = `${server.baseUrl}${URI}`;

vi.mock('haven-hid-pgsql-adapter/adapter.js');
const hidDomain = inMemoryHIDDomain();
const mockPersistence = hidDomain.rightToBeForgotten;
(PgRightToBeForgotten as Mock).mockImplementation(() => mockPersistence);

const returnValue = <T>(value: T): T => value;
const throwError = <T extends Error>(error: T): never => {
  throw error;
};

describe('Erase Identity', () => {
  const makeRequest = async (
    path: string,
    appId: string,
    adminKey: string,
    body: { email: string },
  ) =>
    await request
      .post(path)
      .set(X_APP_ID_HEADER, appId)
      .set(X_APP_KEY_HEADER, adminKey)
      .send(body)
      .ok(() => true);

  beforeAll(async () => {
    await server.start();
  });

  afterAll(async () => {
    await server.stop();
  });

  describe('Erase Identity  API tests', () => {
    it('returns 200 response', async () => {
      const { status } = await makeRequest(url, TEST_APP_ID, TEST_ADMIN_KEY, body);
      expect(status).toBe(200);
    });

    it('returns access denied if missing ADMIN_KEY', async () => {
      const { status } = await makeRequest(url, TEST_APP_ID, '', body);
      expect(status).toBe(401);
    });

    it('returns access denied if incorrect ADMIN_KEY', async () => {
      const { status } = await makeRequest(url, TEST_APP_ID, 'MY-KEY', body);
      expect(status).toBe(401);
    });

    it('returns 400 for bad requests', async () => {
      // @ts-ignore
      const { status } = await makeRequest(url, TEST_APP_ID, TEST_ADMIN_KEY, {});
      expect(status).toBe(400);
    });
  });

  describe('integration tests', () => {
    describe('should delete the identity for a given email and respond with 200', async () => {
      const tx = nextTx();
      const hid = await hidDomain.generator.next();
      const hidToEmail = nextHIDToEmail({ hid });
      const hidToPlotOwner = nextHIDToPlotOwner({
        hid: hidToEmail.hid,
      });
      const hidToSeawareClient = nextHIDToSeaWareClient({
        hid: hidToEmail.hid,
      });
      const profile = nextHIDToProfile({
        hid: hidToEmail.hid,
      });
      const password = nextHIDToPassword({
        hid: hidToEmail.hid,
      });
      const hidObfuscateSpy = vi.spyOn(hidDomain.hidAuditStore, 'obfuscate');
      const authenticateObfuscateSpy = vi.spyOn(hidDomain.authenticateAuditStore, 'obfuscate');
      let status: number;

      hidDomain.emailStore.add(hidToEmail, tx);
      hidDomain.plotStore.add(hidToPlotOwner, tx);
      hidDomain.seaWareStore.add(hidToSeawareClient, tx);
      hidDomain.profileStore.add(profile, tx);
      hidDomain.passwordStore.add(password, tx);

      beforeAll(async () => {
        status = (
          await makeRequest(url, TEST_APP_ID, TEST_ADMIN_KEY, {
            email: hidToEmail.email.email,
          })
        ).status;
      });

      it('should delete hidToEmail', async () => {
        const emailCheck = await hidDomain.hidStore.findAllForEmail(hidToEmail.email);
        expect(emailCheck.hid).toEqual(undefined);
      });

      it('should delete hidToPlot', async () => {
        const plotCheck = await hidDomain.hidStore.findAllForPlotOwnerId(
          hidToPlotOwner.plotOwnerId,
        );
        expect(plotCheck.hid).toEqual(undefined);
      });

      it('should delete hidToSeaware', async () => {
        const seawareCheck = await hidDomain.hidStore.findAllForSeaWareClientId(
          hidToSeawareClient.seaWareClientId,
        );
        expect(seawareCheck.hid).toEqual(undefined);
      });

      it('should delete hidToProfile', async () => {
        const profileCheck = await hidDomain.profileStore.findByHID(hidToEmail.hid);
        expect(profileCheck).toEqual(undefined);
      });

      it('should delete hidToPassword', async () => {
        const passwordCheck = await hidDomain.passwordStore.findByHID(hidToEmail.hid);
        expect(passwordCheck).toBeNull();
      });

      it('should delete hid', async () => {
        const hidCheck = await hidDomain.hidStore.hid.findByHID(hidToEmail.hid);
        expect(hidCheck).toEqual(undefined);
      });

      it('should obfuscate hid audit', async () => {
        expect(hidObfuscateSpy).toBeCalledWith({
          email: hidToEmail.email.email,
          hid: hidToEmail.hid,
        });
      });

      it('should obfuscate authenticate audit', async () => {
        expect(authenticateObfuscateSpy).toBeCalledWith({
          email: hidToEmail.email.email,
          hid: hidToEmail.hid,
        });
      });

      it('should return 200', async () => {
        expect(status).toBe(200);
      });
    });

    describe('should do nothing when email not found and respond with 200', async () => {
      const tx = nextTx();
      const hid = await hidDomain.generator.next();
      const hidToEmail = nextHIDToEmail({ hid });
      const hidToPlotOwner = nextHIDToPlotOwner({
        hid: hidToEmail.hid,
      });
      const hidToSeawareClient = nextHIDToSeaWareClient({
        hid: hidToEmail.hid,
      });
      const profile = nextHIDToProfile({
        hid: hidToEmail.hid,
      });
      const password = nextHIDToPassword({
        hid: hidToEmail.hid,
      });
      let status: number;

      hidDomain.plotStore.add(hidToPlotOwner, tx);
      hidDomain.seaWareStore.add(hidToSeawareClient, tx);
      hidDomain.profileStore.add(profile, tx);
      hidDomain.passwordStore.add(password, tx);

      beforeAll(async () => {
        status = (
          await makeRequest(url, TEST_APP_ID, TEST_ADMIN_KEY, {
            email: hidToEmail.email.email,
          })
        ).status;
      });

      it('should not find hidToEmail', async () => {
        const emailCheck = await hidDomain.hidStore.findAllForEmail(hidToEmail.email);
        expect(emailCheck.hid).toEqual(undefined);
      });

      it('should not delete hidToPlot', async () => {
        const plotCheck = await hidDomain.hidStore.findAllForPlotOwnerId(
          hidToPlotOwner.plotOwnerId,
        );
        expect(plotCheck.hid).toEqual(hid);
      });

      it('should not delete hidToSeaware', async () => {
        const seawareCheck = await hidDomain.hidStore.findAllForSeaWareClientId(
          hidToSeawareClient.seaWareClientId,
        );
        expect(seawareCheck.hid).toEqual(hid);
      });

      it('should not delete hidToProfile', async () => {
        const profileCheck = await hidDomain.profileStore.findByHID(hidToEmail.hid);
        expect(profileCheck).toEqual(profile);
      });

      it('should not delete hidToPassword', async () => {
        const passwordCheck = await hidDomain.passwordStore.findByHID(hidToEmail.hid);
        expect(passwordCheck).toEqual(password);
      });

      it('should not delete hid', async () => {
        const hidCheck = await hidDomain.hidStore.hid.findByHID(hidToEmail.hid);
        expect(hidCheck).toEqual(hid);
      });

      it('should return 200', async () => {
        expect(status).toBe(200);
      });
    });
  });
});
