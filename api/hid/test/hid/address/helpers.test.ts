import { describe, expect, it } from 'vitest';
import { formatToIdentityAddress } from '../../../src/hid/address/helpers.js';
import { Address } from '../../../src/hid/profile/helpers.js';
import { HIDAddress } from 'haven-hid-domain';

describe('formatToIdentityAddress', () => {
  it('should return undefined if input address is undefined', () => {
    const result = formatToIdentityAddress(undefined);
    expect(result).toBeUndefined();
  });

  it('should transform a complete address with all fields', () => {
    const input: Address = {
      line1: '1 Park Ln',
      line2: 'Suite 101',
      city: 'Hemel Hempstead',
      county: 'Hertfordshire',
      postcode: 'HP2 4YJ',
      countryCode: 'GB',
    };

    const expected: HIDAddress = {
      address_line1: '1 Park Ln',
      address_line2: 'Suite 101',
      address_city: 'Hemel Hempstead',
      address_county: 'Hertfordshire',
      address_postcode: 'HP2 4YJ',
      address_country: 'GB',
    };

    const result = formatToIdentityAddress(input);
    expect(result).toEqual(expected);
  });

  it('should handle missing optional line2 by setting empty string', () => {
    const input: Address = {
      line1: '1 Park Ln',
      city: 'Hemel Hempstead',
      county: 'Hertfordshire',
      postcode: 'HP2 4YJ',
      countryCode: 'GB',
    };

    const expected: HIDAddress = {
      address_line1: '1 Park Ln',
      address_city: 'Hemel Hempstead',
      address_county: 'Hertfordshire',
      address_postcode: 'HP2 4YJ',
      address_country: 'GB',
    };

    const result = formatToIdentityAddress(input);
    expect(result).toEqual(expected);
  });

  it('should handle missing optional county by setting empty string', () => {
    const input: Address = {
      line1: '1 Park Ln',
      line2: 'Floor 2',
      city: 'Hemel Hempstead',
      postcode: 'HP2 4YJ',
      countryCode: 'GB',
    };

    const expected: HIDAddress = {
      address_line1: '1 Park Ln',
      address_line2: 'Floor 2',
      address_city: 'Hemel Hempstead',
      address_postcode: 'HP2 4YJ',
      address_country: 'GB',
    };

    const result = formatToIdentityAddress(input);
    expect(result).toEqual(expected);
  });

  it('should handle input with minimum required fields', () => {
    const input: Address = {
      line1: '1 Park Ln',
      city: 'Hemel Hempstead',
      postcode: 'HP2 4YJ',
      countryCode: 'GB',
    };

    const expected: HIDAddress = {
      address_line1: '1 Park Ln',
      address_city: 'Hemel Hempstead',
      address_postcode: 'HP2 4YJ',
      address_country: 'GB',
    };

    const result = formatToIdentityAddress(input);
    expect(result).toEqual(expected);
  });

  it('should not add id or address_effective_from fields', () => {
    const input: Address = {
      line1: '1 Park Ln',
      city: 'Hemel Hempstead',
      postcode: 'HP2 4YJ',
      countryCode: 'GB',
    };

    const result = formatToIdentityAddress(input);
    expect(result?.id).toBeUndefined();
    expect(result?.address_effective_from).toBeUndefined();
  });
});
