import { afterAll, beforeAll, beforeEach, describe, expect, it, Mock, vi } from 'vitest';
import request, { Response } from 'superagent';
import { Server } from '../../mock/server.js';
import { X_APP_ID_HEADER, X_APP_KEY_HEADER } from '../../../src/hid/header.js';
import {
  HavenServiceAccount,
  HavenServiceAccountIdType,
  HavenServiceAccountIdType as IdType,
  HID,
  UPDATE_PROFILE_SOURCE_SUFFIX,
  HavenIdentity,
} from 'haven-hid-domain';
import * as ulid from 'ulid';
import {
  nextHID,
  nextHIDToEmail,
  nextHIDToProfile,
  nextIdentity,
  nextInvalidEmail,
  nextPlotOwnerId,
  nextPlotOwnerWithEmail,
  nextSeaWareClientId,
  nextSeaWareClientWithEmail,
  nextValidEmail,
} from 'haven-hid-domain/mock.js';
import {
  PgHavenIdentityTransactionalStore,
  PgHIDForEmailPlotAndSeaWare,
} from 'haven-hid-pgsql-adapter/adapter.js';
import { jwtVerify } from 'jose';
import { BlapiClientError, Address } from 'haven-identity-blapi-client';
import { AxiosError, AxiosResponse } from 'axios';
import {
  expectEmail,
  getIdentity,
  hidForEmailPlotAndSeaWare,
  identityStore,
  lookupEmailAudits,
  lookupProfileAudits,
  mockBlapi,
  mockBlapiAccount,
  mockPlot,
  setupHIDAndEmail,
  setupIdentity,
} from '../../helper.js';

const mockUnleash = { isEnabled: vi.fn().mockReturnValue(false) };
vi.mock('../../../src/services/unleash.js', async () => ({
  ...(await vi.importActual<typeof import('../../../src/services/unleash.js')>(
    '../../../src/services/unleash.js',
  )),
  createFeatureFlagChecker: vi.fn(() => mockUnleash),
}));

vi.mock('haven-identity-plot-client');
vi.mock('haven-hid-pgsql-adapter/adapter.js');
vi.mock('@havenengineering/module-haven-logging');

(PgHavenIdentityTransactionalStore as Mock).mockImplementation(() => identityStore);
(PgHIDForEmailPlotAndSeaWare as Mock).mockImplementation(() => hidForEmailPlotAndSeaWare);

vi.mock('haven-identity-blapi-client', async () => {
  const mod = await vi.importActual<typeof import('haven-identity-blapi-client')>(
    'haven-identity-blapi-client',
  );
  return {
    ...mod,
    BLAPI: vi.fn().mockImplementation(() => mockBlapi),
    BLAPIAccount: vi.fn().mockImplementation(() => mockBlapiAccount),
  };
});

vi.mock('haven-identity-plot-client', async () => {
  const mod = await vi.importActual<typeof import('haven-identity-plot-client')>(
    'haven-identity-plot-client',
  );
  return {
    ...mod,
    Plot: vi.fn().mockImplementation(() => mockPlot),
  };
});

const mocks = vi.hoisted(() => ({
  sendProfileChangedEmail: vi.fn(),
  sendEmailChangedEmail: vi.fn(),
}));

vi.mock('haven-hid-domain', async (original) => {
  const mod: Object = await original();
  return {
    ...mod,
    sendProfileChangedEmail: mocks.sendProfileChangedEmail,
    sendEmailChangedEmail: mocks.sendEmailChangedEmail,
  };
});

vi.mock('jose');
const mockVerify = jwtVerify as Mock;

const server = new Server();

const TEST_APP_ID = 'TEST_APP';
const TEST_MANAGEMENT_KEY = 'MANAGEMENT-KEY';
const accessToken = '<jwt-token>';
const headers = {
  appId: TEST_APP_ID,
  appKey: TEST_MANAGEMENT_KEY,
  jwt: accessToken,
};

describe('Update Profile', () => {
  const getUrl = (hid: string): string => `${server.baseUrl}/identity/${hid}`;
  type PayloadWithoutIds = {
    email?: string;
    address?: Address;
    title?: string;
    name?: string;
    firstName?: string;
    lastName?: string;
    phoneNumber?: string;
    alternatePhoneNumber?: string;
  };
  type Payload = PayloadWithoutIds & {
    idType: IdType;
    id: number;
  };
  const makeRequest = async (
    path: string,
    headers: {
      appId?: string;
      appKey?: string;
      jwt?: string;
    },
    body: Payload | PayloadWithoutIds,
  ): Promise<Response> => {
    const patch = request.patch(path).ok(() => true);
    if (headers.appKey) {
      patch.set(X_APP_KEY_HEADER, headers.appKey);
    }
    if (headers.appId) {
      patch.set(X_APP_ID_HEADER, headers.appId);
    }
    if (headers.jwt) {
      patch.set('Authorization', `Bearer ${headers.jwt}`);
    }
    return patch.send(body);
  };

  beforeAll(async () => {
    await server.start();
  });

  afterAll(async () => {
    await server.stop();
  });

  beforeEach(() => {
    vi.clearAllMocks();
    vi.resetAllMocks();
  });

  describe('Migrated User', async () => {
    describe('Email and Address', async () => {
      let identity: HavenIdentity;
      let hid: HID;
      let url: string;
      let payload: Payload;

      beforeEach(async () => {
        const changedEmail = nextValidEmail();
        const currentEmail = nextValidEmail();
        const plotOwnerId = nextPlotOwnerId();
        payload = {
          email: changedEmail.email,
          id: plotOwnerId,
          idType: IdType.OWNER_ID,
        };
        identity = await setupIdentity({
          email: currentEmail,
          plotOwnerId: plotOwnerId,
        });
        hid = identity.hid;
        url = getUrl(hid);
        mockMigratedToken(hid, [
          {
            id: payload.id as number,
            type: payload.idType as IdType,
          },
        ]);
      });

      it('returns 200 response', async () => {
        const response = await makeRequest(url, headers, payload);

        expect(mocks.sendEmailChangedEmail).toHaveBeenCalledWith({
          email: payload.email,
          templateId: process.env.EMAIL_CHANGED_TEMPLATE_ID || '',
          name: `${identity.firstName} ${identity.lastName}`,
          oldEmail: identity.email.email,
        });
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(response.body).toEqual({
          hid: identity.hid,
        });
        expect(response.status).toBe(200);
      });

      it('returns 200 response when no id/idType provided', async () => {
        const newEmail = nextValidEmail().email;
        const response = await makeRequest(url, headers, { email: newEmail });

        expect(mocks.sendEmailChangedEmail).toHaveBeenCalledWith({
          email: newEmail,
          templateId: process.env.EMAIL_CHANGED_TEMPLATE_ID || '',
          name: `${identity.firstName} ${identity.lastName}`,
          oldEmail: identity.email.email,
        });
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(response.body).toEqual({
          hid: identity.hid,
        });
        expect(response.status).toBe(200);
      });

      it('returns 422 response for missing user', async () => {
        const unknownHID = nextHID();
        const url = getUrl(unknownHID);
        mockMigratedToken(unknownHID, [
          {
            id: payload.id,
            type: payload.idType,
          },
        ]);

        const response = await makeRequest(url, headers, payload);

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(response.body).toEqual({
          code: 'INVALID_HID',
          message: 'No such HID',
          statusCode: 422,
        });
        expect(response.status).toBe(422);
      });

      it('returns access denied if missing MANAGEMENT_KEY', async () => {
        const { status, body } = await makeRequest(url, { ...headers, appKey: '' }, payload);
        expect(status).toBe(401);
        expect(body.code).toBe('NOT_AUTHORISED');
        expect(body.message).toBe('x-app-key header is required');
        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
      });

      it('returns access denied if incorrect MANAGEMENT_KEY', async () => {
        const { status, body } = await makeRequest(url, { ...headers, appKey: 'Invalid' }, payload);
        expect(status).toBe(401);
        expect(body.code).toBe('NOT_AUTHORISED');
        expect(body.message).toBe('invalid x-app-key header');
        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
      });

      it('returns 400 for request with no id but idType is there', async () => {
        // @ts-ignore
        const { status, body } = await makeRequest(url, headers, {
          idType: HavenServiceAccountIdType.SEAWARE_CLIENT_ID,
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(status).toBe(400);
        expect(body.code).toBe('VALIDATION');
        expect(body.message).toBe(`Missing id for idType="seaware_client_id"`);
      });

      it('returns 400 for request with no idType but id is there', async () => {
        // @ts-ignore
        const { status, body } = await makeRequest(url, headers, {
          id: 1000,
        });
        expect(status).toBe(400);
        expect(body.code).toBe('VALIDATION');
        expect(body.message).toBe(`Missing idType for id="1000"`);
        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
      });

      it('returns 400 for request with missing address elements', async () => {
        const { status, body } = await makeRequest(url, headers, {
          // @ts-ignore
          address: {},
          id: 1234,
          idType: HavenServiceAccountIdType.SEAWARE_CLIENT_ID,
        });
        expect(status).toBe(400);
        expect(body.code).toBe('VALIDATION');
        expect(body.message).toBe(`body/address must have required property 'line1'`);
        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
      });

      it('returns 400 for bad HID', async () => {
        const url = `${server.baseUrl}/identity/aaaaa`;
        const { status, body } = await makeRequest(url, headers, payload);
        expect(status).toBe(400);
        expect(body.code).toBe('VALIDATION');
        expect(body.message).toBe('params/hid must match pattern "[A-Z0-9]{12}"');
        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
      });

      it('returns 400 for an email that does not pass validation', async () => {
        const email = nextInvalidEmail();
        const { status, body } = await makeRequest(url, headers, {
          ...payload,
          email,
        });
        expect(status).toBe(400);
        expect(body.code).toBe('VALIDATION');
        expect(body.message).toBe('body/email is not a valid email');
        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
      });

      it('returns 400 for an unknown id type', async () => {
        const { status, body } = await makeRequest(url, headers, {
          ...payload,
          idType: 'xxx' as HavenServiceAccountIdType,
        });
        expect(status).toBe(400);
        expect(body.code).toBe('VALIDATION');
        expect(body.message).toBe('body/idType must be equal to one of the allowed values');
        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
      });

      it('returns 409 for an email that is already in use by seaware user', async () => {
        const existing = nextSeaWareClientWithEmail();
        mockBlapi.fetchProfile.mockResolvedValue({
          clientId: existing.seaWareClientId,
        });
        const { status, body } = await makeRequest(url, headers, {
          ...payload,
          email: existing.email.email,
        });
        expect(status).toBe(409);
        expect(body.code).toBe('EMAIL_IN_USE');
        expect(body.message).toBe('email is already registered with seaware');
        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
      });

      it('returns 409 for an email that is already in use by plot user', async () => {
        const existing = nextPlotOwnerWithEmail();
        mockPlot.fetchProfile.mockResolvedValue({
          ownerId: existing.plotOwnerId,
        });

        const { status, body } = await makeRequest(url, headers, {
          ...payload,
          email: existing.email.email,
        });
        expect(status).toBe(409);
        expect(body.code).toBe('EMAIL_IN_USE');
        expect(body.message).toBe('email is already registered with owners');
        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
      });

      it('returns 409 for an email that already has an identity registered', async () => {
        const existing = nextHIDToEmail();
        await setupIdentity(existing);

        const { status, body } = await makeRequest(url, headers, {
          ...payload,
          email: existing.email.email,
        });
        expect(status).toBe(409);
        expect(body.code).toBe('EMAIL_IN_USE');
        expect(body.message).toBe('email is already registered with other identity');
        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
      });

      it('returns 400 for a blocked email domain if owner account associated', async () => {
        const existing = nextPlotOwnerWithEmail();
        mockPlot.fetchProfile.mockResolvedValue({
          ownerId: existing.plotOwnerId,
        });

        const { status, body } = await makeRequest(url, headers, {
          ...payload,
          email: '<EMAIL>',
        });
        expect(status).toBe(400);
        expect(body.code).toBe('VALIDATION');
        expect(body.message).toBe('Email domain not allowed');
        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
      });

      it('returns 200 for an email that is linked to hid but not account or profile', async () => {
        const other = nextValidEmail().email;
        await setupHIDAndEmail(other);

        const { status, body } = await makeRequest(url, headers, {
          ...payload,
          email: other,
        });

        expect(mocks.sendEmailChangedEmail).toHaveBeenCalledWith({
          email: other,
          templateId: process.env.EMAIL_CHANGED_TEMPLATE_ID || '',
          name: `${identity.firstName} ${identity.lastName}`,
          oldEmail: identity.email.email,
        });
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body).toEqual({ hid });
        expect(status).toBe(200);
      });

      it('returns 200 for an email/seaware id that are already linked', async () => {
        const existing = nextSeaWareClientWithEmail();
        const identity = await setupIdentity(existing);
        mockMigratedToken(identity.hid, [
          {
            id: existing.seaWareClientId,
            type: IdType.SEAWARE_CLIENT_ID,
          },
        ]);

        const { status, body } = await makeRequest(getUrl(identity.hid), headers, {
          id: existing.seaWareClientId,
          idType: IdType.SEAWARE_CLIENT_ID,
          email: existing.email.email,
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body).toEqual({
          hid: identity.hid,
        });
        expect(status).toBe(200);
      });

      it('returns 200 for an email/owner id that are already linked', async () => {
        const existing = nextPlotOwnerWithEmail();
        const owner = await setupIdentity(existing);

        mockMigratedToken(owner.hid, [
          {
            id: existing.plotOwnerId,
            type: IdType.OWNER_ID,
          },
        ]);

        const { status, body } = await makeRequest(getUrl(owner.hid), headers, {
          id: existing.plotOwnerId,
          idType: IdType.OWNER_ID,
          email: existing.email.email,
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body).toEqual({ hid: owner.hid });
        expect(status).toBe(200);
      });

      it('returns 200 for an unchanged email for migrated identity', async () => {
        const existing = nextIdentity();
        const identity = await setupIdentity(existing);

        mockMigratedToken(identity.hid, existing.accounts);

        const { status, body } = await makeRequest(getUrl(identity.hid), headers, {
          id: existing.accounts[0].id,
          idType: existing.accounts[0].type,
          email: existing.email.email,
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body).toEqual({
          hid: identity.hid,
        });
        expect(status).toBe(200);
      });

      it('returns 200 for an unchanged email for migrated identity with owner and seaware accounts', async () => {
        const currentEmail = nextValidEmail();
        const clientId = nextSeaWareClientId();
        const ownerId = nextPlotOwnerId();
        const identity = await setupIdentity({
          email: currentEmail,
          plotOwnerId: ownerId,
          seaWareClientId: clientId,
        });

        mockMigratedToken(identity.hid, identity.accounts);

        mockBlapi.fetchProfile.mockResolvedValue({
          clientId,
        });
        mockPlot.fetchProfile.mockResolvedValue({
          ownerId,
        });

        const { status, body } = await makeRequest(getUrl(identity.hid), headers, {
          id: clientId,
          idType: IdType.SEAWARE_CLIENT_ID,
          email: identity.email.email,
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body).toEqual({
          hid: identity.hid,
        });
        expect(status).toBe(200);
      });

      it('should be audited using the token subject if token is an on-behalf-of token', async () => {
        const existing = nextSeaWareClientWithEmail();
        const identity = await setupIdentity(existing);

        const txId = 'txId';
        const tokenSubject = '<EMAIL>';
        const source = `${headers.appId}:${tokenSubject}`;
        const mockOnBehalfOfToken = {
          hid: identity.hid,
          accounts: identity.accounts,
          sub: tokenSubject,
          auth_time: Date.now(),
          exp: Date.now() + 10000,
          iat: Date.now(),
          onBehalfOf: {
            prn: identity.hid,
            ctx: 'https://admin.haven.com/identity',
            identity: {
              migrated: true,
            },
          },
        };
        const onBehalfOfHeaders = {
          ...headers,
          jwt: JSON.stringify(mockOnBehalfOfToken),
        };

        mockVerify.mockResolvedValueOnce({
          payload: {
            ...mockOnBehalfOfToken,
          },
        });

        vi.spyOn(ulid, 'ulid').mockReturnValue('txId');
        const { status, body } = await makeRequest(getUrl(identity.hid), onBehalfOfHeaders, {
          id: existing.seaWareClientId,
          idType: IdType.SEAWARE_CLIENT_ID,
          email: '<EMAIL>',
        });

        const audit = await lookupEmailAudits(txId, source);

        expect(body).toEqual({
          hid: identity.hid,
        });
        expect(status).toEqual(200);
        expect(audit).toEqual([
          expect.objectContaining({
            source,
            tx: txId,
            type: 'email',
          }),
        ]);
      });

      it('returns 401 response for a missing idtoken', async () => {
        const { status } = await makeRequest(
          url,
          {
            appId: headers.appId,
            appKey: headers.appKey,
          },
          payload,
        );

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(status).toBe(401);
      });

      it('returns 401 response for a bad idtoken', async () => {
        mockVerify.mockRejectedValue(new Error('Bad'));
        const { status } = await makeRequest(url, headers, payload);

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(status).toBe(401);
      });

      it('returns 403 for idtoken that does not match the path HID', async () => {
        mockVerify.mockResolvedValue({
          payload: { hid: 'not-this-x' },
        });
        const { status } = await makeRequest(url, headers, payload);

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(status).toBe(403);
      });

      it('returns 403 for idtoken where owner id does not match request', async () => {
        mockVerify.mockResolvedValue({
          payload: { hid, accounts: [] },
        });
        const { status } = await makeRequest(url, headers, payload);

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(status).toBe(403);
      });

      it('updates plot but not seaware if account is linked to only to plot', async () => {
        const updatedEmail = nextValidEmail();
        const existing = nextPlotOwnerWithEmail();
        const identity = await setupIdentity(existing);

        mockMigratedToken(identity.hid, identity.accounts);

        const { status, body } = await makeRequest(
          `${server.baseUrl}/identity/${identity.hid}`,
          headers,
          {
            id: existing.plotOwnerId,
            idType: IdType.OWNER_ID,
            email: updatedEmail.email,
          },
        );

        expect(mocks.sendEmailChangedEmail).toHaveBeenCalledWith({
          email: updatedEmail.email,
          templateId: process.env.EMAIL_CHANGED_TEMPLATE_ID || '',
          name: `${identity.firstName} ${identity.lastName}`,
          oldEmail: identity.email.email,
        });
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body).toEqual({
          hid: identity.hid,
        });
        expect(status).toBe(200);

        await expectEmail(identity.hid, updatedEmail);

        expect(mockBlapi.updateProfile).not.toHaveBeenCalled();
        expect(mockPlot.updateProfile).toHaveBeenCalled();
        expect(mockPlot.updateProfile).toHaveBeenCalledWith(existing.plotOwnerId, {
          email: updatedEmail.email,
        });
      });

      it('updates seaware but not plot if account is linked to only to seaware', async () => {
        const updatedEmail = nextValidEmail();
        const existing = nextSeaWareClientWithEmail();
        const identity = await setupIdentity(existing);

        mockMigratedToken(identity.hid, [
          {
            id: existing.seaWareClientId,
            type: IdType.SEAWARE_CLIENT_ID,
          },
        ]);

        const { status } = await makeRequest(
          `${server.baseUrl}/identity/${identity.hid}`,
          headers,
          {
            id: existing.seaWareClientId,
            idType: IdType.SEAWARE_CLIENT_ID,
            email: updatedEmail.email,
          },
        );
        expect(status).toBe(200);

        await expectEmail(identity.hid, updatedEmail);

        expect(mocks.sendEmailChangedEmail).toHaveBeenCalledWith({
          email: updatedEmail.email,
          templateId: process.env.EMAIL_CHANGED_TEMPLATE_ID || '',
          name: `${identity.firstName} ${identity.lastName}`,
          oldEmail: identity.email.email,
        });
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(mockPlot.updateProfile).not.toHaveBeenCalled();
        expect(mockBlapi.updateProfile).toHaveBeenCalledWith(accessToken, {
          email: updatedEmail.email,
        });
      });

      it('updates both plot and seaware if account is linked to both', async () => {
        const updatedEmail = nextValidEmail();
        const email = nextValidEmail();
        const seaware = nextSeaWareClientId();
        const owner = nextPlotOwnerId();
        const identity = await setupIdentity({
          email: email,
          seaWareClientId: seaware,
          plotOwnerId: owner,
        });

        mockMigratedToken(identity.hid, [
          {
            id: seaware,
            type: IdType.SEAWARE_CLIENT_ID,
          },
        ]);

        const { status } = await makeRequest(
          `${server.baseUrl}/identity/${identity.hid}`,
          headers,
          {
            id: seaware,
            idType: IdType.SEAWARE_CLIENT_ID,
            email: updatedEmail.email,
          },
        );
        expect(status).toBe(200);

        await expectEmail(identity.hid, updatedEmail);

        expect(mocks.sendEmailChangedEmail).toHaveBeenCalledWith({
          email: updatedEmail.email,
          templateId: process.env.EMAIL_CHANGED_TEMPLATE_ID || '',
          name: `${identity.firstName} ${identity.lastName}`,
          oldEmail: identity.email.email,
        });
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(mockPlot.updateProfile).toHaveBeenCalledWith(owner, {
          email: updatedEmail.email,
        });
        expect(mockBlapi.updateProfile).toHaveBeenCalledWith(accessToken, {
          email: updatedEmail.email,
        });
      });

      it('updates address to seaware', async () => {
        const email = nextValidEmail();
        const seaware = nextSeaWareClientId();
        const owner = nextPlotOwnerId();
        const identity = await setupIdentity({
          email: email,
          seaWareClientId: seaware,
          plotOwnerId: owner,
        });
        mockBlapiAccount.fetchProfileById.mockResolvedValue({
          address: {
            id: 1,
            line1: 'line 1',
            postcode: 'AAA',
            city: 'Old City',
            countryCode: 'GB',
          },
          clientId: seaware,
          title: identity.title,
          firstName: identity.firstName,
          lastName: identity.lastName,
          email: email,
        });

        mockMigratedToken(identity.hid, [
          {
            id: seaware,
            type: IdType.SEAWARE_CLIENT_ID,
          },
        ]);

        const addressUpdate: Address = {
          line1: 'line 1',
          postcode: 'AAA',
          city: 'New City',
          countryCode: 'GB',
        };
        const { status, body } = await makeRequest(
          `${server.baseUrl}/identity/${identity.hid}`,
          headers,
          {
            id: seaware,
            idType: IdType.SEAWARE_CLIENT_ID,
            address: addressUpdate,
          },
        );

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).toHaveBeenCalledWith({
          email: identity.email.email,
          templateId: process.env.PROFILE_CHANGED_TEMPLATE_ID || '',
          name: `${identity.firstName} ${identity.lastName}`,
          changedFields: ['address'],
        });
        expect(body).toEqual({
          hid: identity.hid,
        });
        expect(status).toBe(200);
        expect(mockPlot.updateProfile).not.toHaveBeenCalled();
        expect(mockBlapi.updateProfile).toHaveBeenCalledWith(accessToken, {
          address: addressUpdate,
        });
      });

      it('updates nothing if nothing provided', async () => {
        const email = nextValidEmail();
        const seaware = nextSeaWareClientId();
        const owner = nextPlotOwnerId();
        const identity = await setupIdentity({
          email: email,
          seaWareClientId: seaware,
          plotOwnerId: owner,
        });

        mockMigratedToken(identity.hid, [
          {
            id: seaware,
            type: IdType.SEAWARE_CLIENT_ID,
          },
        ]);

        const { status, body } = await makeRequest(
          `${server.baseUrl}/identity/${identity.hid}`,
          headers,
          {
            id: seaware,
            idType: IdType.SEAWARE_CLIENT_ID,
          },
        );

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body).toEqual({
          hid: identity.hid,
        });
        expect(status).toBe(200);
        expect(mockPlot.updateProfile).not.toHaveBeenCalled();
        expect(mockBlapi.updateProfile).not.toHaveBeenCalled();
      });

      it('blapi error is returned from api call', async () => {
        const updatedEmail = nextValidEmail();
        const email = nextValidEmail();
        const seaware = nextSeaWareClientId();
        const owner = nextPlotOwnerId();
        const identity = await setupIdentity({
          email: email,
          seaWareClientId: seaware,
          plotOwnerId: owner,
        });

        mockMigratedToken(identity.hid, [
          {
            id: seaware,
            type: IdType.SEAWARE_CLIENT_ID,
          },
        ]);

        mockBlapi.updateProfile.mockRejectedValue(BlapiClientError.updateError(new Error('oops')));

        const { status, body } = await makeRequest(
          `${server.baseUrl}/identity/${identity.hid}`,
          headers,
          {
            id: seaware,
            idType: IdType.SEAWARE_CLIENT_ID,
            email: updatedEmail.email,
          },
        );

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(status).toBe(500);
        expect(body).toEqual({
          code: 'BLAPI_ERROR',
          message: 'Failed to update user',
          statusCode: 500,
        });
        expect(mockPlot.updateProfile).toHaveBeenCalledWith(owner, {
          email: updatedEmail.email,
        });
        expect(mockBlapi.updateProfile).toHaveBeenCalledWith(accessToken, {
          email: updatedEmail.email,
        });
      });
    });

    describe('Name and phoneNumbers', () => {
      let identity: HavenIdentity;
      let hid: HID;
      let url: string;
      let payload: Payload;

      beforeEach(async () => {
        const seawareAccount = nextSeaWareClientWithEmail();

        payload = {
          id: seawareAccount.seaWareClientId,
          idType: IdType.SEAWARE_CLIENT_ID,
        };
        identity = await setupIdentity(seawareAccount);
        hid = identity.hid;
        url = getUrl(hid);

        mockMigratedToken(hid, [
          {
            id: payload.id,
            type: payload.idType,
          },
        ]);
      });

      it('should reject with 400 when TITLE is not a valid title', async () => {
        const { status, body } = await makeRequest(url, headers, {
          ...payload,
          name: 'a',
          title: 'XX',
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body.message).toBe(`body/title must be equal to one of the allowed values`);
        expect(body.code).toBe('VALIDATION');
        expect(status).toBe(400);
      });

      it('should reject with 400 when NAME length is 0', async () => {
        const { status, body } = await makeRequest(url, headers, {
          ...payload,
          name: '',
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body.message).toBe(`body/name must NOT have fewer than 1 characters`);
        expect(body.code).toBe('VALIDATION');
        expect(status).toBe(400);
      });

      it('should reject with 400 when NAME length is greater than 100', async () => {
        const { status, body } = await makeRequest(url, headers, {
          ...payload,
          name: 'a'.repeat(101),
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body.message).toBe(`body/name must NOT have more than 100 characters`);
        expect(body.code).toBe('VALIDATION');
        expect(status).toBe(400);
      });

      it('should reject with 400 when FIRST NAME length is 0', async () => {
        const { status, body } = await makeRequest(url, headers, {
          ...payload,
          name: 'a',
          firstName: '',
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body.message).toBe(`body/firstName must NOT have fewer than 1 characters`);
        expect(body.code).toBe('VALIDATION');
        expect(status).toBe(400);
      });

      it('should reject with 400 when FIRST NAME length is greater than 100', async () => {
        const { status, body } = await makeRequest(url, headers, {
          ...payload,
          name: 'a',
          firstName: 'a'.repeat(101),
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body.message).toBe(`body/firstName must NOT have more than 100 characters`);
        expect(body.code).toBe('VALIDATION');
        expect(status).toBe(400);
      });

      it('should reject with 400 when LAST NAME length is 0', async () => {
        const { status, body } = await makeRequest(url, headers, {
          ...payload,
          name: 'a',
          lastName: '',
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body.message).toBe(`body/lastName must NOT have fewer than 1 characters`);
        expect(body.code).toBe('VALIDATION');
        expect(status).toBe(400);
      });

      it('should reject with 400 when LAST NAME length is greater than 100', async () => {
        const { status, body } = await makeRequest(url, headers, {
          ...payload,
          name: 'a',
          lastName: 'a'.repeat(101),
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body.message).toBe(`body/lastName must NOT have more than 100 characters`);
        expect(body.code).toBe('VALIDATION');
        expect(status).toBe(400);
      });

      it('should reject with 400 when PHONE NUMBER length is 0', async () => {
        const { status, body } = await makeRequest(url, headers, {
          ...payload,
          name: 'a',
          phoneNumber: '',
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body.message).toBe(`body/phoneNumber must NOT have fewer than 1 characters`);
        expect(body.code).toBe('VALIDATION');
        expect(status).toBe(400);
      });

      it('should reject with 400 when PHONE NUMBER length is greater than 30', async () => {
        const { status, body } = await makeRequest(url, headers, {
          ...payload,
          name: 'a',
          phoneNumber: '1'.repeat(31),
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body.message).toBe(`body/phoneNumber must NOT have more than 30 characters`);
        expect(body.code).toBe('VALIDATION');
        expect(status).toBe(400);
      });

      it('should reject with 400 when PHONE NUMBER is not a valid phone number', async () => {
        const { status, body } = await makeRequest(url, headers, {
          ...payload,
          name: 'a',
          phoneNumber: 'not valid number',
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body.message).toBe(`body/phoneNumber is not valid`);
        expect(body.code).toBe('VALIDATION');
        expect(status).toBe(400);
      });
      it('should reject with 400 when ALTERNATE PHONE NUMBER is not a valid phone number', async () => {
        const { status, body } = await makeRequest(url, headers, {
          ...payload,
          name: 'a',
          alternatePhoneNumber: 'not valid number',
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body.message).toBe(`body/phoneNumber is not valid`);
        expect(body.code).toBe('VALIDATION');
        expect(status).toBe(400);
      });

      it('should respond with 200 and update profile', async () => {
        const owner = nextPlotOwnerWithEmail();
        const profile = nextHIDToProfile();
        const identity = await setupIdentity(
          {
            email: owner.email,
            plotOwnerId: owner.plotOwnerId,
          },
          profile,
        );
        const profileUpdate = { name: 'a' };

        mockMigratedToken(identity.hid, [
          {
            id: owner.plotOwnerId,
            type: IdType.OWNER_ID,
          },
        ]);

        const { status, body } = await makeRequest(
          `${server.baseUrl}/identity/${identity.hid}`,
          headers,
          {
            id: owner.plotOwnerId,
            idType: IdType.OWNER_ID,
            ...profileUpdate,
          },
        );

        const updatedProfile = await getIdentity(identity.hid);

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).toHaveBeenCalledWith({
          email: identity.email.email,
          templateId: process.env.PROFILE_CHANGED_TEMPLATE_ID || '',
          name: `${profile.firstName} ${profile.lastName}`,
          changedFields: ['name'],
        });
        expect(body).toEqual({
          hid: identity.hid,
        });
        expect(updatedProfile?.name).toEqual(profileUpdate.name);
        expect(status).toEqual(200);
      });

      it('should update Seaware via blapi for profile changes', async () => {
        const email = nextValidEmail();
        const seawareId = nextSeaWareClientId();
        const ownerId = nextPlotOwnerId();
        const identity = await setupIdentity({
          email: email,
          seaWareClientId: seawareId,
          plotOwnerId: ownerId,
        });
        const profileUpdate = {
          title: 'Mr',
          firstName: 'b',
          lastName: 'c',
          phoneNumber: '07759123456',
          alternatePhoneNumber: '06666123456',
        };

        mockMigratedToken(identity.hid, [
          {
            id: seawareId,
            type: IdType.SEAWARE_CLIENT_ID,
          },
        ]);

        const { status, body } = await makeRequest(
          `${server.baseUrl}/identity/${identity.hid}`,
          headers,
          {
            id: seawareId,
            idType: IdType.SEAWARE_CLIENT_ID,
            ...profileUpdate,
          },
        );

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).toHaveBeenCalledWith({
          email: identity.email.email,
          templateId: process.env.PROFILE_CHANGED_TEMPLATE_ID || '',
          name: `${profileUpdate.firstName} ${profileUpdate.lastName}`,
          changedFields: ['name', 'contact number'],
        });
        expect(body).toEqual({
          hid: identity.hid,
        });
        expect(status).toBe(200);
        expect(mockPlot.updateProfile).not.toHaveBeenCalled();
        expect(mockBlapi.updateProfile).toHaveBeenCalledWith(accessToken, {
          ...profileUpdate,
        });
      });

      it('should not update plot', async () => {
        const owner = nextPlotOwnerWithEmail();
        const identity = await setupIdentity({
          email: owner.email,
          plotOwnerId: owner.plotOwnerId,
        });

        mockMigratedToken(identity.hid, [
          {
            id: owner.plotOwnerId,
            type: IdType.OWNER_ID,
          },
        ]);

        const { status, body } = await makeRequest(
          `${server.baseUrl}/identity/${identity.hid}`,
          headers,
          {
            id: owner.plotOwnerId,
            idType: IdType.OWNER_ID,
            name: 'a',
          },
        );

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).toHaveBeenCalledWith({
          email: identity.email.email,
          templateId: process.env.PROFILE_CHANGED_TEMPLATE_ID || '',
          name: `${identity.firstName} ${identity.lastName}`,
          changedFields: ['name'],
        });
        expect(body).toEqual({
          hid: identity.hid,
        });
        expect(status).toBe(200);
        expect(mockPlot.updateProfile).not.toHaveBeenCalled();
        expect(mockBlapi.updateProfile).not.toHaveBeenCalled();
      });

      it('X-APP-ID header to identify back end service issuing request', async () => {
        const { status, body } = await makeRequest(
          url,
          { ...headers, appId: '' },
          {
            ...payload,
            name: 'a',
          },
        );

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body.code).toEqual('FORBIDDEN');
        expect(body.message).toEqual('x-app-id header is missing');
        expect(status).toBe(403);
      });

      it('X-APP-KEY header to identify back end service issuing request', async () => {
        const { status, body } = await makeRequest(
          url,
          { ...headers, appKey: '' },
          {
            ...payload,
            name: 'a',
          },
        );

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body.code).toEqual('NOT_AUTHORISED');
        expect(body.message).toEqual('x-app-key header is required');
        expect(status).toBe(401);
      });

      it('should be audited and include x-app-id of requesting app', async () => {
        const txId = 'txId';
        const source = `${headers.appId}:${UPDATE_PROFILE_SOURCE_SUFFIX}`;

        vi.spyOn(ulid, 'ulid').mockReturnValue('txId');

        const { status, body } = await makeRequest(url, headers, {
          ...payload,
          name: 'a',
        });

        const audit = await lookupProfileAudits(txId, source);

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).toHaveBeenCalledWith({
          email: identity.email.email,
          templateId: process.env.PROFILE_CHANGED_TEMPLATE_ID || '',
          name: `${identity.firstName} ${identity.lastName}`,
          changedFields: ['name'],
        });
        expect(body).toEqual({ hid });
        expect(status).toEqual(200);
        expect(audit).toEqual([
          expect.objectContaining({
            source,
            tx: txId,
            type: 'profile',
          }),
        ]);
      });

      it('should truncate name fields that exceed BLAPI (30 characters) rules.', async () => {
        const email = nextValidEmail();
        const seawareId = nextSeaWareClientId();
        const ownerId = nextPlotOwnerId();
        const identity = await setupIdentity({
          email: email,
          seaWareClientId: seawareId,
          plotOwnerId: ownerId,
        });
        const name = 'a';
        const longName = name.repeat(50);
        const profileUpdate = {
          name: longName,
          firstName: longName,
          lastName: longName,
          phoneNumber: '07759123456',
        };

        mockMigratedToken(identity.hid, [
          {
            id: seawareId,
            type: IdType.SEAWARE_CLIENT_ID,
          },
        ]);

        const { status, body } = await makeRequest(
          `${server.baseUrl}/identity/${identity.hid}`,
          headers,
          {
            id: seawareId,
            idType: IdType.SEAWARE_CLIENT_ID,
            ...profileUpdate,
          },
        );

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).toHaveBeenCalledWith({
          email: identity.email.email,
          templateId: process.env.PROFILE_CHANGED_TEMPLATE_ID || '',
          name: `${profileUpdate.firstName} ${profileUpdate.lastName}`,
          changedFields: ['name', 'contact number'],
        });
        expect(body).toEqual({
          hid: identity.hid,
        });
        expect(mockBlapi.updateProfile).toHaveBeenCalledWith(accessToken, {
          ...profileUpdate,
          name: name.repeat(30),
          firstName: name.repeat(30),
          lastName: name.repeat(30),
        });
        expect(status).toEqual(200);
      });
    });

    describe('Fix accounts', async () => {
      let identity: HavenIdentity;
      let hid: HID;
      let url: string;
      let payload: Payload;

      beforeEach(async () => {
        const changedEmail = nextValidEmail();
        const currentEmail = nextValidEmail();
        const plotOwnerId = nextPlotOwnerId();
        payload = {
          email: changedEmail.email,
          id: plotOwnerId,
          idType: IdType.OWNER_ID,
        };
        identity = await setupIdentity({
          email: currentEmail,
        });
        hid = identity.hid;
        url = getUrl(hid);
        mockMigratedToken(hid, [
          {
            id: payload.id as number,
            type: payload.idType as IdType,
          },
        ]);
      });

      it('Use accounts from jwt payload only if token is an on-behalf-of token', async () => {
        const tokenSubject = '<EMAIL>';
        const mockOnBehalfOfToken = {
          hid: identity.hid,
          accounts: [
            {
              id: payload.id as number,
              type: payload.idType as IdType,
            },
          ],
          sub: tokenSubject,
          auth_time: Date.now(),
          exp: Date.now() + 10000,
          iat: Date.now(),
          onBehalfOf: {
            prn: identity.hid,
            ctx: 'https://admin.haven.com/identity',
            identity: {
              migrated: true,
            },
          },
        };
        const onBehalfOfHeaders = {
          ...headers,
          jwt: JSON.stringify(mockOnBehalfOfToken),
        };

        mockVerify.mockResolvedValueOnce({
          payload: {
            ...mockOnBehalfOfToken,
          },
        });

        const existingIdentity = await getIdentity(identity.hid);
        expect(existingIdentity?.accounts).toEqual([]);

        const response = await makeRequest(url, onBehalfOfHeaders, payload);

        expect(response.body).toEqual({
          hid: identity.hid,
        });
        expect(response.status).toBe(200);
        expect(mockPlot.updateProfile).toHaveBeenCalledWith(payload.id, {
          email: payload.email,
        });
        expect(mockBlapi.updateProfile).not.toHaveBeenCalled();

        const updatedIdentity = await getIdentity(identity.hid);
        expect(updatedIdentity?.accounts).toEqual([
          {
            id: payload.id,
            type: payload.idType,
          },
        ]);
      });
    });
  });

  describe('proxy authenticated user tests', async () => {
    const PLOT_ID_PREFIX = 'PLOT:';
    const SEAWARE_ID_PREFIX = 'SEAWARE:';
    let hid: HID;
    let email: string;
    let accountId: number;
    let payload: Payload;
    let url: string;

    beforeEach(async () => {
      email = nextValidEmail().email;
      hid = await setupHIDAndEmail(email);
      accountId = nextSeaWareClientId();
      payload = {
        email: email,
        id: accountId,
        idType: IdType.SEAWARE_CLIENT_ID,
      };
      url = getUrl(hid);
    });

    it('returns 200 response for proxy authenticate seaware user and only updates seaware', async () => {
      mockProxyAuthenticatedToken(SEAWARE_ID_PREFIX, hid, payload.id, [
        {
          id: payload.id,
          type: payload.idType,
        },
      ]);

      const response = await makeRequest(url, headers, payload);

      expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
      expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
      expect(response.body).toEqual({ hid });
      expect(response.status).toBe(200);
      expect(mockPlot.updateProfile).not.toHaveBeenCalled();
      expect(mockBlapi.updateProfile).toHaveBeenCalledWith(accessToken, {
        email,
      });
    });

    it('returns 200 response for proxy authenticate owner and only updates PLOT', async () => {
      mockProxyAuthenticatedToken(PLOT_ID_PREFIX, hid, payload.id, [
        {
          id: payload.id,
          type: IdType.OWNER_ID,
        },
      ]);

      const response = await makeRequest(url, headers, { ...payload, idType: IdType.OWNER_ID });

      expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
      expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
      expect(response.body).toEqual({ hid });
      expect(response.status).toBe(200);
      expect(mockPlot.updateProfile).toHaveBeenCalledWith(accountId, {
        email,
      });
      expect(mockBlapi.updateProfile).not.toHaveBeenCalled();
    });

    it('ignores updates for address if proxy authenticated plot user', async () => {
      const addressUpdate: Address = {
        line1: 'line 1',
        postcode: 'AAA',
        city: 'New City',
        countryCode: 'GB',
      };
      mockProxyAuthenticatedToken(PLOT_ID_PREFIX, hid, payload.id, [
        {
          id: payload.id,
          type: IdType.OWNER_ID,
        },
      ]);

      const response = await makeRequest(url, headers, {
        id: payload.id,
        idType: IdType.OWNER_ID,
        address: addressUpdate,
      });

      expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
      expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
      expect(response.body).toEqual({ hid });
      expect(response.status).toBe(200);
      expect(mockPlot.updateProfile).not.toHaveBeenCalled();
      expect(mockBlapi.updateProfile).not.toHaveBeenCalled();
    });

    it('updates address if proxy authenticated seaware user', async () => {
      const addressUpdate: Address = {
        line1: 'line 1',
        postcode: 'AAA',
        city: 'New City',
        countryCode: 'GB',
      };
      mockProxyAuthenticatedToken(SEAWARE_ID_PREFIX, hid, payload.id, [
        {
          id: payload.id,
          type: IdType.SEAWARE_CLIENT_ID,
        },
      ]);

      const response = await makeRequest(url, headers, {
        id: payload.id,
        idType: IdType.SEAWARE_CLIENT_ID,
        address: addressUpdate,
      });

      expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
      expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
      expect(response.body).toEqual({ hid });
      expect(response.status).toBe(200);
      expect(mockPlot.updateProfile).not.toHaveBeenCalled();
      expect(mockBlapi.updateProfile).toHaveBeenCalledWith(accessToken, {
        address: addressUpdate,
      });
    });

    it('updates phoneNumber if proxy authenticated seaware user', async () => {
      mockProxyAuthenticatedToken(SEAWARE_ID_PREFIX, hid, payload.id, [
        {
          id: payload.id,
          type: IdType.SEAWARE_CLIENT_ID,
        },
      ]);
      const phone = '06666123456';

      const response = await makeRequest(url, headers, {
        id: payload.id,
        idType: IdType.SEAWARE_CLIENT_ID,
        phoneNumber: phone,
      });

      expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
      expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
      expect(response.body).toEqual({ hid });
      expect(response.status).toBe(200);
      expect(mockPlot.updateProfile).not.toHaveBeenCalled();
      expect(mockBlapi.updateProfile).toHaveBeenCalledWith(accessToken, {
        phoneNumber: phone,
      });
    });
    it('updates alternatePhoneNumber if proxy authenticated seaware user', async () => {
      mockProxyAuthenticatedToken(SEAWARE_ID_PREFIX, hid, payload.id, [
        {
          id: payload.id,
          type: IdType.SEAWARE_CLIENT_ID,
        },
      ]);
      const phone = '06666123456';

      const response = await makeRequest(url, headers, {
        id: payload.id,
        idType: IdType.SEAWARE_CLIENT_ID,
        alternatePhoneNumber: phone,
      });

      expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
      expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
      expect(response.body).toEqual({ hid });
      expect(response.status).toBe(200);
      expect(mockPlot.updateProfile).not.toHaveBeenCalled();
      expect(mockBlapi.updateProfile).toHaveBeenCalledWith(accessToken, {
        alternatePhoneNumber: phone,
      });
    });

    it('can clear alternatePhoneNumber if proxy authenticated seaware user', async () => {
      mockProxyAuthenticatedToken(SEAWARE_ID_PREFIX, hid, payload.id, [
        {
          id: payload.id,
          type: IdType.SEAWARE_CLIENT_ID,
        },
      ]);
      const phone = '';

      const response = await makeRequest(url, headers, {
        id: payload.id,
        idType: IdType.SEAWARE_CLIENT_ID,
        alternatePhoneNumber: phone,
      });

      expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
      expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
      expect(response.body).toEqual({ hid });
      expect(response.status).toBe(200);
      expect(mockPlot.updateProfile).not.toHaveBeenCalled();
      expect(mockBlapi.updateProfile).toHaveBeenCalledWith(accessToken, {
        alternatePhoneNumber: phone,
      });
    });

    it('returns access denied if missing MANAGEMENT_KEY', async () => {
      const { status, body } = await makeRequest(url, { ...headers, appKey: '' }, payload);

      expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
      expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
      expect(status).toBe(401);
      expect(body.code).toBe('NOT_AUTHORISED');
      expect(body.message).toBe('x-app-key header is required');
    });

    it('returns access denied if incorrect MANAGEMENT_KEY', async () => {
      const { status, body } = await makeRequest(url, { ...headers, appKey: 'Invalid' }, payload);

      expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
      expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
      expect(status).toBe(401);
      expect(body.code).toBe('NOT_AUTHORISED');
      expect(body.message).toBe('invalid x-app-key header');
    });

    it('returns 400 for missing id', async () => {
      mockProxyAuthenticatedToken(SEAWARE_ID_PREFIX, hid, payload.id, [
        {
          id: payload.id,
          type: IdType.SEAWARE_CLIENT_ID,
        },
      ]);
      const { status, body } = await makeRequest(url, headers, {});

      expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
      expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
      expect(body.message).toBe(`Unmigrated user requires an id and idType`);
      expect(body.code).toBe('VALIDATION');
      expect(status).toBe(400);
    });

    it('returns 400 for bad HID', async () => {
      const url = `${server.baseUrl}/identity/aaaaa`;
      const { status, body } = await makeRequest(url, headers, payload);

      expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
      expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
      expect(status).toBe(400);
      expect(body.code).toBe('VALIDATION');
      expect(body.message).toBe('params/hid must match pattern "[A-Z0-9]{12}"');
    });

    it('returns 400 for an email that does not pass validation', async () => {
      const email = nextInvalidEmail();
      mockProxyAuthenticatedToken(SEAWARE_ID_PREFIX, hid, payload.id, [
        {
          id: payload.id,
          type: IdType.SEAWARE_CLIENT_ID,
        },
      ]);

      const { status, body } = await makeRequest(url, headers, {
        ...payload,
        email,
      });

      expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
      expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
      expect(body.message).toBe('body/email is not a valid email');
      expect(body.code).toBe('VALIDATION');
      expect(status).toBe(400);
    });

    it('returns 400 for an unknown id type', async () => {
      const { status, body } = await makeRequest(url, headers, {
        ...payload,
        idType: 'xxx' as HavenServiceAccountIdType,
      });

      expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
      expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
      expect(status).toBe(400);
      expect(body.code).toBe('VALIDATION');
      expect(body.message).toBe('body/idType must be equal to one of the allowed values');
    });

    it('returns 409 for an email that is already in use by migrated identity', async () => {
      mockProxyAuthenticatedToken(SEAWARE_ID_PREFIX, hid, payload.id, [
        {
          id: payload.id,
          type: payload.idType,
        },
      ]);

      const changedEmail = nextValidEmail();
      await setupIdentity({
        email: changedEmail,
      });

      const { status, body } = await makeRequest(url, headers, {
        ...payload,
        email: changedEmail.email,
      });

      expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
      expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
      expect(status).toBe(409);
      expect(body.code).toBe('EMAIL_IN_USE');
      expect(body.message).toBe('email is already registered with other identity');
    });

    it('returns 409 for an email that is already in use by seaware user', async () => {
      mockProxyAuthenticatedToken(SEAWARE_ID_PREFIX, hid, payload.id, [
        {
          id: payload.id,
          type: payload.idType,
        },
      ]);

      const existing = nextSeaWareClientWithEmail();
      mockBlapi.fetchProfile.mockResolvedValue({
        clientId: existing.seaWareClientId,
      });
      const { status, body } = await makeRequest(url, headers, {
        ...payload,
        email: existing.email.email,
      });

      expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
      expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
      expect(status).toBe(409);
      expect(body.code).toBe('EMAIL_IN_USE');
      expect(body.message).toBe('email is already registered with seaware');
    });

    it('returns 409 for an email that is already in use by plot user', async () => {
      const url = getUrl(hid);
      mockProxyAuthenticatedToken(PLOT_ID_PREFIX, hid, payload.id, [
        {
          id: payload.id,
          type: IdType.OWNER_ID,
        },
      ]);

      const existing = nextPlotOwnerWithEmail();
      mockPlot.fetchProfile.mockResolvedValue({
        ownerId: existing.plotOwnerId,
      });
      const { status, body } = await makeRequest(url, headers, {
        ...payload,
        idType: IdType.OWNER_ID,
        email: existing.email.email,
      });

      expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
      expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
      expect(status).toBe(409);
      expect(body.code).toBe('EMAIL_IN_USE');
      expect(body.message).toBe('email is already registered with owners');
    }, 20000);

    it('returns 409 for an email that is already in use by other account type', async () => {
      const url = getUrl(hid);
      mockProxyAuthenticatedToken(PLOT_ID_PREFIX, hid, payload.id, [
        {
          id: payload.id,
          type: IdType.OWNER_ID,
        },
        {
          id: nextSeaWareClientId(),
          type: IdType.SEAWARE_CLIENT_ID,
        },
      ]);

      const existing = nextPlotOwnerWithEmail();
      mockPlot.fetchProfile.mockResolvedValue(undefined);
      mockBlapi.fetchProfile.mockResolvedValue({});
      const { status, body } = await makeRequest(url, headers, {
        ...payload,
        idType: IdType.OWNER_ID,
        email: existing.email.email,
      });

      expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
      expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
      expect(status).toBe(409);
      expect(body.code).toBe('EMAIL_IN_USE');
      expect(body.message).toBe('email is already registered with seaware');
    }, 20000);

    it('returns 400 for a blocked email domain if owner account associated', async () => {
      const url = getUrl(hid);
      mockProxyAuthenticatedToken(PLOT_ID_PREFIX, hid, payload.id, [
        {
          id: payload.id,
          type: IdType.OWNER_ID,
        },
      ]);

      const existing = nextPlotOwnerWithEmail();
      mockPlot.fetchProfile.mockResolvedValue({
        ownerId: existing.plotOwnerId,
      });
      const { status, body } = await makeRequest(url, headers, {
        ...payload,
        idType: IdType.OWNER_ID,
        email: '<EMAIL>',
      });

      expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
      expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
      expect(status).toBe(400);
      expect(body.code).toBe('VALIDATION');
      expect(body.message).toBe('Email domain not allowed');
    });

    it('returns 401 response for a missing idtoken', async () => {
      const { status } = await makeRequest(
        url,
        {
          appKey: headers.appKey,
          appId: headers.appId,
        },
        payload,
      );

      expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
      expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
      expect(status).toBe(401);
    });

    it('returns 401 response for a bad idtoken', async () => {
      mockVerify.mockRejectedValue(new Error('Bad'));
      const { status } = await makeRequest(url, headers, payload);

      expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
      expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
      expect(status).toBe(401);
    });

    it('returns 403 for idtoken that does not match the path HID', async () => {
      mockVerify.mockResolvedValue({
        payload: { hid: 'not-this-x' },
      });
      const { status } = await makeRequest(url, headers, payload);

      expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
      expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
      expect(status).toBe(403);
    });

    it('returns 403 for idtoken where owner id does not match request', async () => {
      mockVerify.mockResolvedValue({
        payload: { hid: 'OTHER', accounts: [] },
      });
      const { status } = await makeRequest(url, headers, payload);

      expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
      expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
      expect(status).toBe(403);
    });

    describe('Seaware User', () => {
      const SEAWARE_ID_PREFIX = 'SEAWARE:';
      let hid: HID;
      let accountId: number;
      let payload: Payload;
      let url: string;

      beforeEach(async () => {
        email = nextValidEmail().email;
        hid = await setupHIDAndEmail(email);
        accountId = nextSeaWareClientId();
        payload = {
          id: accountId,
          idType: IdType.SEAWARE_CLIENT_ID,
        };
        url = getUrl(hid);

        vi.resetAllMocks();
        mockBlapi.updateProfile.mockResolvedValue({ success: true });
        mockPlot.updateProfile.mockResolvedValue(true);
      });

      it('should reject with BlapiClientError when update is rejected by Seaware', async () => {
        mockProxyAuthenticatedToken(SEAWARE_ID_PREFIX, hid, payload.id, [
          {
            id: payload.id,
            type: payload.idType,
          },
        ]);

        const axiosError = new AxiosError('Axios Error', 'AXIOS_ERROR_CODE', undefined, {}, {
          status: 400,
          data: {
            Errors: [{ Message: 'Invalid something' }],
          },
        } as AxiosResponse);
        const blapiClientError = BlapiClientError.updateError(axiosError);

        mockBlapi.updateProfile.mockRejectedValue(blapiClientError);

        const { body, status } = await makeRequest(url, headers, {
          ...payload,
          title: 'Mr',
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body).toEqual({
          code: 'BLAPI_BAD_REQUEST',
          message: 'Failed to update user: Invalid something',
          statusCode: 500,
        });
        expect(status).toBe(500);
        expect(mockBlapi.updateProfile).toHaveBeenCalledWith(accessToken, {
          title: 'Mr',
        });
      });

      it('should respond with 200 and only update seaware profile when there is not an email update', async () => {
        mockProxyAuthenticatedToken(SEAWARE_ID_PREFIX, hid, payload.id, [
          {
            id: payload.id,
            type: payload.idType,
          },
        ]);

        const { body, status } = await makeRequest(url, headers, {
          ...payload,
          title: 'Mr',
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body).toEqual({ hid });
        expect(status).toBe(200);
        expect(mockPlot.updateProfile).not.toBeCalled();
        expect(mockBlapi.updateProfile).toHaveBeenCalledWith(accessToken, {
          title: 'Mr',
        });
      });

      it('should respond with 200 and only update seaware profile when there is an email update', async () => {
        mockProxyAuthenticatedToken(SEAWARE_ID_PREFIX, hid, payload.id, [
          {
            id: payload.id,
            type: payload.idType,
          },
        ]);

        const { body, status } = await makeRequest(url, headers, {
          ...payload,
          email,
          title: 'Mr',
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body).toEqual({ hid });
        expect(status).toBe(200);
        expect(mockPlot.updateProfile).not.toBeCalled();
        expect(mockBlapi.updateProfile).toHaveBeenCalledWith(accessToken, {
          email,
          title: 'Mr',
        });
      });
    });
  });

  const mockMigratedToken = (hid: HID, accounts: HavenServiceAccount[]) => {
    mockVerify.mockResolvedValue({
      payload: {
        sub: hid,
        hid: hid,
        accounts: accounts,
      },
    });
  };
  const mockProxyAuthenticatedToken = (
    subjectPrefix: string,
    hid: HID,
    id: number,
    accounts: HavenServiceAccount[],
  ) => {
    mockVerify.mockResolvedValue({
      payload: {
        sub: `${subjectPrefix}${id}`,
        hid: hid,
        accounts: accounts,
      },
    });
  };
});
