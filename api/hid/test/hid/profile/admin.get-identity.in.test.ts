import { afterAll, beforeAll, beforeEach, describe, expect, it, Mock, vi } from 'vitest';
import request, { Response } from 'superagent';
import { Server } from '../../mock/server.js';
import { X_APP_ID_HEADER, X_APP_KEY_HEADER } from '../../../src/hid/header.js';
import { nextHID, nextSeaWareClientWithEmail } from 'haven-hid-domain/mock.js';
import {
  hidForEmailPlotAndSeaWare,
  hidStore,
  identityStore,
  mockBlapi,
  mockPlot,
  setupIdentity,
} from '../../helper.js';
import { getDependencies } from '../../../src/dependencies/dependencies.js';

vi.mock('haven-identity-plot-client');
vi.mock('haven-hid-pgsql-adapter/adapter.js');
vi.mock('../../../src/dependencies/dependencies.js');

const server = new Server();

const TEST_APP_ID = 'TEST_APP';
const TEST_ADMIN_KEY = 'ADMIN-KEY';
const accessToken = '<jwt-token>';
const headers = {
  appId: TEST_APP_ID,
  appKey: TEST_ADMIN_KEY,
};

const mockDependencies = {
  identityManagement: identityStore,
  hidStore: hidStore,
  store: hidForEmailPlotAndSeaWare,
  blapi: mockBlapi,
  plot: mockPlot,
};
(getDependencies as Mock).mockReturnValue(mockDependencies);

describe('Get Profile', () => {
  const getUrl = (hid: string): string => `${server.baseUrl}/admin/identity/${hid}`;
  const makeRequest = async (
    path: string,
    headers: {
      appId?: string;
      appKey?: string;
      jwt?: string;
    },
  ): Promise<Response> => {
    const fetch = request.get(path).ok(() => true);
    if (headers.appKey) {
      fetch.set(X_APP_KEY_HEADER, headers.appKey);
    }
    if (headers.appId) {
      fetch.set(X_APP_ID_HEADER, headers.appId);
    }
    return fetch.send();
  };

  beforeAll(async () => {
    await server.start();
  });

  afterAll(async () => {
    await server.stop();
  });

  beforeEach(() => {
    vi.resetAllMocks();
    (getDependencies as Mock).mockReturnValue(mockDependencies);
    mockPlot.fetchProfile.mockResolvedValue(undefined);
    mockBlapi.fetchProfile.mockResolvedValue(undefined);
  });

  describe('access control', () => {
    const url = getUrl('XXXXX');
    it('returns access denied if missing MANAGEMENT_KEY', async () => {
      const { status, body } = await makeRequest(url, { ...headers, appKey: '' });
      expect(status).toBe(401);
      expect(body.code).toBe('NOT_AUTHORISED');
      expect(body.message).toBe('x-app-key header is required');
    });

    it('returns access denied if incorrect MANAGEMENT_KEY', async () => {
      const { status, body } = await makeRequest(url, { ...headers, appKey: 'Invalid' });
      expect(status).toBe(401);
      expect(body.code).toBe('NOT_AUTHORISED');
      expect(body.message).toBe('invalid x-app-key header');
    });
  });

  describe('bad requests', () => {
    it('returns 400 for bad HID', async () => {
      const url = `${server.baseUrl}/admin/identity/aaaaa`;
      const { status, body } = await makeRequest(url, headers);
      expect(status).toBe(400);
      expect(body.code).toBe('VALIDATION');
      expect(body.message).toBe('params/hid must match pattern "[A-Z0-9]{12}"');
    });

    it('returns 422 response for missing user', async () => {
      const unknownHID = nextHID();
      const url = getUrl(unknownHID);

      const response = await makeRequest(url, headers);

      expect(response.body).toEqual({
        code: 'INVALID_HID',
        message: `No such HID: ${unknownHID}`,
        statusCode: 422,
      });
      expect(response.status).toBe(422);
    });
  });

  describe('valid requests', () => {
    it('returns 200 response for an identity', async () => {
      const seawareClientWithEmail = nextSeaWareClientWithEmail();
      mockBlapi.fetchProfile.mockResolvedValueOnce({
        clientId: seawareClientWithEmail.seaWareClientId,
        email: seawareClientWithEmail.email.email,
      });

      const identity = await setupIdentity(seawareClientWithEmail);
      const hid = identity.hid;
      const url = getUrl(hid);

      const response = await makeRequest(url, headers);

      expect(response.body).toEqual({
        migrated: true,
        email: identity.email.email,
        firstName: identity.firstName,
        hid: identity.hid,
        lastName: identity.lastName,
        phoneNumber: identity.phoneNumber,
        title: identity.title,
        passwordStrength: identity.passwordStrength,
        accounts: [
          {
            ...identity.accounts[0],
            state: 'MATCHED',
          },
        ],
      });
      expect(response.status).toBe(200);
    });
  });
});
