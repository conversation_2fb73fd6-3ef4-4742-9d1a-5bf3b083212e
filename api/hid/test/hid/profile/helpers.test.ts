import { ValidEmail } from 'haven-hid-domain';
import {
  getChangedProfileFields,
  checkAddressIsChanged,
  ExtendedHavenIdentity,
  checkAddressPropertyIsChanged,
} from '../../../src/hid/profile/helpers.js';
import { describe, expect, it } from 'vitest';
import { CountryCodes } from 'haven-identity-blapi-client';

const migratedUser: ExtendedHavenIdentity = {
  hid: '123',
  title: 'Mr',
  firstName: '<PERSON>',
  lastName: 'Smith',
  email: { email: '<EMAIL>' } as ValidEmail,
  emailVerified: true,
  accounts: [],
};

describe('getChangedProfileFields', () => {
  it('should return the "name" if any name property changed', () => {
    const result = getChangedProfileFields(
      {
        title: 'Mrs',
        firstName: 'Test',
        lastName: 'Test',
      },
      migratedUser,
    );
    expect(result).toEqual(['name']);
  });

  it('should return the "contact number" if any phone number property changed', () => {
    const result = getChangedProfileFields(
      {
        phoneNumber: '*********',
        alternatePhoneNumber: '*********',
      },
      migratedUser,
    );
    expect(result).toEqual(['contact number']);
  });

  it('should return the "contact number" if the phone number property changed', () => {
    const result = getChangedProfileFields(
      {
        phoneNumber: '*********',
        alternatePhoneNumber: '*********',
      },
      {
        ...migratedUser,
        phoneNumber: '*********',
        alternatePhoneNumber: '*********',
      },
    );
    expect(result).toEqual(['contact number']);
  });

  it('should return the "contact number" if the alternate phone number property changed', () => {
    const result = getChangedProfileFields(
      {
        phoneNumber: '*********',
        alternatePhoneNumber: '*********',
      },
      {
        ...migratedUser,
        phoneNumber: '*********',
        alternatePhoneNumber: '*********',
      },
    );
    expect(result).toEqual(['contact number']);
  });

  it('should return the "name" and "contact number" if both name and phone number properties changed', () => {
    const result = getChangedProfileFields(
      {
        title: 'Mrs',
        firstName: 'Test',
        lastName: 'Test',
        phoneNumber: '*********',
        alternatePhoneNumber: '*********',
      },
      migratedUser,
    );
    expect(result).toEqual(['name', 'contact number']);
  });

  it('should handle when both properties have a value but evaluate to false', () => {
    const result = getChangedProfileFields(
      {
        title: 'Mrs',
        firstName: 'Test',
        lastName: 'Test',
        phoneNumber: '',
        alternatePhoneNumber: '',
      },
      migratedUser,
    );
    expect(result).toEqual(['name']);
  });

  it('should return an empty array if no fields have changed', () => {
    const profile = {
      firstName: 'John',
      lastName: 'Doe',
    };

    const expected: string[] = [];
    const result = getChangedProfileFields(profile, { ...migratedUser, ...profile });
    expect(result).toEqual(expected);
  });
});

describe('checkAddressIsChanged', () => {
  const address = {
    line1: 'line1',
    line2: 'line2',
    city: 'city',
    county: 'county',
    postcode: 'postcode',
    countryCode: 'GB' as CountryCodes,
  };
  const currentBlapiAddress = {
    line1: 'line1',
    line2: 'line2',
    line3: 'line3',
    line4: 'line4',
    city: 'city',
    zip: 'zip',
    country: 'US',
  };

  it('should return false if both address and currentBlapiAddress are undefined', () => {
    const result = checkAddressIsChanged(undefined, undefined);
    expect(result).toBe(false);
  });

  it('should return false if address is undefined', () => {
    const result = checkAddressIsChanged(undefined, currentBlapiAddress);
    expect(result).toBe(false);
  });

  it('should return false if currentBlapiAddress is undefined', () => {
    const result = checkAddressIsChanged(address, undefined);
    expect(result).toBe(false);
  });

  it('should return false if address and currentBlapiAddress are exactly same', () => {
    const result = checkAddressIsChanged(address, {
      ...currentBlapiAddress,
      line4: 'county',
      zip: 'postcode',
      country: 'GB',
    });
    expect(result).toBe(false);
  });

  it('should return false if address and currentBlapiAddress are same but different casing', () => {
    const result = checkAddressIsChanged(address, {
      ...currentBlapiAddress,
      line4: 'County',
      zip: 'Postcode',
      country: 'gb',
    });
    expect(result).toBe(false);
  });

  it('should return true if address and currentBlapiAddress are different', () => {
    const result = checkAddressIsChanged(address, currentBlapiAddress);
    expect(result).toBe(true);
  });
});

describe('checkAddressPropertyIsChanged', () => {
  it('should return false if both values are the same', () => {
    const result = checkAddressPropertyIsChanged('line1', 'line1');
    expect(result).toBe(false);
  });

  it('should return false if both values are the same but different casing', () => {
    const result = checkAddressPropertyIsChanged('line1', 'Line1');
    expect(result).toBe(false);
  });

  it('should return true if both values are different', () => {
    const result = checkAddressPropertyIsChanged('line1', 'line2');
    expect(result).toBe(true);
  });

  it('should return true if both values are different but different casing', () => {
    const result = checkAddressPropertyIsChanged('line1', 'Line2');
    expect(result).toBe(true);
  });

  it('should return false if both values are undefined', () => {
    const result = checkAddressPropertyIsChanged(undefined, undefined);
    expect(result).toBe(false);
  });

  it('should return false if values are undefined and empty string', () => {
    const result = checkAddressPropertyIsChanged(undefined, '');
    expect(result).toBe(false);
  });
});
