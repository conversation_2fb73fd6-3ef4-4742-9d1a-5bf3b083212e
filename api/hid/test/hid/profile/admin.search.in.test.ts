import { afterAll, beforeAll, beforeEach, describe, expect, it, Mock, vi } from 'vitest';
import request from 'superagent';
import { Server } from '../../mock/server.js';
import { X_APP_ID_HEADER, X_APP_KEY_HEADER } from '../../../src/hid/header.js';
import { nextSeaWareClientWithEmail, nextTx, nextValidEmail } from 'haven-hid-domain/mock.js';
import {
  hidForEmailPlotAndSeaWare,
  hidStore,
  identityStore,
  mockBlapi,
  mockPlot,
  setupIdentity,
} from '../../helper.js';
import { getDependencies } from '../../../src/dependencies/dependencies.js';
import { OwnerProfile } from 'haven-identity-plot-client';
import { HavenServiceAccountIdType, returnValue, throwError, ValidEmail } from 'haven-hid-domain';
import { Guest } from 'haven-identity-blapi-client';

vi.mock('haven-identity-plot-client');
vi.mock('haven-hid-pgsql-adapter/adapter.js');
vi.mock('../../../src/dependencies/dependencies.js');

const server = new Server();

const TEST_APP_ID = 'TEST_APP';
const TEST_ADMIN_KEY = 'ADMIN-KEY';
const accessToken = '<jwt-token>';
const headers = {
  appId: TEST_APP_ID,
  appKey: TEST_ADMIN_KEY,
};

const mockDependencies = {
  identityManagement: identityStore,
  hidStore: hidStore,
  store: hidForEmailPlotAndSeaWare,
  blapi: mockBlapi,
  plot: mockPlot,
};
(getDependencies as Mock).mockReturnValue(mockDependencies);

describe('Search Identity', () => {
  const url = `${server.baseUrl}/admin/identity/search`;
  const makeRequest = async (
    body: { email: string },
    headers: {
      appId?: string;
      appKey?: string;
      jwt?: string;
    },
  ): Promise<request.Response> => {
    const fetch = request.post(url).ok(() => true);
    if (headers.appKey) {
      fetch.set(X_APP_KEY_HEADER, headers.appKey);
    }
    if (headers.appId) {
      fetch.set(X_APP_ID_HEADER, headers.appId);
    }
    return fetch.send(body);
  };

  beforeAll(async () => {
    await server.start();
  });

  afterAll(async () => {
    await server.stop();
  });

  beforeEach(() => {
    vi.resetAllMocks();
    (getDependencies as Mock).mockReturnValue(mockDependencies);
  });

  const lookupHid = async (email: ValidEmail) =>
    hidForEmailPlotAndSeaWare.getHIDForEmail(email, undefined, nextTx('test'));

  describe('access control', () => {
    it('returns access denied if missing MANAGEMENT_KEY', async () => {
      const { status, body } = await makeRequest({ email: '<EMAIL>' }, { ...headers, appKey: '' });
      expect(status).toBe(401);
      expect(body.code).toBe('NOT_AUTHORISED');
      expect(body.message).toBe('x-app-key header is required');
    });

    it('returns access denied if incorrect MANAGEMENT_KEY', async () => {
      const { status, body } = await makeRequest(
        { email: '<EMAIL>' },
        { ...headers, appKey: 'Invalid' },
      );
      expect(status).toBe(401);
      expect(body.code).toBe('NOT_AUTHORISED');
      expect(body.message).toBe('invalid x-app-key header');
    });
  });

  describe('ignores bad requests', () => {
    it('returns 200 for bad email', async () => {
      const { status, body } = await makeRequest({ email: 'b.com' }, headers);
      expect(status).toBe(200);
      expect(body).toEqual([]);
    });
  });

  describe('valid requests', () => {
    it('returns 200 response for an identity stored in database', async () => {
      const seawareClientWithEmail = nextSeaWareClientWithEmail();

      const identity = await setupIdentity(seawareClientWithEmail);

      const response = await makeRequest({ email: identity.email.email }, headers);

      expect(response.body).toEqual([
        expect.objectContaining({
          migrated: true,
          email: identity.email.email,
          firstName: identity.firstName,
          hid: identity.hid,
          lastName: identity.lastName,
          phoneNumber: identity.phoneNumber,
          title: identity.title,
          accounts: identity.accounts,
        }),
      ]);
      expect(response.status).toBe(200);
    });
    it('returns 200 response for an owner stored only in plot', async () => {
      const email = nextValidEmail();
      const owner: OwnerProfile = {
        title: 'Mr',
        firstName: 'First',
        lastName: 'Last',
        name: 'not used',
        ownerId: 1234567,
        mobilePhone: '000000',
        dayPhone: '000001',
        eveningPhone: '000002',
      };
      mockPlot.fetchProfile.mockResolvedValueOnce(owner);
      mockBlapi.fetchProfile.mockResolvedValueOnce(undefined);

      const response = await makeRequest({ email: email.email }, headers);

      const hid = await lookupHid(email);
      expect(hid).not.toBeUndefined();

      expect(response.body).toEqual([
        expect.objectContaining({
          hid: hid,
          email: email.email,
          title: owner.title,
          firstName: owner.firstName,
          lastName: owner.lastName,
          accounts: [
            {
              id: owner.ownerId,
              type: HavenServiceAccountIdType.OWNER_ID,
            },
          ],
          migrated: false,
        }),
      ]);
      expect(response.status).toBe(200);
    });
    it('returns 200 response for a guest stored only in seaware', async () => {
      const email = nextValidEmail();
      const guest: Guest = {
        title: 'Mr',
        firstName: 'First',
        lastName: 'Last',
        clientId: 1234567,
      };
      mockPlot.fetchProfile.mockResolvedValueOnce(undefined);
      mockBlapi.fetchProfile.mockResolvedValueOnce(guest);

      const response = await makeRequest({ email: email.email }, headers);

      const hid = await lookupHid(email);
      expect(hid).not.toBeUndefined();

      expect(response.body).toEqual([
        expect.objectContaining({
          hid: hid,
          email: email.email,
          title: guest.title,
          firstName: guest.firstName,
          lastName: guest.lastName,
          accounts: [
            {
              id: guest.clientId,
              type: HavenServiceAccountIdType.SEAWARE_CLIENT_ID,
            },
          ],
          migrated: false,
        }),
      ]);
      expect(response.status).toBe(200);
    });
    it('returns 200 response for a unknown user stored in plot and seaware', async () => {
      const email = nextValidEmail();
      const guest: Guest = {
        title: 'Mr',
        firstName: 'First',
        lastName: 'Last',
        clientId: 1234567,
      };
      const owner: OwnerProfile = {
        title: 'Mr',
        firstName: 'First',
        lastName: 'Last',
        name: 'not used',
        ownerId: 1234567,
        mobilePhone: '000000',
        dayPhone: '000001',
        eveningPhone: '000002',
      };
      mockPlot.fetchProfile.mockResolvedValueOnce(owner);
      mockBlapi.fetchProfile.mockResolvedValueOnce(guest);

      const response = await makeRequest({ email: email.email }, headers);

      const hid = await lookupHid(email);
      expect(hid).not.toBeUndefined();

      expect(response.body).toEqual([
        expect.objectContaining({
          hid: hid,
          email: email.email,
          title: guest.title,
          firstName: guest.firstName,
          lastName: guest.lastName,
          accounts: [
            {
              id: guest.clientId,
              type: HavenServiceAccountIdType.SEAWARE_CLIENT_ID,
            },
            {
              id: owner.ownerId,
              type: HavenServiceAccountIdType.OWNER_ID,
            },
          ],
          migrated: false,
        }),
      ]);
      expect(response.status).toBe(200);
    });

    it('returns 200 response for a unknown user', async () => {
      const email = nextValidEmail();
      mockPlot.fetchProfile.mockResolvedValueOnce(undefined);
      mockBlapi.fetchProfile.mockResolvedValueOnce(undefined);

      const response = await makeRequest({ email: email.email }, headers);

      const hid = await lookupHid(email);
      expect(hid).not.toBeUndefined();

      expect(response.body).toEqual([]);
      expect(response.status).toBe(200);
    });
  });
});
