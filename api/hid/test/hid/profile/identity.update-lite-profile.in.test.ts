import { afterAll, beforeAll, beforeEach, describe, expect, it, Mock, vi } from 'vitest';
import request, { Response } from 'superagent';
import { Server } from '../../mock/server.js';
import { X_APP_ID_HEADER, X_APP_KEY_HEADER } from '../../../src/hid/header.js';
import {
  HavenServiceAccount,
  HavenServiceAccountIdType as IdType,
  HID,
  UPDATE_PROFILE_SOURCE_SUFFIX,
  HavenIdentity,
} from 'haven-hid-domain';
import * as ulid from 'ulid';
import {
  nextHID,
  nextHIDToEmail,
  nextHIDToProfile,
  nextIdentity,
  nextInvalidEmail,
  nextPlotOwnerWithEmail,
  nextSeaWareClientWithEmail,
  nextValidEmail,
} from 'haven-hid-domain/mock.js';
import {
  PgHavenIdentityTransactionalStore,
  PgHIDForEmailPlotAndSeaWare,
} from 'haven-hid-pgsql-adapter/adapter.js';
import { jwtVerify } from 'jose';
import {
  getIdentity,
  hidForEmailPlotAndSeaWare,
  identityStore,
  lookupProfileAudits,
  mockBlapi,
  mockPlot,
  setupHIDAndEmail,
  setupIdentity,
  setupLiteIdentity,
} from '../../helper.js';

vi.mock('haven-identity-plot-client');
vi.mock('haven-hid-pgsql-adapter/adapter.js');
vi.mock('@havenengineering/module-haven-logging');

(PgHavenIdentityTransactionalStore as Mock).mockImplementation(() => identityStore);
(PgHIDForEmailPlotAndSeaWare as Mock).mockImplementation(() => hidForEmailPlotAndSeaWare);

vi.mock('haven-identity-blapi-client', async () => {
  const mod = await vi.importActual<typeof import('haven-identity-blapi-client')>(
    'haven-identity-blapi-client',
  );
  return {
    ...mod,
    BLAPI: vi.fn().mockImplementation(() => mockBlapi),
  };
});

vi.mock('haven-identity-plot-client', async () => {
  const mod = await vi.importActual<typeof import('haven-identity-plot-client')>(
    'haven-identity-plot-client',
  );
  return {
    ...mod,
    Plot: vi.fn().mockImplementation(() => mockPlot),
  };
});

const mocks = vi.hoisted(() => ({
  sendProfileChangedEmail: vi.fn(),
  sendEmailChangedEmail: vi.fn(),
}));

vi.mock('haven-hid-domain', async (original) => {
  const mod: Object = await original();
  return {
    ...mod,
    sendProfileChangedEmail: mocks.sendProfileChangedEmail,
    sendEmailChangedEmail: mocks.sendEmailChangedEmail,
  };
});

vi.mock('jose');
const mockVerify = jwtVerify as Mock;

const server = new Server();

const TEST_APP_ID = 'TEST_APP';
const TEST_MANAGEMENT_KEY = 'MANAGEMENT-KEY';
const accessToken = '<jwt-token>';
const headers = {
  appId: TEST_APP_ID,
  appKey: TEST_MANAGEMENT_KEY,
  jwt: accessToken,
};

describe('Update Lite Profile', () => {
  const getUrl = (hid: string): string => `${server.baseUrl}/identity/lite/${hid}`;
  type Payload = {
    email?: string;
    title?: string;
    name?: string;
    firstName?: string;
    lastName?: string;
    phoneNumber?: string;
  };

  const makeRequest = async (
    path: string,
    headers: {
      appId?: string;
      appKey?: string;
      jwt?: string;
    },
    body: Payload,
  ): Promise<Response> => {
    const patch = request.patch(path).ok(() => true);
    if (headers.appKey) {
      patch.set(X_APP_KEY_HEADER, headers.appKey);
    }
    if (headers.appId) {
      patch.set(X_APP_ID_HEADER, headers.appId);
    }
    if (headers.jwt) {
      patch.set('Authorization', `Bearer ${headers.jwt}`);
    }
    return patch.send(body);
  };

  beforeAll(async () => {
    await server.start();
  });

  afterAll(async () => {
    await server.stop();
  });

  beforeEach(() => {
    vi.clearAllMocks();
    vi.resetAllMocks();
  });

  describe('Lite User', async () => {
    describe('Email', async () => {
      let identity: HavenIdentity;
      let hid: HID;
      let url: string;
      let payload: Payload;

      beforeEach(async () => {
        const changedEmail = nextValidEmail();
        const currentEmail = nextValidEmail();

        payload = {
          email: changedEmail.email,
        };
        identity = await setupIdentity({
          email: currentEmail,
        });
        hid = identity.hid;
        url = getUrl(hid);
        mockMigratedToken(hid, []);
      });

      it('returns 200 response', async () => {
        const response = await makeRequest(url, headers, payload);

        expect(mocks.sendEmailChangedEmail).toHaveBeenCalledWith({
          email: payload.email,
          templateId: process.env.EMAIL_CHANGED_TEMPLATE_ID || '',
          name: `${identity.firstName} ${identity.lastName}`,
          oldEmail: identity.email.email,
        });
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(response.body).toEqual({
          hid: identity.hid,
        });
        expect(response.status).toBe(200);
      });

      it('returns 422 response for missing user', async () => {
        const unknownHID = nextHID();
        const url = getUrl(unknownHID);
        mockMigratedToken(unknownHID, []);

        const response = await makeRequest(url, headers, payload);

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(response.body).toEqual({
          code: 'INVALID_HID',
          message: 'No such HID',
          statusCode: 422,
        });
        expect(response.status).toBe(422);
      });

      it('returns 422 response for not a lite user', async () => {
        const seawareClient = nextSeaWareClientWithEmail();
        const identity = await setupIdentity(seawareClient);
        const hid = identity.hid;
        const url = getUrl(hid);
        mockMigratedToken(hid, [
          { id: seawareClient.seaWareClientId, type: IdType.SEAWARE_CLIENT_ID },
        ]);

        const response = await makeRequest(url, headers, payload);

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(response.body).toEqual({
          code: 'INVALID_HID',
          message: 'No such Lite HID',
          statusCode: 422,
        });
        expect(response.status).toBe(422);
      });

      it('returns access denied if missing MANAGEMENT_KEY', async () => {
        const { status, body } = await makeRequest(url, { ...headers, appKey: '' }, payload);

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(status).toBe(401);
        expect(body.code).toBe('NOT_AUTHORISED');
        expect(body.message).toBe('x-app-key header is required');
      });

      it('returns access denied if incorrect MANAGEMENT_KEY', async () => {
        const { status, body } = await makeRequest(url, { ...headers, appKey: 'Invalid' }, payload);

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(status).toBe(401);
        expect(body.code).toBe('NOT_AUTHORISED');
        expect(body.message).toBe('invalid x-app-key header');
      });

      it('returns 400 for bad HID', async () => {
        const url = `${server.baseUrl}/identity/lite/aaaaa`;
        const { status, body } = await makeRequest(url, headers, payload);

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(status).toBe(400);
        expect(body.code).toBe('VALIDATION');
        expect(body.message).toBe('params/hid must match pattern "[A-Z0-9]{12}"');
      });

      it('returns 400 for an email that does not pass validation', async () => {
        const email = nextInvalidEmail();
        const { status, body } = await makeRequest(url, headers, {
          ...payload,
          email,
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(status).toBe(400);
        expect(body.code).toBe('VALIDATION');
        expect(body.message).toBe('body/email is not a valid email');
      });

      it('returns 409 for an email that is already in use by seaware user', async () => {
        const existing = nextSeaWareClientWithEmail();
        mockBlapi.fetchProfile.mockResolvedValue({
          clientId: existing.seaWareClientId,
        });
        const { status, body } = await makeRequest(url, headers, {
          ...payload,
          email: existing.email.email,
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(status).toBe(409);
        expect(body.code).toBe('EMAIL_IN_USE');
        expect(body.message).toBe('email is already registered with seaware');
      });

      it('returns 409 for an email that is already in use by plot user', async () => {
        const existing = nextPlotOwnerWithEmail();
        mockPlot.fetchProfile.mockResolvedValue({
          ownerId: existing.plotOwnerId,
        });

        const { status, body } = await makeRequest(url, headers, {
          ...payload,
          email: existing.email.email,
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(status).toBe(409);
        expect(body.code).toBe('EMAIL_IN_USE');
        expect(body.message).toBe('email is already registered with owners');
      });

      it('returns 409 for an email that already has an identity registered', async () => {
        const existing = nextHIDToEmail();
        await setupIdentity(existing);

        const { status, body } = await makeRequest(url, headers, {
          ...payload,
          email: existing.email.email,
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(status).toBe(409);
        expect(body.code).toBe('EMAIL_IN_USE');
        expect(body.message).toBe('email is already registered with other identity');
      });

      it('returns 200 for an email that is linked to hid but not account or profile', async () => {
        const other = nextValidEmail().email;
        await setupHIDAndEmail(other);

        const { status, body } = await makeRequest(url, headers, {
          ...payload,
          email: other,
        });

        expect(mocks.sendEmailChangedEmail).toHaveBeenCalledWith({
          email: other,
          templateId: process.env.EMAIL_CHANGED_TEMPLATE_ID || '',
          name: `${identity.firstName} ${identity.lastName}`,
          oldEmail: identity.email.email,
        });
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body).toEqual({ hid });
        expect(status).toBe(200);
      });

      it('returns 200 for an unchanged email for migrated identity', async () => {
        const existing = nextIdentity();
        const identity = await setupIdentity(existing);

        mockMigratedToken(identity.hid, existing.accounts);

        const { status, body } = await makeRequest(getUrl(identity.hid), headers, {
          email: existing.email.email,
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body).toEqual({
          hid: identity.hid,
        });
        expect(status).toBe(200);
      });

      it('returns 401 response for a missing idtoken', async () => {
        const { status } = await makeRequest(
          url,
          {
            appId: headers.appId,
            appKey: headers.appKey,
          },
          payload,
        );

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(status).toBe(401);
      });

      it('returns 401 response for a bad idtoken', async () => {
        mockVerify.mockRejectedValue(new Error('Bad'));
        const { status } = await makeRequest(url, headers, payload);

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(status).toBe(401);
      });

      it('returns 403 for idtoken that does not match the path HID', async () => {
        mockVerify.mockResolvedValue({
          payload: { hid: 'not-this-x' },
        });
        const { status } = await makeRequest(url, headers, payload);

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(status).toBe(403);
      });

      it('updates nothing if nothing provided', async () => {
        const email = nextValidEmail();
        const identity = await setupIdentity({
          email: email,
        });

        mockMigratedToken(identity.hid, []);

        const { status, body } = await makeRequest(
          `${server.baseUrl}/identity/lite/${identity.hid}`,
          headers,
          {},
        );
        expect(body).toEqual({
          hid: identity.hid,
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(status).toBe(200);
        expect(mockPlot.updateProfile).not.toHaveBeenCalled();
        expect(mockBlapi.updateProfile).not.toHaveBeenCalled();
      });
    });

    describe('Name and phoneNumbers', () => {
      let identity: HavenIdentity;
      let hid: HID;
      let url: string;
      let payload: Payload = {};

      beforeEach(async () => {
        const email = nextHIDToEmail();
        identity = await setupLiteIdentity(email.email);
        hid = identity.hid;
        url = getUrl(hid);

        mockMigratedToken(hid, []);
      });

      it('should reject with 400 when TITLE is not a valid title', async () => {
        const { status, body } = await makeRequest(url, headers, {
          name: 'a',
          title: 'XX',
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body.message).toBe(`body/title must be equal to one of the allowed values`);
        expect(body.code).toBe('VALIDATION');
        expect(status).toBe(400);
      });

      it('should reject with 400 when NAME length is 0', async () => {
        const { status, body } = await makeRequest(url, headers, {
          name: '',
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body.message).toBe(`body/name must NOT have fewer than 1 characters`);
        expect(body.code).toBe('VALIDATION');
        expect(status).toBe(400);
      });

      it('should reject with 400 when NAME length is greater than 100', async () => {
        const { status, body } = await makeRequest(url, headers, {
          name: 'a'.repeat(101),
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body.message).toBe(`body/name must NOT have more than 100 characters`);
        expect(body.code).toBe('VALIDATION');
        expect(status).toBe(400);
      });

      it('should reject with 400 when FIRST NAME length is 0', async () => {
        const { status, body } = await makeRequest(url, headers, {
          name: 'a',
          firstName: '',
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body.message).toBe(`body/firstName must NOT have fewer than 1 characters`);
        expect(body.code).toBe('VALIDATION');
        expect(status).toBe(400);
      });

      it('should reject with 400 when FIRST NAME length is greater than 100', async () => {
        const { status, body } = await makeRequest(url, headers, {
          name: 'a',
          firstName: 'a'.repeat(101),
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body.message).toBe(`body/firstName must NOT have more than 100 characters`);
        expect(body.code).toBe('VALIDATION');
        expect(status).toBe(400);
      });

      it('should reject with 400 when LAST NAME length is 0', async () => {
        const { status, body } = await makeRequest(url, headers, {
          name: 'a',
          lastName: '',
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body.message).toBe(`body/lastName must NOT have fewer than 1 characters`);
        expect(body.code).toBe('VALIDATION');
        expect(status).toBe(400);
      });

      it('should reject with 400 when LAST NAME length is greater than 100', async () => {
        const { status, body } = await makeRequest(url, headers, {
          name: 'a',
          lastName: 'a'.repeat(101),
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body.message).toBe(`body/lastName must NOT have more than 100 characters`);
        expect(body.code).toBe('VALIDATION');
        expect(status).toBe(400);
      });

      it('should reject with 400 when PHONE NUMBER length is greater than 30', async () => {
        const { status, body } = await makeRequest(url, headers, {
          name: 'a',
          phoneNumber: '1'.repeat(31),
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body.message).toBe(`body/phoneNumber must NOT have more than 30 characters`);
        expect(body.code).toBe('VALIDATION');
        expect(status).toBe(400);
      });

      it('should reject with 400 when PHONE NUMBER is not a valid phone number', async () => {
        const { status, body } = await makeRequest(url, headers, {
          ...payload,
          name: 'a',
          phoneNumber: 'not valid number',
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body.message).toBe(`body/phoneNumber is not valid`);
        expect(body.code).toBe('VALIDATION');
        expect(status).toBe(400);
      });

      it('should respond with 200 and update profile', async () => {
        const email = nextHIDToEmail();
        const profile = nextHIDToProfile();
        const identity = await setupLiteIdentity(email.email, profile);
        const profileUpdate = { name: 'a' };

        mockMigratedToken(identity.hid, []);

        const { status, body } = await makeRequest(
          `${server.baseUrl}/identity/lite/${identity.hid}`,
          headers,
          {
            ...profileUpdate,
          },
        );

        const updatedProfile = await getIdentity(identity.hid);

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).toHaveBeenCalledWith({
          email: identity.email.email,
          templateId: process.env.PROFILE_CHANGED_TEMPLATE_ID || '',
          name: `${identity.firstName} ${identity.lastName}`,
          changedFields: ['name'],
        });
        expect(body).toEqual({
          hid: identity.hid,
        });
        expect(updatedProfile?.name).toEqual(profileUpdate.name);
        expect(status).toEqual(200);
      });

      it('X-APP-ID header to identify back end service issuing request', async () => {
        const { status, body } = await makeRequest(
          url,
          { ...headers, appId: '' },
          {
            ...payload,
            name: 'a',
          },
        );

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body.code).toEqual('FORBIDDEN');
        expect(body.message).toEqual('x-app-id header is missing');
        expect(status).toBe(403);
      });

      it('X-APP-KEY header to identify back end service issuing request', async () => {
        const { status, body } = await makeRequest(
          url,
          { ...headers, appKey: '' },
          {
            ...payload,
            name: 'a',
          },
        );

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body.code).toEqual('NOT_AUTHORISED');
        expect(body.message).toEqual('x-app-key header is required');
        expect(status).toBe(401);
      });

      it('should be audited and include x-app-id of requesting app', async () => {
        const txId = 'txId';
        const source = `${headers.appId}:${UPDATE_PROFILE_SOURCE_SUFFIX}`;

        vi.spyOn(ulid, 'ulid').mockReturnValue('txId');

        const { status, body } = await makeRequest(url, headers, {
          ...payload,
          name: 'a',
        });

        const audit = await lookupProfileAudits(txId, source);

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).toHaveBeenCalledWith({
          email: identity.email.email,
          templateId: process.env.PROFILE_CHANGED_TEMPLATE_ID || '',
          name: `${identity.firstName} ${identity.lastName}`,
          changedFields: ['name'],
        });
        expect(body).toEqual({ hid });
        expect(status).toEqual(200);
        expect(audit).toEqual([
          expect.objectContaining({
            source,
            tx: txId,
            type: 'profile',
          }),
        ]);
      });
    });

    describe('Empty changedFields scenarios', () => {
      let identity: HavenIdentity;
      let hid: HID;
      let url: string;

      beforeEach(async () => {
        const email = nextHIDToEmail();
        identity = await setupLiteIdentity(email.email);
        hid = identity.hid;
        url = getUrl(hid);
        mockMigratedToken(hid, []);
      });

      it('should not send profile changed email when no profile fields are provided', async () => {
        const { status, body } = await makeRequest(url, headers, {
          email: nextValidEmail().email,
        });

        expect(mocks.sendEmailChangedEmail).toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body).toEqual({ hid });
        expect(status).toBe(200);
      });

      it('should not send profile changed email when only email is updated', async () => {
        const newEmail = nextValidEmail().email;

        const { status } = await makeRequest(url, headers, {
          email: newEmail,
        });

        expect(mocks.sendEmailChangedEmail).toHaveBeenCalledWith({
          email: newEmail,
          templateId: process.env.EMAIL_CHANGED_TEMPLATE_ID || '',
          name: `${identity.firstName} ${identity.lastName}`,
          oldEmail: identity.email.email,
        });
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(status).toBe(200);
      });

      it('should not send profile changed email when profile fields are identical to existing values', async () => {
        const existingProfile = {
          name: 'Test User',
          firstName: 'Test',
          lastName: 'User',
          phoneNumber: '01234567890',
        };
        const email = nextHIDToEmail();
        const knownIdentity = await setupLiteIdentity(email.email, existingProfile);
        const knownUrl = getUrl(knownIdentity.hid);

        mockMigratedToken(knownIdentity.hid, []);

        const { status, body } = await makeRequest(knownUrl, headers, {
          name: existingProfile.name,
          firstName: existingProfile.firstName,
          lastName: existingProfile.lastName,
          phoneNumber: existingProfile.phoneNumber,
        });

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body).toEqual({ hid: knownIdentity.hid });
        expect(status).toBe(200);
      });

      it('should handle empty request body without errors', async () => {
        const { status, body } = await makeRequest(url, headers, {});

        expect(mocks.sendEmailChangedEmail).not.toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body).toEqual({ hid });
        expect(status).toBe(200);
      });

      it('should handle email change with empty profile', async () => {
        const newEmail = nextValidEmail().email;

        const { status, body } = await makeRequest(url, headers, {
          email: newEmail,
          name: undefined,
          firstName: undefined,
          lastName: undefined,
        } as any);

        expect(mocks.sendEmailChangedEmail).toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body).toEqual({ hid });
        expect(status).toBe(200);
      });

      it('should not send profile changed email when request contains only email', async () => {
        const newEmail = nextValidEmail().email;

        const { status, body } = await makeRequest(url, headers, {
          email: newEmail,
          // No profile fields at all
        });

        expect(mocks.sendEmailChangedEmail).toHaveBeenCalled();
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(body).toEqual({ hid });
        expect(status).toBe(200);
      });
    });

    describe('Profile change detection edge cases', () => {
      let identity: HavenIdentity;
      let hid: HID;
      let url: string;

      beforeEach(async () => {
        const email = nextHIDToEmail();
        identity = await setupLiteIdentity(email.email);
        hid = identity.hid;
        url = getUrl(hid);
        mockMigratedToken(hid, []);
      });

      it('should detect when profile has actual changes vs no changes', async () => {
        const { status: status1 } = await makeRequest(url, headers, {
          name: 'New Name Different From Existing',
        });

        expect(mocks.sendProfileChangedEmail).toHaveBeenCalledWith(
          expect.objectContaining({
            changedFields: ['name'],
          }),
        );
        expect(status1).toBe(200);

        vi.clearAllMocks();

        // Second request with no changes (same value)
        const { status: status2 } = await makeRequest(url, headers, {
          name: 'New Name Different From Existing',
        });

        // No changes
        expect(mocks.sendProfileChangedEmail).not.toHaveBeenCalled();
        expect(status2).toBe(200);
      });
    });
  });

  const mockMigratedToken = (hid: HID, accounts: HavenServiceAccount[]) => {
    mockVerify.mockResolvedValue({
      payload: {
        sub: hid,
        hid: hid,
        accounts: accounts,
      },
    });
  };
});
