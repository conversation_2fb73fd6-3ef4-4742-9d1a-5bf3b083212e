import { describe, expect, it, vi } from 'vitest';
import { checkBlockedEmailDomains } from '../../src/identity/owners.js';
import { ValidationError } from '../../src/error.js';

describe('checkOwnersEmailDomain', () => {
  it('should not throw any error when blocked email domains are not defined', () => {
    vi.stubEnv('OWNERS_BLOCKED_EMAIL_DOMAINS', '');
    const email = '<EMAIL>';

    expect(() => checkBlockedEmailDomains(email)).not.toThrow();
  });

  it('should throw ValidationError when email domain is blocked', () => {
    vi.stubEnv('OWNERS_BLOCKED_EMAIL_DOMAINS', 'example.com,test.com');
    const email = '<EMAIL>';

    expect(() => checkBlockedEmailDomains(email)).toThrow(ValidationError);
  });

  it('should not throw any error when email domain is not blocked', () => {
    vi.stubEnv('OWNERS_BLOCKED_EMAIL_DOMAINS', 'example.com,test.com');
    const email = '<EMAIL>';

    expect(() => checkBlockedEmailDomains(email)).not.toThrow();
  });
});
