import { describe, expect, it } from 'vitest';
import {
  environmentName<PERSON>romToggle,
  Feature<PERSON><PERSON><PERSON><PERSON><PERSON>,
  FeatureFlag,
  overrideUnleashToggle,
  createConstantFeatureFlagChecker,
} from '../../src/services/unleash.js';

describe('Unleash override tests', async () => {
  const flagIsFalse = createConstantFeatureFlagChecker(false);
  const flagIsTrue = createConstantFeatureFlagChecker(true);

  it('should convert toggle names to reasonable environment variable names', () => {
    expect(environmentNameFromToggle(FeatureFlag.IDENTITY_MASTERING_ADDRESS)).toEqual(
      'UNLEASH_IDENTITY_MASTERING_ADDRESS',
    );
  });

  it('should use the unleash value when environment variable is not set', async () => {
    expect(flagIsFalse.isEnabled(FeatureFlag.IDENTITY_MASTERING_ADDRESS)).toBeFalsy();

    expect(flagIsTrue.isEnabled(FeatureFlag.IDENTITY_MASTERING_ADDRESS)).toBeTruthy();
  });

  it('should set the environment variable to override', () => {
    overrideUnleashToggle(FeatureFlag.IDENTITY_MASTERING_ADDRESS, true);
    expect(process.env.UNLEASH_IDENTITY_MASTERING_ADDRESS).toEqual('true');

    overrideUnleashToggle(FeatureFlag.IDENTITY_MASTERING_ADDRESS, false);
    expect(process.env.UNLEASH_IDENTITY_MASTERING_ADDRESS).toEqual('false');
  });

  it('should override unleash value when environment variable is set to true', async () => {
    overrideUnleashToggle(FeatureFlag.IDENTITY_MASTERING_ADDRESS, true);
    expect(flagIsFalse.isEnabled(FeatureFlag.IDENTITY_MASTERING_ADDRESS)).toBeTruthy();
  });

  it('should override unleash value when environment variable is set to false', async () => {
    overrideUnleashToggle(FeatureFlag.IDENTITY_MASTERING_ADDRESS, false);
    expect(flagIsTrue.isEnabled(FeatureFlag.IDENTITY_MASTERING_ADDRESS)).toBeFalsy();
  });
});
