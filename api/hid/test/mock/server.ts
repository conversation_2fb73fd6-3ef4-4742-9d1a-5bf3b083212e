import { check, waitUntilUsed } from 'tcp-port-used';
import { RateLimit, server, start } from '../../src/hid-api.js';
export class ServerConfig {
  public port = generateRandomPortNumber();
  public host = '127.0.0.1';
}

export class Server {
  config: ServerConfig;
  rateLimit?: RateLimit;
  server: typeof server | undefined;

  constructor() {
    this.config = new ServerConfig();
  }

  public async start(): Promise<Server> {
    const isRunning = await check(this.config.port, this.config.host);
    if (!isRunning) {
      console.log('Starting new server instance');

      if (this.rateLimit) {
        await start(this.config.port, this.rateLimit);
      } else {
        await start(this.config.port);
      }
      await waitUntilUsed(this.config.port, 50, 5000);
      this.server = server;
    } else {
      console.log('Using pre-existing server instance');
    }

    return this;
  }

  public async stop() {
    if (this.server) {
      await this.server.close();
    }
  }

  get baseUrl(): string {
    return `http://${this.config.host}:${this.config.port}`;
  }

  get host(): string {
    return this.config.host;
  }

  get port(): number {
    return this.config.port;
  }

  withRateLimit(rateLimit: RateLimit) {
    this.rateLimit = rateLimit;
    return this;
  }
}

const generateRandomPortNumber = (): number => {
  const max = 10000;
  const min = 3000;

  return Math.floor(Math.random() * (max - min) + min);
};
