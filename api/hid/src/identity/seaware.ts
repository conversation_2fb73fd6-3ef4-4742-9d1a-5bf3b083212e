import { BLA<PERSON>, BLAPIAccount, Guest } from 'haven-identity-blapi-client';
import { HavenServiceAccountIdType } from 'haven-hid-domain';
import { EmailExistsError } from '../error.js';

export const createBLAPIClient = (): BLAPI => {
  return new BLAPI(
    process.env.BLAPI_URL || '',
    process.env.BLAPI_X_API_KEY || '',
    process.env.BLAPI_IDENTITY_URL || '',
    process.env.BLAPI_X_IDENTITY_KEY || '',
  );
};

export const createBLAPIAccountClient = (): BLAPIAccount => {
  return new BLAPIAccount(
    process.env.BLAPI_ACCOUNT_URL || '',
    process.env.BLAPI_ACCOUNT_X_API_KEY || '',
  );
};

const guestDoesNotMatchId = (guest: Guest, id: number, idType: HavenServiceAccountIdType) =>
  !(guest.clientId === id && idType === HavenServiceAccountIdType.SEAWARE_CLIENT_ID);

export const checkGuestIsNotRegisteredWithSeaware = (
  guest: Guest | undefined,
  id: number,
  idType: HavenServiceAccountIdType,
) => {
  if (guest && guestDoesNotMatchId(guest, id, idType)) {
    throw new EmailExistsError('email is already registered with seaware');
  }
  return;
};
