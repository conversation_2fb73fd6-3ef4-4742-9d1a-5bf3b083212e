import { OwnerProfile, Plot } from 'haven-identity-plot-client';
import { HavenServiceAccountIdType } from 'haven-hid-domain';
import { ValidationError, EmailExistsError } from '../error.js';

export const createPlotClient = (): Plot => {
  return new Plot(process.env.PLOT_API_URL || '', process.env.PLOT_API_KEY || '');
};

const ownerDoesNotMatchId = (owner: OwnerProfile, id: number, idType: HavenServiceAccountIdType) =>
  !(owner.ownerId === id && idType === HavenServiceAccountIdType.OWNER_ID);

export const checkOwnerNotRegisteredWithPlot = (
  existing: OwnerProfile | undefined,
  id: number,
  idType: HavenServiceAccountIdType,
) => {
  if (existing && ownerDoesNotMatchId(existing, id, idType)) {
    throw new EmailExistsError('email is already registered with owners');
  }
};

export const checkBlockedEmailDomains = (email: string) => {
  const blockedDomainsConfig = process.env.OWNERS_BLOCKED_EMAIL_DOMAINS;
  const blockedDomains = blockedDomainsConfig ? blockedDomainsConfig?.split(',') : [];

  if (
    !blockedDomains ||
    !blockedDomains.length ||
    blockedDomains.every((domain) => !email.includes(domain))
  ) {
    return;
  }

  throw new ValidationError('Email domain not allowed');
};
