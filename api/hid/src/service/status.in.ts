import {
  FastifyPluginAsyncJsonSchemaToTs,
  JsonSchemaToTsProvider,
} from '@fastify/type-provider-json-schema-to-ts';

const URI = '/status';

const statusResponse = {
  type: 'object',
  $id: 'status',
  properties: {
    healthy: { type: 'boolean' },
    version: { type: 'string' },
  },
  description: 'Health status',
} as const;

const statusEndpoint = {
  schema: {
    response: {
      200: statusResponse,
    },
  },
  config: {
    openapi: {
      description: 'Haven Identity status endpoint',
      summary: 'Status',
      tags: ['Health'],
    },
    rateLimit: false,
    method: 'GET',
    url: URI,
  },
} as const;

export const status: FastifyPluginAsyncJsonSchemaToTs = (fastify) => {
  const version = process.env.APP_VERSION || 'unknown';
  const server = fastify.withTypeProvider<JsonSchemaToTsProvider>();

  server.addSchema(statusResponse);

  fastify.get(URI, statusEndpoint, async (_request, reply): Promise<void> => {
    await reply.code(200).send({
      healthy: true,
      version: version,
    });
  });

  return Promise.resolve();
};
