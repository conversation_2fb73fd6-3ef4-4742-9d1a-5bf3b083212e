import { AccessError, ForbiddenError } from '../error.js';
import { FastifyReply, FastifyRequest } from 'fastify';
import AuthenticationConfig, { Role } from '../config/authentication-config.js';
import { JWTPayload } from 'jose';
import { HavenServiceAccount, HavenServiceAccountIdType } from 'haven-hid-domain';

export const checkAccess =
  (config: AuthenticationConfig, role: Role) =>
  (req: FastifyRequest, _reply: FastifyReply, done: () => void) => {
    const appId = req.headers['x-app-id'] as string;
    const appKey = req.headers['x-app-key'] as string;
    if (!appId) throw new ForbiddenError('x-app-id header is missing');
    if (!appKey) throw new AccessError('x-app-key header is required');

    if (!config.getKeys(role).includes(appKey)) throw new AccessError('invalid x-app-key header');
    done();
  };

export const validateHid = (hid: string, payload: JWTPayload) => {
  if (hid !== payload.hid) {
    throw new ForbiddenError('Access token HID does not match request path');
  }
};

export const validateAccount = (
  id: number | undefined,
  idType: HavenServiceAccountIdType | undefined,
  payload: JWTPayload,
) => {
  const accounts = payload.accounts as HavenServiceAccount[] | undefined;
  if ((id || idType) && (!accounts || !accounts.find((it) => it.id === id && it.type === idType))) {
    throw new ForbiddenError('Access token accounts do not match request');
  }
};
