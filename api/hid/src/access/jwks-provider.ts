import { createRemoteJWKSet } from 'jose';
export type JWKS = ReturnType<typeof createRemoteJWKSet>;
export interface JWKSConfig {
  jwks: () => JWKS;
}
const keys: Record<string, JWKS> = {};

export const jwksProvider = (url: string = process.env.IDENTITY_JWKS_URL || ''): JWKSConfig => {
  if (!url) {
    throw new Error('IDENTITY_JWKS_URL env-var is not set');
  }

  return {
    jwks: (): JWKS => {
      if (!keys[url]) {
        const jwks = createRemoteJWKSet(new URL(url), {
          headers: {
            'User-Agent': 'hid-jwt-verify',
          },
        });
        keys[url] = jwks;
      }
      return keys[url];
    },
  };
};
