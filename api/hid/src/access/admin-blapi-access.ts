import { SyncedHavenIdentity } from 'haven-hid-api';
import { HavenServiceAccount, HID } from 'haven-hid-domain';
import { SignJWT } from 'jose';
import { importJWK, JWK, KeyLike, JWTPayload } from 'jose';

export interface JWTKey {
  issuer: string;
  kid: string;
  alg: string;
  key: KeyLike | Uint8Array;
}

export interface AuthToken extends JWTPayload {
  hid: HID;
  migrated: boolean;
  accounts: HavenServiceAccount[];
}

export interface OnBehalfOfJWTPayload extends AuthToken {
  onBehalfOf: {
    prn: HID;
    ctx: string;
    identity: Partial<SyncedHavenIdentity>;
  };
}

export const ADMIN_USER_UPN_HEADER = 'x-admin-upn';

export const getJWK = async (): Promise<JWTKey> => {
  const alg = 'RS256';
  const json = process.env.JWKS;
  if (!json) {
    throw new Error('Missing JWKS configuration');
  }
  const issuer = process.env.OIDC_ISSUER;
  if (!issuer) {
    throw new Error('Missing OIDC_ISSUER for signed token verification');
  }
  const jwks = JSON.parse(json) as { keys: JWK[] };
  const jwk = jwks.keys[0];
  if (!jwk.kid) throw new Error('JWK has no kid');
  if (!jwk.kty) throw new Error('JWK has no kty');
  if (jwk.kty !== 'RSA') throw new Error('JWK kty is not RSA!');

  const key = await importJWK({ ...jwk, alg: alg });
  return {
    issuer,
    kid: jwk.kid,
    key: key,
    alg,
  };
};

export const buildOnBehalfOfJwt = (
  payload: AuthToken,
  adminUserUpn: string,
): OnBehalfOfJWTPayload => {
  const now = Date.now();
  return {
    ...payload,
    sub: adminUserUpn,
    auth_time: now,
    iat: now / 1000,
    exp: now / 1000 + 300,
    onBehalfOf: {
      prn: payload.hid,
      ctx: 'https://admin.haven.com/identity',
      identity: {
        migrated: payload.migrated,
      },
    },
  };
};

export const signJwt = async (key: JWTKey, jwt: OnBehalfOfJWTPayload): Promise<string> => {
  const header = { alg: key.alg, kid: key.kid, typ: 'JWT' };
  return await new SignJWT(jwt).setProtectedHeader(header).sign(key.key);
};

export const generateBlapiBearerToken = async (
  { hid, accounts, migrated }: AuthToken,
  userUpn: string,
): Promise<string> => {
  const iss = process.env.OIDC_ISSUER;
  if (!iss) {
    throw new Error('Missing OIDC_ISSUER for signed token verification');
  }

  const jwtKey = await getJWK();
  const payload = buildOnBehalfOfJwt(
    {
      hid,
      accounts,
      iss,
      migrated,
    },
    userUpn,
  );

  return await signJwt(jwtKey, payload);
};
