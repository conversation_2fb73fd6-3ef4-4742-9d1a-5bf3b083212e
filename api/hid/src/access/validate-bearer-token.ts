import { JWTPayload, jwtVerify } from 'jose';
import { logger } from '@havenengineering/module-haven-logging';
import { JWKS } from './jwks-provider.js';
import { AccessError } from '../error.js';
import { AuthenticationMethodType, HavenServiceAccount } from 'haven-hid-domain';
import { SyncedHavenIdentity } from '../exports.js';

export interface IdentityTokenPayload extends JWTPayload {
  auth_method: AuthenticationMethodType;
  accounts: HavenServiceAccount[];
}

export type OnBehalfOfTokenPayload = {
  prn: string;
  ctx: string;
  identity: SyncedHavenIdentity;
};

export interface OnBehalfOfToken extends JWTPayload {
  accounts: HavenServiceAccount[];
  onBehalfOf: OnBehalfOfTokenPayload;
}

export const getBearerToken = (authorization: string | undefined): string => {
  if (!authorization || !authorization.startsWith('Bearer')) {
    logger.warn(`Invalid authorization: ${authorization}`);
    throw new AccessError('No bearer token for identity');
  }
  return authorization.replace('Bearer', '').trim();
};
export const validateBearerToken = async (
  jwks: JWKS,
  token: string,
): Promise<IdentityTokenPayload | OnBehalfOfToken> => {
  try {
    const { payload } = await jwtVerify(token, jwks);
    return <IdentityTokenPayload | OnBehalfOfToken>payload;
  } catch (e: unknown) {
    const message = (e as Error).message;
    logger.warn(message);
    throw new AccessError('Invalid token:' + message);
  }
};
