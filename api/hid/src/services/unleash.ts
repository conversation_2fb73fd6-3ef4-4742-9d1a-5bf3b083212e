import { FastifyRequest } from 'fastify';
import { startUnleash } from 'unleash-client';
import { logger } from '@havenengineering/module-haven-logging';

export enum FeatureFlag {
  IDENTITY_MASTERING_ADDRESS = 'identity-mastering-address',
}

export type FlagCheckFunction = (toggle: string) => boolean;

export type FeatureFlagChecker = {
  isEnabled: (toggle: string, req?: FastifyRequest) => boolean;
};

const createFunctionFeatureFlagChecker = (
  realFlagCheckFunction: FlagCheckFunction,
): FeatureFlagChecker => {
  return {
    isEnabled: (toggle: string, req?: FastifyRequest): boolean => {
      const featureFlagOverride = getFeatureFlagOverride(toggle, req);
      if (featureFlagOverride !== undefined) {
        return featureFlagOverride;
      }
      return realFlagCheckFunction(toggle);
    },
  };
};

export const createFeatureFlagChecker = async (): Promise<FeatureFlagChecker | undefined> => {
  try {
    const unleash = await startUnleash({
      url: `${process.env.UNLEASH_API_URL}`,
      appName: 'service-haven-identity',
      customHeaders: { Authorization: `${process.env.UNLEASH_API_KEY}` },
    });
    return createFunctionFeatureFlagChecker((toggle: string) => {
      return unleash.isEnabled(toggle);
    });
  } catch (error) {
    logger.error(`Error initializing Unleash: `, error);

    return;
  }
};

export const createConstantFeatureFlagChecker = (value: boolean): FeatureFlagChecker => {
  return createFunctionFeatureFlagChecker(() => {
    return value;
  });
};

export const environmentNameFromToggle = (toggle: string): string => {
  return 'UNLEASH_' + toggle.toUpperCase().replaceAll('-', '_');
};

export const overrideUnleashToggle = (toggle: FeatureFlag, value: boolean): void => {
  process.env[environmentNameFromToggle(toggle)] = value ? 'true' : 'false';
};

export const getFeatureFlagOverride = (
  toggle: string,
  req?: FastifyRequest,
): boolean | undefined => {
  if (req?.cookies[toggle]) {
    return req.cookies[toggle] === 'true';
  }
  const envName = environmentNameFromToggle(toggle);
  const envOverride = process?.env[envName];
  if (envOverride) {
    return envOverride === 'true';
  }
  return undefined;
};
