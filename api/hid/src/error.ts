interface Metadata {
  [key: string]: unknown;
}

class ApiError implements Error {
  readonly name!: string;
  readonly code!: string;
  readonly message!: string;
  readonly statusCode!: number;
  readonly metadata?: Metadata;

  constructor(
    name: string,
    code: string,
    message: string,
    statusCode: number,
    metadata?: Metadata,
  ) {
    this.name = name || 'ApiError';
    this.code = code;
    this.message = message;
    this.statusCode = statusCode;
    this.metadata = metadata || {};
  }
}

export class ValidationError extends ApiError {
  constructor(message: string, metadata?: Metadata) {
    super('ValidationError', 'VALIDATION', message, 400, metadata);
  }
}

export class EmailExistsError extends ApiError {
  constructor(message: string) {
    super('ValidationError', 'EMAIL_IN_USE', message, 409);
  }
}

export class UnknownIdentityError extends ApiError {
  constructor(message: string) {
    super('UnknownIdentity', 'INVALID_HID', message, 422);
  }
}

export class ForbiddenError extends ApiError {
  constructor(message: string) {
    super('AccessError', 'FORBIDDEN', message, 403);
  }
}

export class AccessError extends ApiError {
  constructor(message: string) {
    super('AccessError', 'NOT_AUTHORISED', message, 401);
  }
}

export class UpdatePasswordAuthorizationError extends ApiError {
  constructor(message: string) {
    super('UpdatePasswordAuthorizationError', 'UPDATE_PASSWORD_NOT_AUTHORISED', message, 401);
  }
}
