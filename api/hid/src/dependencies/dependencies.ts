import {
  HavenIdentityTransactionalStore,
  HIDEmailVerificationRequestGenerator,
  HIDForEmailPlotAndSeaWare,
  HIDStore,
  RightToBeForgotten,
} from 'haven-hid-domain';
import {
  PgHavenIdentityTransactionalStore,
  PgHIDForEmailPlotAndSeaWare,
  PgRightToBeForgotten,
  PgHidStore,
  PgEmailVerificationRequestStore,
  PgEmailStore,
} from 'haven-hid-pgsql-adapter';
import { createBLAPIClient, createBLAPIAccountClient } from '../identity/seaware.js';
import { createPlotClient } from '../identity/owners.js';
import { JWKSConfig, jwksProvider } from '../access/jwks-provider.js';
import { BLAPI, BLAPIAccount } from 'haven-identity-blapi-client';
import { Plot } from 'haven-identity-plot-client';

type Dependencies = {
  hidStore: HIDStore;
  identityManagement: HavenIdentityTransactionalStore;
  store: HIDForEmailPlotAndSeaWare;
  blapi: BLAPI;
  blapiAccount: BLAPIAccount;
  plot: Plot;
  keysProvider: JWKSConfig;
  rightToBeForgotten: RightToBeForgotten;
  emailVerification: HIDEmailVerificationRequestGenerator;
};

let instance: Dependencies;

export const getDependencies = () => {
  if (!instance) {
    const hidForEmail = PgHIDForEmailPlotAndSeaWare();
    const emailVerificationRequestStore = PgEmailVerificationRequestStore();
    const emailStore = PgEmailStore();
    const emailVerification = new HIDEmailVerificationRequestGenerator(
      hidForEmail,
      emailVerificationRequestStore,
      emailStore,
    );
    instance = {
      identityManagement: PgHavenIdentityTransactionalStore(),
      blapi: createBLAPIClient(),
      blapiAccount: createBLAPIAccountClient(),
      plot: createPlotClient(),
      keysProvider: jwksProvider(),
      store: hidForEmail,
      hidStore: PgHidStore(),
      rightToBeForgotten: PgRightToBeForgotten(),
      emailVerification: emailVerification,
    };
  }

  return instance;
};
