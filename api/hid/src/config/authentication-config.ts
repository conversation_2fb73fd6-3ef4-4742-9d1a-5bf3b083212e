export const enum Role {
  ADMIN = 'admin',
  USER_MANAGEMENT = 'user_management',
  CLIENT = 'client',
}
type Key = string;
type Client = {
  client: string;
  key: Key;
  roles: Role[];
};
type GroupedKeysByRole = Record<Role, Key[]>;
type AuthenticationConfigJson = {
  clients: Client[];
};

const parseClients = (json: string): Client[] =>
  (JSON.parse(json) as AuthenticationConfigJson).clients;

const defaultRole = Role.CLIENT;
const groupClientKeysByRole = (clients: Client[]) =>
  clients.reduce<GroupedKeysByRole>((groupedKeys, { key, roles }) => {
    const clientRoles = roles || [defaultRole];
    clientRoles.forEach((role) => {
      groupedKeys[role] ??= [];
      groupedKeys[role] = [...groupedKeys[role], key];
    });
    return groupedKeys;
  }, {} as GroupedKeysByRole);

export class AuthenticationConfig {
  private clients: Client[] = [];
  private keys = {} as GroupedKeysByRole;

  constructor() {
    if (process.env.AUTHENTICATION_CONFIG)
      this.clients = parseClients(process.env.AUTHENTICATION_CONFIG);

    this.keys = groupClientKeysByRole(this.clients);
  }

  getKeys(role: Role): Key[] {
    return this.keys[role] || [];
  }
}

export default AuthenticationConfig;
