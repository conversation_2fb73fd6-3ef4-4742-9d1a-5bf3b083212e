export const hidApiOverview = {
  openapi: {
    openapi: '3.0.3', // Note: 3.1 is not supported - so you can't have multiple examples fo usages it the docs
    info: {
      title: 'Haven Identity (HID) Service',
      description: 'Generation of unique identity (HID) for Haven customers',
      contact: {
        name: 'Haven Engineering',
        url: 'https://haven.com',
        email: '<EMAIL>',
      },
      license: {
        name: 'ISC',
        url: `https://choosealicense.com/licenses/isc/`,
      },
      version: process.env.APP_VERSION || 'DEVELOPMENT',
    },
    servers: [
      {
        url: 'http://service-haven-identity-hid.identity.svc:3000/',
        description: 'Production internal',
      },
      {
        url: 'http://service-haven-identity-hid.dev-identity.svc:3000/',
        description: 'Development internal',
      },
      {
        url: 'https://service-haven-identity-hid.dev.haven-leisure.com/',
        description: 'Development via gateway',
      },
      {
        url: `http://localhost:3000/`,
        description: 'Local testing',
      },
    ],
    tags: [
      {
        name: 'HID',
        description: 'Unique Haven Identity',
      },
    ],
    components: {
      securitySchemes: {
        appKeyAuth: {
          type: 'apiKey',
          in: 'header',
          name: 'X-APP-KEY',
        },
        appKeyId: {
          type: 'apiKey',
          in: 'header',
          name: 'X-APP-ID',
        },
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
      },
    },
  },
};
