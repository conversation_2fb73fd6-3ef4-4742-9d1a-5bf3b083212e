import { HavenApm } from '@havenengineering/module-shared-apm';
import { logger } from '@havenengineering/module-haven-logging';

HavenApm.Start({
  tags: {
    platform: 'identity',
  },
});

if (HavenApm.Context) {
  HavenApm.Context.use('http', {
    headers: ['x-app-id'],
    blocklist: ['/status', '/metrics'],
  });
} else {
  logger.info('No Datadog Context');
}

logger.info('Configured APM');

const setup = () => {
  // Setup is actually done on import so that code is instrumented before fastify is created.
  // note: the export from this file will still need to be assigned/or used otherwise the unused import is not applied
  // see https://docs.datadoghq.com/tracing/trace_collection/dd_libraries/nodejs/?tab=containers#adding-the-tracer-in-code
};

export default setup;
