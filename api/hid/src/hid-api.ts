import fastifyRateLimit from '@fastify/rate-limit';
import cookies from '@fastify/cookie';
import { fastify, FastifyRequest } from 'fastify';
import fastifyOpenapiDocs from 'fastify-openapi-docs';
import {
  addRequestTimestamp,
  defaultError<PERSON><PERSON><PERSON>,
  logR<PERSON>ponseDuration,
  logger,
  notfound<PERSON>andler,
} from 'haven-fastify';

import { checkAccess } from './access/check-access.js';
import AuthenticationConfig, { Role } from './config/authentication-config.js';
import { hidApiOverview } from './docs.js';
import { HIDsForEmails } from './hid/email/batch.hid.email.in.js';
import { HIDsForEmailUpdates } from './hid/email/batch.hid.email-updates.in.js';
import { EmailForHID } from './hid/email/email.hid.in.js';
import { HIDForEmail } from './hid/email/hid.email.in.js';
import metrics from './service/metrics.in.js';
import { status } from './service/status.in.js';
import { eraseIdentityForEmail } from './hid/admin/admin.erase-identity.in.js';
import { updateProfile } from './hid/profile/identity.update-profile.in.js';
import { updateLiteProfile } from './hid/profile/identity.update-lite-profile.in.js';
import { updatePassword } from './hid/password/identity.update-password.in.js';
import { updateLitePassword } from './hid/password/identity.update-lite-password.in.js';
import { getIdentity } from './hid/admin/admin.get-identity.in.js';
import { getBlapiProfile } from './hid/admin/admin.get-blapi-profile.in.js';
import { getBlapiBookings } from './hid/admin/admin.get-blapi-bookings.in.js';
import { updateBalpiEmail } from './hid/admin/admin.update-blapi-email.in.js';
import { linkAccount } from './hid/account/hid.link-account.in.js';
import { unlinkAccount } from './hid/admin/admin.unlink-account.in.js';
import { isEmailRegistered } from './hid/email/email.is-registered.in.js';
import { searchIdentity } from './hid/admin/admin.search.in.js';
import { generateEmailVerificationToken } from './hid/email/email.generate-verification-token.in.js';
import { getBlapiBooking } from './hid/admin/admin.get-blapi-booking.in.js';
import { getBlapiBookingCyhh } from './hid/admin/admin.get-blapi-booking-cyhh.in.js';

export const server = fastify({
  disableRequestLogging: true,
  logger: logger,
});

export const start = async (
  defaultPort = 3000,
  defaultRateLimiting: RateLimit = {
    max: 6000,
    timeWindow: '1 minute',
  },
) => {
  try {
    const authenticationConfig = new AuthenticationConfig();

    const port = normalizePort(process.env.PORT as string, defaultPort);
    server.log.info(`SERVER PORT: ${port}`);

    await server.register(fastifyRateLimit, {
      global: true,
      keyGenerator,
      ...defaultRateLimiting,
    });

    await server.register(metrics);
    await server.register(cookies);

    server.addHook('onRequest', addRequestTimestamp(['/status', '/metrics']));
    server.addHook('onResponse', logResponseDuration);

    server.setNotFoundHandler(notfoundHandler);
    server.setErrorHandler(defaultErrorHandler);

    await server.register(fastifyOpenapiDocs, hidApiOverview);

    await server.register(async (server, _options) => {
      server.addHook('onRequest', checkAccess(authenticationConfig, Role.CLIENT));
      await server.register(HIDForEmail);
      await server.register(HIDsForEmails);
      await server.register(HIDsForEmailUpdates);
      await server.register(EmailForHID);
      await server.register(isEmailRegistered);
      await server.register(generateEmailVerificationToken);
    });

    await server.register(async (server, _options) => {
      server.addHook('onRequest', checkAccess(authenticationConfig, Role.ADMIN));
      await server.register(eraseIdentityForEmail);
      await server.register(getIdentity);
      await server.register(getBlapiProfile);
      await server.register(updateBalpiEmail);
      await server.register(getBlapiBookings);
      await server.register(getBlapiBooking);
      await server.register(getBlapiBookingCyhh);
      await server.register(searchIdentity);
    });

    await server.register(async (server, _options) => {
      server.addHook('onRequest', checkAccess(authenticationConfig, Role.USER_MANAGEMENT));
      await server.register(updatePassword);
      await server.register(updateProfile);
      await server.register(updateLiteProfile);
      await server.register(updateLitePassword);
      await server.register(linkAccount);
      await server.register(unlinkAccount);
    });

    await server.register(status);
    await server.listen({
      port: port,
      host: '0.0.0.0',
    });
  } catch (err) {
    server.log.error(err, 'Unexpected error starting server');
    process.exit(1);
  }
};

const normalizePort = (value: string, fallback: number): number => {
  const port = parseInt(value, 10);

  if (isNaN(port)) return fallback;
  if (port >= 0) return port;

  return fallback;
};

const keyGenerator = (request: FastifyRequest) =>
  (request.headers['x-app-id'] as string) || request.ip; // fallback to default

export type RateLimit = {
  max: number;
  timeWindow: string;
};
