import { Profile } from 'haven-hid-domain';

export interface ExtendedProfile extends Profile {
  alternatePhoneNumber: string;
}
export const generatePartialProfile = (updateValues: {
  name: string | undefined;
  title: string | undefined;
  firstName: string | undefined;
  lastName: string | undefined;
  phoneNumber: string | undefined;
  alternatePhoneNumber: string | undefined;
}): Partial<ExtendedProfile> | undefined => {
  const changes = (Object.entries(updateValues) as [keyof ExtendedProfile, string][]).reduce(
    (initialProfileData: Partial<ExtendedProfile>, [key, value]) =>
      value === undefined
        ? initialProfileData
        : {
            ...initialProfileData,
            [key]: value,
          },
    {},
  );

  if (Object.keys(changes).length === 0) {
    return undefined;
  }
  return changes;
};
