import { HavenIdentity } from 'haven-hid-domain';
import { ExtendedProfile } from './generate-partial-update.js';
import { BLAPIAccountAddress, CountryCodes } from 'haven-identity-blapi-client';

export type Address = {
  line1: string;
  line2?: string;
  city: string;
  county?: string;
  postcode: string;
  countryCode: CountryCodes;
};

export type ExtendedHavenIdentity = HavenIdentity & { alternatePhoneNumber?: string };

export const getChangedProfileFields = (
  profile: Partial<ExtendedProfile>,
  migratedUser: ExtendedHavenIdentity,
) => {
  const changedFields = Object.keys(profile)
    .filter((key) => {
      const newValue = profile[key as keyof ExtendedProfile];
      const existingValue = migratedUser[key as keyof HavenIdentity];

      if (!newValue && !existingValue) {
        return false;
      }

      return newValue !== existingValue;
    })
    .map((field) => {
      if (['title', 'firstName', 'lastName', 'name'].includes(field)) {
        return 'name';
      }

      if (['phoneNumber', 'alternatePhoneNumber'].includes(field)) {
        return 'contact number';
      }

      return null;
    })
    .filter((field) => field !== null) as string[];

  return [...new Set(changedFields)];
};

export const checkAddressIsChanged = (
  address: Address | undefined,
  currentBlapiAddress: BLAPIAccountAddress | undefined,
) => {
  if (!address || !currentBlapiAddress) return false;

  return (
    checkAddressPropertyIsChanged(address.line1, currentBlapiAddress?.line1) ||
    checkAddressPropertyIsChanged(address.line2, currentBlapiAddress?.line2) ||
    checkAddressPropertyIsChanged(address.county, currentBlapiAddress?.line4) ||
    checkAddressPropertyIsChanged(address.city, currentBlapiAddress?.city) ||
    checkAddressPropertyIsChanged(address.postcode, currentBlapiAddress?.zip) ||
    checkAddressPropertyIsChanged(address.countryCode, currentBlapiAddress?.country)
  );
};

export const checkAddressPropertyIsChanged = (
  newValue: string | undefined,
  existingValue: string | undefined,
) => {
  const trimmedNewValue = newValue?.toLowerCase()?.trim();
  const trimmedExistingValue = existingValue?.toLowerCase()?.trim();
  if (!trimmedNewValue && !trimmedExistingValue) return false;

  return trimmedNewValue !== trimmedExistingValue;
};
