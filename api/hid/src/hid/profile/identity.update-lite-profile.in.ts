import {
  FastifyPluginAsyncJsonSchemaToTs,
  JsonSchemaToTsProvider,
} from '@fastify/type-provider-json-schema-to-ts';
import { FromSchema } from 'json-schema-to-ts';

import {
  AccessErrorResponseSchema,
  ErrorResponseSchema,
  registerSchemas,
  UserManagementHeadersSchema,
} from '../schemas.js';
import {
  HavenIdentity,
  HID,
  sendEmailChangedEmail,
  sendProfileChangedEmail,
  Transaction,
  tx,
  UPDATE_PROFILE_SOURCE_SUFFIX,
  ValidEmail,
} from 'haven-hid-domain';
import { HID_VALIDATION_PATTERN, validateEmail, validatePhoneNumber } from '../validation.js';
import { getBearerToken, validateBearerToken } from '../../access/validate-bearer-token.js';
import { validateHid } from '../../access/check-access.js';
import { X_APP_ID_HEADER } from '../header.js';
import { logger } from '@havenengineering/module-haven-logging';
import { EmailExistsError, UnknownIdentityError } from '../../error.js';
import { ExtendedProfile, generatePartialProfile } from './generate-partial-update.js';
import { getDependencies } from '../../dependencies/dependencies.js';
import { getChangedProfileFields } from './helpers.js';

export const URI = '/identity/lite/:hid';

const RequestParamsSchema = {
  $id: 'updateLiteProfileParams',
  type: 'object',
  properties: {
    hid: {
      type: 'string',
      pattern: HID_VALIDATION_PATTERN,
    },
  },
  required: ['hid'],
} as const;

const RequestBodySchema = {
  type: 'object',
  $id: 'updateLiteProfile',
  description: `Updates a lite users profile`,
  properties: {
    email: { type: 'string' },
    title: {
      type: 'string',
      enum: ['Rev', 'Sir', 'Miss', 'Ms', 'Mrs', 'Mr', 'Dr', 'Mx'],
    },
    name: {
      type: 'string',
      maxLength: 100,
      minLength: 1,
    },
    firstName: {
      type: 'string',
      maxLength: 100,
      minLength: 1,
    },
    lastName: {
      type: 'string',
      maxLength: 100,
      minLength: 1,
    },
    phoneNumber: {
      type: 'string',
      maxLength: 30,
      minLength: 0,
    },
  },
  additionalProperties: false,
} as const;

const SuccessResponseSchema = {
  type: 'object',
  $id: 'updateLiteProfileSuccess',
  description: 'Confirm profile has been updated',
  properties: {
    hid: {
      type: 'string',
      pattern: HID_VALIDATION_PATTERN,
    },
    message: { type: 'string' },
  },
  required: ['hid'],
  additionalProperties: false,
  example: {
    hid: 'A1B2C2DDDDDD',
  },
} as const;

const updateLiteProfileConfig = {
  schema: {
    params: RequestParamsSchema,
    body: RequestBodySchema,
    response: {
      200: SuccessResponseSchema,
      400: ErrorResponseSchema,
      403: AccessErrorResponseSchema,
    },
    headers: UserManagementHeadersSchema,
  },
  config: {
    openapi: {
      description: `Updates lite profile associated with Haven Identity. Note: Requires the X-APP-KEY to have user management permissions.`,
      summary: 'User management api to update lite user profile',
      tags: ['Identity'],
      security: [
        {
          appKeyId: [],
          appKeyAuth: [],
          bearerAuth: [],
        },
      ],
    },
    rateLimit: {},
    method: 'PATCH',
    url: URI,
  },
} as const;

const schemas = [
  RequestParamsSchema,
  RequestBodySchema,
  SuccessResponseSchema,
  AccessErrorResponseSchema,
];

export const updateLiteProfile: FastifyPluginAsyncJsonSchemaToTs = (fastify) => {
  const server = fastify.withTypeProvider<JsonSchemaToTsProvider>();
  const { identityManagement, plot, blapi, keysProvider } = getDependencies();

  registerSchemas(schemas, fastify);

  const checkEmailAvailable = async (newEmail: ValidEmail, identity: HavenIdentity) => {
    const identityFromEmail = await identityManagement.findByEmail(newEmail);
    if (identityFromEmail && identity.hid !== identityFromEmail.hid) {
      throw new EmailExistsError('email is already registered with other identity');
    }

    const owner = await plot.fetchProfile(newEmail.email);
    if (owner) {
      throw new EmailExistsError('email is already registered with owners');
    }

    const guest = await blapi.fetchProfile(newEmail.email);
    if (guest) {
      throw new EmailExistsError('email is already registered with seaware');
    }
  };

  const updateLiteUser = async (
    validatedEmail: ValidEmail | undefined,
    hid: HID,
    profile: Partial<ExtendedProfile> | undefined,
    transaction: Transaction,
  ) => {
    const existingIdentity = await identityManagement.findById(hid);
    if (!existingIdentity) {
      logger.error('Identity does not exist', { hid });
      throw new UnknownIdentityError('No such HID');
    }

    if (existingIdentity.accounts.length > 0) {
      logger.error('Identity is not Lite', { hid });
      throw new UnknownIdentityError('No such Lite HID');
    }

    if (validatedEmail && !validatedEmail.equals(existingIdentity.email)) {
      await checkEmailAvailable(validatedEmail, existingIdentity);
      await identityManagement.updateEmail(hid, validatedEmail, transaction);

      await sendEmailChangedEmail({
        email: validatedEmail.email,
        templateId: process.env.EMAIL_CHANGED_TEMPLATE_ID || '',
        name: `${profile?.firstName || existingIdentity.firstName} ${profile?.lastName || existingIdentity.lastName}`,
        oldEmail: existingIdentity.email.email,
      });
    }

    const changedFields = profile ? getChangedProfileFields(profile, existingIdentity) : [];
    const profileIsChanged = changedFields.length > 0;

    if (profile && profileIsChanged) {
      await identityManagement.updateProfile(hid, profile, transaction);
      if (profileIsChanged) {
        await sendProfileChangedEmail({
          email: validatedEmail?.email || existingIdentity.email.email,
          templateId: process.env.PROFILE_CHANGED_TEMPLATE_ID || '',
          name: `${profile.firstName || existingIdentity.firstName} ${profile.lastName || existingIdentity.lastName}`,
          changedFields: changedFields,
        });
      }
    }
  };

  server.patch<{
    Params: FromSchema<typeof RequestParamsSchema>;
    Body: FromSchema<typeof RequestBodySchema>;
    attachValidation: true;
  }>(URI, updateLiteProfileConfig, async (req, res): Promise<void> => {
    const {
      headers: { [X_APP_ID_HEADER]: source, authorization },
      params: { hid },
      body: { email, title, name, firstName, lastName, phoneNumber },
    } = req;

    try {
      const jwks = keysProvider.jwks();
      const bearerToken = getBearerToken(authorization);
      const jwt = await validateBearerToken(jwks, bearerToken);
      const transaction = tx(`${source as string}:${UPDATE_PROFILE_SOURCE_SUFFIX}`);
      const validatedEmail = email ? validateEmail(email) : undefined;

      const profile = generatePartialProfile({
        title,
        name,
        firstName,
        lastName,
        phoneNumber,
        alternatePhoneNumber: undefined,
      });

      profile?.phoneNumber && validatePhoneNumber(profile.phoneNumber);

      validateHid(hid, jwt);

      await updateLiteUser(validatedEmail, hid, profile, transaction);

      return await res.send({ hid });
    } catch (error) {
      logger.error(`Error updating profile for lite HID ${hid}`, error);
      throw error;
    }
  });

  return Promise.resolve();
};
