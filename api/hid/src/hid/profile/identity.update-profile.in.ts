import {
  FastifyPluginAsyncJsonSchemaToTs,
  JsonSchemaToTsProvider,
} from '@fastify/type-provider-json-schema-to-ts';
import { FromSchema } from 'json-schema-to-ts';

import {
  AccessErrorResponseSchema,
  ErrorResponseSchema,
  registerSchemas,
  UserManagementHeadersSchema,
} from '../schemas.js';
import {
  HavenIdentity,
  HavenServiceAccount,
  HavenServiceAccountIdType,
  HavenServiceAccountIdType as IdType,
  HID,
  Profile,
  Transaction,
  tx,
  UPDATE_PROFILE_SOURCE_SUFFIX,
  ValidEmail,
  sendEmailChangedEmail,
  sendProfileChangedEmail,
} from 'haven-hid-domain';
import {
  HID_VALIDATION_PATTERN,
  validateEmail,
  validateId,
  validatePhoneNumber,
} from '../validation.js';
import {
  checkOwnerNotRegisteredWithPlot,
  checkBlockedEmailDomains,
} from '../../identity/owners.js';
import { checkGuestIsNotRegisteredWithSeaware } from '../../identity/seaware.js';
import {
  getBearerToken,
  IdentityTokenPayload,
  OnBehalfOfToken,
  validateBearerToken,
} from '../../access/validate-bearer-token.js';
import { validateAccount, validateHid } from '../../access/check-access.js';
import { X_APP_ID_HEADER } from '../header.js';
import { logger } from '@havenengineering/module-haven-logging';
import { EmailExistsError, UnknownIdentityError, ValidationError } from '../../error.js';
import { ExtendedProfile, generatePartialProfile } from './generate-partial-update.js';
import { getDependencies } from '../../dependencies/dependencies.js';
import { Address, checkAddressIsChanged, getChangedProfileFields } from './helpers.js';
import { formatToIdentityAddress } from '../address/helpers.js';
import { createFeatureFlagChecker, FeatureFlag } from '../../services/unleash.js';

export const URI = '/identity/:hid';

const RequestParamsSchema = {
  $id: 'updateProfileParams',
  type: 'object',
  properties: {
    hid: {
      type: 'string',
      pattern: HID_VALIDATION_PATTERN,
    },
  },
  required: ['hid'],
} as const;

const RequestBodySchema = {
  type: 'object',
  $id: 'updateProfile',
  description: `Updates a users profile for on behalf of owners or my-account.  
  This will proxy to plot or seaware unless the id-token indicates 
  the user has been migrated to haven identity in which case it will update haven identity database 
  as well as all upstream systems.
  
  Note: currently address and alternatePhoneNumber are only used to update upstream Seaware systems
  `,
  properties: {
    id: { type: 'number' },
    idType: {
      type: 'string',
      enum: Object.values(IdType),
    },
    email: { type: 'string' },
    title: {
      type: 'string',
      enum: ['Rev', 'Sir', 'Miss', 'Ms', 'Mrs', 'Mr', 'Dr', 'Mx'],
    },
    name: {
      type: 'string',
      maxLength: 100,
      minLength: 1,
    },
    firstName: {
      type: 'string',
      maxLength: 100,
      minLength: 1,
    },
    lastName: {
      type: 'string',
      maxLength: 100,
      minLength: 1,
    },
    phoneNumber: {
      type: 'string',
      maxLength: 30,
      minLength: 1,
    },
    alternatePhoneNumber: {
      type: 'string',
      maxLength: 30,
      minLength: 0,
    },
    address: {
      type: 'object',
      properties: {
        line1: {
          type: 'string',
          maxLength: 100,
        },
        line2: {
          type: 'string',
          maxLength: 100,
        },
        city: {
          type: 'string',
          maxLength: 50,
        },
        county: {
          type: 'string',
          maxLength: 100,
        },
        postcode: {
          type: 'string',
          maxLength: 10,
        },
        countryCode: {
          type: 'string',
          enum: ['BE', 'IE', 'DE', 'FR', 'GB', 'OT'],
          minLength: 2,
          maxLength: 2,
        },
      },
      required: ['line1', 'city', 'postcode', 'countryCode'],
    },
  },
  additionalProperties: false,
} as const;

const SuccessResponseSchema = {
  type: 'object',
  $id: 'updateProfileSuccess',
  description: 'Confirm profile has been updated',
  properties: {
    hid: {
      type: 'string',
      pattern: HID_VALIDATION_PATTERN,
    },
    message: { type: 'string' },
  },
  required: ['hid'],
  additionalProperties: false,
  example: {
    hid: 'A1B2C2DDDDDD',
  },
} as const;

interface SeawareUpdateParams extends Partial<Profile> {
  email?: ValidEmail;
  address?: Address;
}

const updateProfileConfig = {
  schema: {
    params: RequestParamsSchema,
    body: RequestBodySchema,
    response: {
      200: SuccessResponseSchema,
      400: ErrorResponseSchema,
      403: AccessErrorResponseSchema,
    },
    headers: UserManagementHeadersSchema,
  },
  config: {
    openapi: {
      description: `Updates profile associated with Haven Identity and Plot/Seaware. Note: Requires the X-APP-KEY to have user management permissions.`,
      summary: 'User management api to update profile',
      tags: ['Identity'],
      security: [
        {
          appKeyId: [],
          appKeyAuth: [],
          bearerAuth: [],
        },
      ],
    },
    rateLimit: {},
    method: 'PATCH',
    url: URI,
  },
} as const;

const schemas = [
  RequestParamsSchema,
  RequestBodySchema,
  SuccessResponseSchema,
  AccessErrorResponseSchema,
];

export const updateProfile: FastifyPluginAsyncJsonSchemaToTs = (fastify) => {
  const server = fastify.withTypeProvider<JsonSchemaToTsProvider>();
  const { identityManagement, plot, blapi, blapiAccount, store, keysProvider } = getDependencies();

  registerSchemas(schemas, fastify);

  const checkEmailAvailableInIdentity = async (newEmail: ValidEmail, hid: HID) => {
    const identityFromEmail = await identityManagement.findByEmail(newEmail);
    if (identityFromEmail && hid !== identityFromEmail.hid)
      throw new EmailExistsError('email is already registered with other identity');
  };

  const checkEmailAvailable = async (newEmail: ValidEmail, identity: HavenIdentity) => {
    await checkEmailAvailableInIdentity(newEmail, identity.hid);

    const owner = await plot.fetchProfile(newEmail.email);
    if (owner && !isProfileOwnedByIdentity(owner.ownerId, identity.accounts, IdType.OWNER_ID))
      throw new EmailExistsError('email is already registered with owners');

    const guest = await blapi.fetchProfile(newEmail.email);
    if (
      guest &&
      !isProfileOwnedByIdentity(guest.clientId, identity.accounts, IdType.SEAWARE_CLIENT_ID)
    )
      throw new EmailExistsError('email is already registered with seaware');
  };

  const isProfileOwnedByIdentity = (
    profileId: number,
    accounts: HavenIdentity['accounts'],
    idType: IdType,
  ): boolean => accounts.some(({ type, id }) => type === idType && id === profileId);

  const checkEmailAvailableInPlot = async (validEmail: ValidEmail, id: number, idType: IdType) => {
    const owner = await plot.fetchProfile(validEmail.email);
    checkOwnerNotRegisteredWithPlot(owner, id, idType);
  };

  const checkEmailAvailableInSeaware = async (
    validEmail: ValidEmail,
    id: number,
    idType: IdType,
  ) => {
    const guest = await blapi.fetchProfile(validEmail.email);
    checkGuestIsNotRegisteredWithSeaware(guest, id, idType);
  };

  const updateRelatedAccounts = async (
    accounts: HavenServiceAccount[],
    email: string | undefined,
    address: Address | undefined,
    profile: Partial<ExtendedProfile> | undefined,
    hid: HID,
    bearerToken: string,
  ) => {
    await Promise.all(
      accounts.map(async (account) => {
        const { type, id } = account;

        switch (type) {
          case HavenServiceAccountIdType.OWNER_ID:
            if (!email) return false;
            await plot.updateProfile(id, {
              email,
            });
            break;
          case HavenServiceAccountIdType.SEAWARE_CLIENT_ID:
            if (!email && !address && !profile) return false;

            const truncatedProfile = truncateProfileNameFields(profile);

            await blapi.updateProfile(bearerToken, {
              ...(email && { email }),
              ...(address && { address }),
              ...truncatedProfile,
            });

            break;
        }

        logger.info(`Updated profile for ${hid} ${type} ${id}`);

        return true;
      }),
    );
  };

  const truncateProfileNameFields = (
    profile: Partial<ExtendedProfile> | undefined,
  ): Partial<ExtendedProfile> => {
    if (!profile) return {};

    const { name, firstName, lastName } = profile;

    return {
      ...profile,
      ...(name && { name: truncate(name) }),
      ...(firstName && {
        firstName: truncate(firstName),
      }),
      ...(lastName && {
        lastName: truncate(lastName),
      }),
    };
  };

  const truncate = (value: string, length = 30): string => value.slice(0, length);

  const updateSeawareForUnmigrated = async (
    hid: HID,
    bearerToken: string,
    id: number,
    idType: IdType,
    transaction: Transaction,
    payload: SeawareUpdateParams,
  ) => {
    const { email, ...profile } = payload;

    if (!email) {
      await blapi.updateProfile(bearerToken, profile);
      return hid;
    }
    await blapi.updateProfile(bearerToken, {
      ...profile,
      email: email.email,
    });
    const hidToReturn = await store.getHIDForEmailAndSeaWare(email, undefined, id, transaction);

    return hidToReturn;
  };

  const updatePlotForUnmigrated = async (
    hid: HID,
    id: number,
    idType: IdType,
    transaction: Transaction,
    validatedEmail: ValidEmail | undefined,
  ) => {
    if (!validatedEmail) return hid;

    await plot.updateProfile(id, {
      email: validatedEmail?.email,
    });

    const hidToReturn = await store.getHIDForEmailAndPlot(
      validatedEmail,
      undefined,
      id,
      transaction,
    );

    return hidToReturn;
  };

  const isMigratedUser = (jwt: IdentityTokenPayload | OnBehalfOfToken): boolean => {
    if (jwt.onBehalfOf) {
      return (jwt as OnBehalfOfToken).onBehalfOf.identity.migrated;
    }
    return jwt.sub === jwt.hid;
  };

  const updateMigratedUser = async (
    validatedEmail: ValidEmail | undefined,
    hid: HID,
    email: string | undefined,
    address: Address | undefined,
    profile: Partial<ExtendedProfile> | undefined,
    bearerToken: string,
    transaction: Transaction,
    accounts: HavenServiceAccount[] | undefined,
    isIdentityAddAddressEnabled: boolean | undefined,
  ) => {
    const migratedUser = await identityManagement.findById(hid);
    if (!migratedUser) {
      logger.error('Identity does not exist', { hid });
      throw new UnknownIdentityError('No such HID');
    }

    const accountsToUpdate = accounts || migratedUser.accounts;

    if (validatedEmail && !validatedEmail.equals(migratedUser.email)) {
      if (hasOwnerAccount(accountsToUpdate)) checkBlockedEmailDomains(validatedEmail.email);

      await checkEmailAvailable(validatedEmail, migratedUser);
    }

    if (validatedEmail) {
      const missingAccounts = getMissingAccounts(migratedUser, accountsToUpdate);

      await Promise.all(
        missingAccounts.map((account) => {
          if (account.type === HavenServiceAccountIdType.OWNER_ID) {
            return store.getHIDForEmailAndPlot(
              migratedUser.email,
              undefined,
              account.id,
              transaction,
            );
          }
          if (account.type === HavenServiceAccountIdType.SEAWARE_CLIENT_ID) {
            return store.getHIDForEmailAndSeaWare(
              migratedUser.email,
              undefined,
              account.id,
              transaction,
            );
          }
        }),
      );
    }

    const seawareClientId = accountsToUpdate.find(
      (account) => account.type === IdType.SEAWARE_CLIENT_ID,
    )?.id;
    const currentBlapiProfile = seawareClientId
      ? await blapiAccount.fetchProfileById(seawareClientId)
      : undefined;

    const currentBlapiAddress = currentBlapiProfile?.address;
    const currentBlapiAlternatePhoneNumber = currentBlapiProfile?.alternatePhoneNumber || '';

    await updateRelatedAccounts(accountsToUpdate, email, address, profile, hid, bearerToken);

    if (validatedEmail && !validatedEmail.equals(migratedUser.email)) {
      await identityManagement.updateEmail(hid, validatedEmail, transaction);
      await sendEmailChangedEmail({
        email: validatedEmail.email,
        templateId: process.env.EMAIL_CHANGED_TEMPLATE_ID || '',
        name: `${profile?.firstName || migratedUser.firstName} ${profile?.lastName || migratedUser.lastName}`,
        oldEmail: migratedUser.email.email,
      });
    }

    const changedProfiledFields = profile
      ? getChangedProfileFields(profile, {
          ...migratedUser,
          alternatePhoneNumber: currentBlapiAlternatePhoneNumber,
        })
      : [];

    const profileIsChanged = changedProfiledFields.length > 0;

    if (profile && profileIsChanged) {
      await identityManagement.updateProfile(hid, profile, transaction);
    }

    if (isIdentityAddAddressEnabled) {
      const identityAddress = formatToIdentityAddress(address);
      identityAddress && (await identityManagement.addAddress(hid, identityAddress, transaction));
    }

    const addressIsChanged = checkAddressIsChanged(address, currentBlapiAddress);

    if (profileIsChanged || addressIsChanged) {
      const changedFields = addressIsChanged
        ? [...changedProfiledFields, 'address']
        : changedProfiledFields;

      await sendProfileChangedEmail({
        email: validatedEmail?.email || migratedUser.email.email,
        templateId: process.env.PROFILE_CHANGED_TEMPLATE_ID || '',
        name: `${profile?.firstName || migratedUser.firstName} ${profile?.lastName || migratedUser.lastName}`,
        changedFields: changedFields,
      });
    }
  };

  const hasOwnerAccount = (accounts: HavenServiceAccount[]): boolean =>
    accounts.some(({ type }) => type === HavenServiceAccountIdType.OWNER_ID);

  const getMissingAccounts = (
    migratedUser: HavenIdentity,
    processAccounts: HavenServiceAccount[],
  ): HavenServiceAccount[] => {
    const missingAccounts = processAccounts.filter(
      (a) => !migratedUser.accounts.some((m) => m.id === a.id && m.type === a.type),
    );
    return missingAccounts;
  };

  const updateUnmigratedUser = async (
    idType: IdType,
    hid: HID,
    id: number,
    validatedEmail: ValidEmail | undefined,
    address: Address | undefined,
    profile: Partial<Profile> | undefined,
    bearerToken: string,
    transaction: Transaction,
    accounts: HavenServiceAccount[],
  ) => {
    let hidToReturn: HID;

    if (validatedEmail) {
      await checkEmailAvailableInIdentity(validatedEmail, hid);

      // Perform the validation of all account types here so that we don't update one but not another
      await Promise.all(
        accounts.map((account) => {
          if (account.type === HavenServiceAccountIdType.OWNER_ID) {
            checkBlockedEmailDomains(validatedEmail.email);
            return checkEmailAvailableInPlot(validatedEmail, account.id, account.type);
          }
          if (account.type === HavenServiceAccountIdType.SEAWARE_CLIENT_ID) {
            return checkEmailAvailableInSeaware(validatedEmail, account.id, account.type);
          }
        }),
      );
    }

    switch (idType) {
      case HavenServiceAccountIdType.OWNER_ID:
        hidToReturn = await updatePlotForUnmigrated(hid, id, idType, transaction, validatedEmail);
        break;
      case HavenServiceAccountIdType.SEAWARE_CLIENT_ID:
        hidToReturn = await updateSeawareForUnmigrated(hid, bearerToken, id, idType, transaction, {
          ...(validatedEmail && {
            email: validatedEmail,
          }),
          ...(address && { address }),
          ...(profile && profile),
        });
        break;
    }

    return hidToReturn;
  };

  server.patch<{
    Params: FromSchema<typeof RequestParamsSchema>;
    Body: FromSchema<typeof RequestBodySchema>;
    attachValidation: true;
  }>(URI, updateProfileConfig, async (req, res): Promise<void> => {
    const {
      headers: { [X_APP_ID_HEADER]: source, authorization },
      params: { hid },
      body: {
        id,
        idType,
        email,
        title,
        name,
        firstName,
        lastName,
        address,
        phoneNumber,
        alternatePhoneNumber,
      },
    } = req;

    try {
      const featureFlagChecker = await createFeatureFlagChecker();

      const isIdentityAddAddressEnabled = featureFlagChecker?.isEnabled(
        FeatureFlag.IDENTITY_MASTERING_ADDRESS,
        req,
      );

      const jwks = keysProvider.jwks();

      const bearerToken = getBearerToken(authorization);
      const jwt = await validateBearerToken(jwks, bearerToken);
      const sourceSuffix = jwt.onBehalfOf ? `${jwt.sub}` : UPDATE_PROFILE_SOURCE_SUFFIX;
      const transaction = tx(`${source as string}:${sourceSuffix}`);

      const validatedEmail = email ? validateEmail(email) : undefined;

      const profile = generatePartialProfile({
        title,
        name,
        firstName,
        lastName,
        phoneNumber,
        alternatePhoneNumber,
      });

      profile?.phoneNumber && validatePhoneNumber(profile.phoneNumber);
      profile?.alternatePhoneNumber && validatePhoneNumber(profile.alternatePhoneNumber);

      validateId(idType, id);
      validateHid(hid, jwt);
      validateAccount(id, idType, jwt);

      if (!isMigratedUser(jwt)) {
        if (!idType || !id) {
          throw new ValidationError('Unmigrated user requires an id and idType');
        }
        const hidToReturn = await updateUnmigratedUser(
          idType,
          hid,
          id,
          validatedEmail,
          address,
          profile,
          bearerToken,
          transaction,
          jwt.accounts,
        );

        req.log.info({}, `Updated profile for ${idType} ${id}`);

        return await res.send({
          hid: hidToReturn,
        });
      }

      const adminJwtAccounts = jwt.onBehalfOf ? jwt.accounts : undefined;
      await updateMigratedUser(
        validatedEmail,
        hid,
        email,
        address,
        profile,
        bearerToken,
        transaction,
        adminJwtAccounts,
        isIdentityAddAddressEnabled,
      );

      return await res.send({ hid });
    } catch (error) {
      logger.error(`Error updating profile for ${idType} ${id} HID ${hid}`, error);
      throw error;
    }
  });

  return Promise.resolve();
};
