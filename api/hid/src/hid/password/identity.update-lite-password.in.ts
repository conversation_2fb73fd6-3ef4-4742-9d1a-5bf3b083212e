import {
  FastifyPluginAsyncJsonSchemaToTs,
  JsonSchemaToTsProvider,
} from '@fastify/type-provider-json-schema-to-ts';
import { FromSchema } from 'json-schema-to-ts';

import {
  AccessErrorResponseSchema,
  registerSchemas,
  UserManagementHeadersSchema,
} from '../schemas.js';
import { sendPasswordChangedEmail, tx, UPDATE_PASSWORD_SOURCE_SUFFIX } from 'haven-hid-domain';
import { PasswordStrength } from '@havenengineering/module-shared-password-strength';
import { HID_VALIDATION_PATTERN, validatePassword } from '../validation.js';
import { getBearerToken, validateBearerToken } from '../../access/validate-bearer-token.js';
import { validateHid } from '../../access/check-access.js';
import { X_APP_ID_HEADER } from '../header.js';
import { logger } from '@havenengineering/module-haven-logging';
import { UnknownIdentityError, UpdatePasswordAuthorizationError } from '../../error.js';
import { bcryptCompare, bcryptHash } from 'haven-identity-password-util';
import { BlapiClientError } from 'haven-identity-blapi-client';
import { getDependencies } from '../../dependencies/dependencies.js';

export const URI = '/identity/lite/:hid/password';

const RequestParamsSchema = {
  $id: 'updateLitePasswordParams',
  type: 'object',
  properties: {
    hid: {
      type: 'string',
      pattern: HID_VALIDATION_PATTERN,
    },
  },
  required: ['hid'],
} as const;

const RequestBodySchema = {
  type: 'object',
  $id: 'updateLitePassword',
  description: `Updates a lite users password`,
  properties: {
    password: { type: 'string' },
    currentPassword: { type: 'string' },
  },
  required: ['password'],
  additionalProperties: false,
} as const;

const SuccessResponseSchema = {
  type: 'object',
  $id: 'updateLitePasswordSuccess',
  description: 'Confirm password has been updated',
  properties: {
    hid: {
      type: 'string',
      pattern: HID_VALIDATION_PATTERN,
    },
    message: { type: 'string' },
  },
  required: ['hid'],
  additionalProperties: false,
  example: {
    hid: 'A1B2C2DDDDDD',
  },
} as const;

export const ErrorResponseSchema = {
  type: 'object',
  $id: 'GenericError',
  description: 'Some error description',
  properties: {
    code: { type: 'string' },
    message: { type: 'string' },
    statusCode: { type: 'number' },
    strength: {
      type: 'string',
      enum: Object.values(PasswordStrength),
    },
    warning: { type: 'string' },
    suggestions: { type: 'array' },
  },
  example: {
    code: 'ERROR_CODE',
    message: 'Something wrong with your request',
    statusCode: 400,
    score: 'week',
    warning: 'warning',
    suggestions: ['suggestion1', 'suggestion2'],
  },
} as const;

const updateLitePasswordConfig = {
  schema: {
    params: RequestParamsSchema,
    body: RequestBodySchema,
    response: {
      200: SuccessResponseSchema,
      400: ErrorResponseSchema,
      403: AccessErrorResponseSchema,
    },
    headers: UserManagementHeadersSchema,
  },
  config: {
    openapi: {
      description: `Updates password associated with Haven Identity. Note: Requires the X-APP-KEY to have user management permissions.`,
      summary: 'User management api to update lite user password',
      tags: ['Identity'],
      security: [
        {
          appKeyId: [],
          appKeyAuth: [],
          bearerAuth: [],
        },
      ],
    },
    rateLimit: {},
    method: 'PUT',
    url: URI,
  },
} as const;

const schemas = [
  RequestParamsSchema,
  RequestBodySchema,
  SuccessResponseSchema,
  AccessErrorResponseSchema,
];

export const updateLitePassword: FastifyPluginAsyncJsonSchemaToTs = (fastify) => {
  const server = fastify.withTypeProvider<JsonSchemaToTsProvider>();
  const { identityManagement, keysProvider } = getDependencies();

  registerSchemas(schemas, fastify);

  server.put<{
    Params: FromSchema<typeof RequestParamsSchema>;
    Body: FromSchema<typeof RequestBodySchema>;
    attachValidation: true;
  }>(URI, updateLitePasswordConfig, async (req, res): Promise<void> => {
    const {
      params: { hid },
      body: { password, currentPassword },
    } = req;

    try {
      const jwks = keysProvider.jwks();
      const source = req.headers[X_APP_ID_HEADER] as string;
      const transaction = tx(`${source}:${UPDATE_PASSWORD_SOURCE_SUFFIX}`);

      const bearerToken = getBearerToken(req.headers.authorization);
      const jwt = await validateBearerToken(jwks, bearerToken);

      validateHid(hid, jwt);
      const strength = validatePassword(password);
      const existingIdentity = await identityManagement.findById(hid);

      if (!existingIdentity) {
        logger.error('Identity does not exist', { hid });
        throw new UnknownIdentityError('No such HID');
      }

      if (existingIdentity.accounts.length > 0) {
        logger.error('Identity is not Lite', { hid });
        throw new UnknownIdentityError('No such Lite HID');
      }

      if (currentPassword !== undefined) {
        const check = await bcryptCompare(currentPassword, existingIdentity.passwordHash || '');
        if (!check) {
          throw new UpdatePasswordAuthorizationError('existing password does not match');
        }
      }

      const hash = await bcryptHash(password);
      await identityManagement.updatePassword(
        {
          hid,
          hash,
          strength,
        },
        transaction,
      );
      await sendPasswordChangedEmail({
        email: existingIdentity.email.email,
        templateId: process.env.PASSWORD_CHANGED_TEMPLATE_ID || '',
        name: `${existingIdentity.firstName} ${existingIdentity.lastName}`,
      });
      return await res.send({ hid });
    } catch (e: unknown) {
      if (e instanceof BlapiClientError && e.code === 'BLAPI_PASSWORD_AUTHORIZATION_ERROR') {
        throw new UpdatePasswordAuthorizationError(e.message);
      }
      req.log.error(`Error updating password for ${hid}`, e);
      throw e;
    }
  });

  return Promise.resolve();
};
