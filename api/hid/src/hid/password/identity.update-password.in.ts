import {
  FastifyPluginAsyncJsonSchemaToTs,
  JsonSchemaToTsProvider,
} from '@fastify/type-provider-json-schema-to-ts';
import { FromSchema } from 'json-schema-to-ts';

import {
  AccessErrorResponseSchema,
  registerSchemas,
  UserManagementHeadersSchema,
} from '../schemas.js';
import {
  HavenServiceAccountIdType as IdType,
  sendPasswordChangedEmail,
  tx,
  UPDATE_PASSWORD_SOURCE_SUFFIX,
} from 'haven-hid-domain';
import { PasswordStrength } from '@havenengineering/module-shared-password-strength';
import { HID_VALIDATION_PATTERN, validateId, validatePassword } from '../validation.js';
import { getBearerToken, validateBearerToken } from '../../access/validate-bearer-token.js';
import { validateAccount, validateHid } from '../../access/check-access.js';
import { X_APP_ID_HEADER } from '../header.js';
import { logger } from '@havenengineering/module-haven-logging';
import {
  UnknownIdentityError,
  UpdatePasswordAuthorizationError,
  ValidationError,
} from '../../error.js';
import { bcryptCompare, bcryptHash } from 'haven-identity-password-util';
import { BlapiClientError } from 'haven-identity-blapi-client';
import { getDependencies } from '../../dependencies/dependencies.js';

export const URI = '/identity/:hid/password';

const RequestParamsSchema = {
  $id: 'updatePasswordParams',
  type: 'object',
  properties: {
    hid: {
      type: 'string',
      pattern: HID_VALIDATION_PATTERN,
    },
  },
  required: ['hid'],
} as const;

const RequestBodySchema = {
  type: 'object',
  $id: 'updatePassword',
  description: `Updates a users password for on behalf of owners or my-account.  
    This will proxy to plot or seaware unless the id-token indicates 
    the user has been migrated to haven identity in which case it will update haven identity database 
    as well as all upstream systems`,
  properties: {
    id: { type: 'number' },
    idType: {
      type: 'string',
      enum: Object.values(IdType),
    },
    password: { type: 'string' },
    currentPassword: { type: 'string' },
  },
  required: ['password'],
  additionalProperties: false,
} as const;

const SuccessResponseSchema = {
  type: 'object',
  $id: 'updatePasswordSuccess',
  description: 'Confirm password has been updated',
  properties: {
    hid: {
      type: 'string',
      pattern: HID_VALIDATION_PATTERN,
    },
    message: { type: 'string' },
  },
  required: ['hid'],
  additionalProperties: false,
  example: {
    hid: 'A1B2C2DDDDDD',
  },
} as const;

export const ErrorResponseSchema = {
  type: 'object',
  $id: 'GenericError',
  description: 'Some error description',
  properties: {
    code: { type: 'string' },
    message: { type: 'string' },
    statusCode: { type: 'number' },
    strength: {
      type: 'string',
      enum: Object.values(PasswordStrength),
    },
    warning: { type: 'string' },
    suggestions: { type: 'array' },
  },
  example: {
    code: 'ERROR_CODE',
    message: 'Something wrong with your request',
    statusCode: 400,
    score: 'week',
    warning: 'warning',
    suggestions: ['suggestion1', 'suggestion2'],
  },
} as const;

const updatePasswordConfig = {
  schema: {
    params: RequestParamsSchema,
    body: RequestBodySchema,
    response: {
      200: SuccessResponseSchema,
      400: ErrorResponseSchema,
      403: AccessErrorResponseSchema,
    },
    headers: UserManagementHeadersSchema,
  },
  config: {
    openapi: {
      description: `Updates password associated with Haven Identity and Plot/Seaware. Note: Requires the X-APP-KEY to have user management permissions.`,
      summary: 'User management api to update password',
      tags: ['Identity'],
      security: [
        {
          appKeyId: [],
          appKeyAuth: [],
          bearerAuth: [],
        },
      ],
    },
    rateLimit: {},
    method: 'PUT',
    url: URI,
  },
} as const;

const schemas = [
  RequestParamsSchema,
  RequestBodySchema,
  SuccessResponseSchema,
  AccessErrorResponseSchema,
];

export const updatePassword: FastifyPluginAsyncJsonSchemaToTs = (fastify) => {
  const server = fastify.withTypeProvider<JsonSchemaToTsProvider>();
  const { identityManagement, keysProvider, plot, blapi } = getDependencies();

  registerSchemas(schemas, fastify);

  server.put<{
    Params: FromSchema<typeof RequestParamsSchema>;
    Body: FromSchema<typeof RequestBodySchema>;
    attachValidation: true;
  }>(URI, updatePasswordConfig, async (req, res): Promise<void> => {
    const hid = req.params.hid;
    const idType = req.body.idType as IdType;
    const id = req.body.id;
    const password = req.body.password;
    const currentPassword = req.body.currentPassword;

    try {
      const jwks = keysProvider.jwks();
      const source = req.headers[X_APP_ID_HEADER] as string;
      const transaction = tx(`${source}:${UPDATE_PASSWORD_SOURCE_SUFFIX}`);

      const bearerToken = getBearerToken(req.headers.authorization);
      const jwt = await validateBearerToken(jwks, bearerToken);

      validateId(idType, id);
      validateHid(hid, jwt);
      validateAccount(id, idType, jwt);

      if (jwt.sub === jwt.hid) {
        const strength = validatePassword(password);
        const existingIdentity = await identityManagement.findById(hid);

        if (!existingIdentity) {
          logger.error('Identity does not exist', { hid });
          throw new UnknownIdentityError('No such HID');
        }

        if (currentPassword !== undefined) {
          const check = await bcryptCompare(currentPassword, existingIdentity.passwordHash || '');
          if (!check) {
            throw new UpdatePasswordAuthorizationError('existing password does not match');
          }
        }

        const hash = await bcryptHash(password);
        await identityManagement.updatePassword(
          {
            hid,
            hash,
            strength,
          },
          transaction,
        );
        await sendPasswordChangedEmail({
          email: existingIdentity.email.email,
          templateId: process.env.PASSWORD_CHANGED_TEMPLATE_ID || '',
          name: `${existingIdentity.firstName} ${existingIdentity.lastName}`,
        });
        return await res.send({ hid });
      } else {
        if (!id || !idType) {
          throw new ValidationError('Unmigrated user requires an id and idType');
        }
        switch (idType) {
          case IdType.OWNER_ID:
            const plotValidationError = plot.validatePassword(password);
            if (plotValidationError) {
              throw createPasswordValidationError(plotValidationError);
            }
            await plot.updatePassword(id, password);
            return await res.send({ hid });
          case IdType.SEAWARE_CLIENT_ID:
            if (!currentPassword) {
              throw new ValidationError('body/currentPassword is required');
            }
            const blapiValidationError = blapi.validatePassword(password);
            if (blapiValidationError) {
              throw createPasswordValidationError(blapiValidationError);
            }
            await blapi.updatePassword(bearerToken, {
              currentPassword,
              newPassword: password,
            });
            return await res.send({ hid });
        }
      }
    } catch (e: unknown) {
      if (e instanceof BlapiClientError && e.code === 'BLAPI_PASSWORD_AUTHORIZATION_ERROR') {
        throw new UpdatePasswordAuthorizationError(e.message);
      }
      req.log.error(`Error updating password for ${idType} ${id}`, e);
      throw e;
    }
  });

  return Promise.resolve();
};

const createPasswordValidationError = (error: string) => {
  return new ValidationError('body/password is not valid password', {
    strength: PasswordStrength.UNKNOWN,
    warning: 'Password does not match the requirements',
    suggestions: [error],
  });
};
