import { H<PERSON>Address } from 'haven-hid-domain';
import { Address } from '../profile/helpers.js';

export function formatToIdentityAddress(
  incomingAddress: Address | undefined,
): HIDAddress | undefined {
  if (!incomingAddress) {
    return;
  }

  const transformedAddress = {
    address_line1: incomingAddress.line1,
    address_line2: incomingAddress.line2,
    address_city: incomingAddress.city,
    address_county: incomingAddress.county,
    address_postcode: incomingAddress.postcode,
    address_country: incomingAddress.countryCode,
  };

  return transformedAddress;
}
