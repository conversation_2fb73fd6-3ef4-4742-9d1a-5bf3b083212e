import {
  HavenServiceAccount,
  HavenServiceAccountIdType as IdType,
  HID,
  Transaction,
  ValidEmail,
} from 'haven-hid-domain';
import { getDependencies } from '../../dependencies/dependencies.js';
import { AdminIdentitySearchResult } from './admin.search.in.js';

export const identitySearchViaServices = async (
  email: ValidEmail,
  tx: Transaction,
): Promise<AdminIdentitySearchResult[]> => {
  const { plot, blapi, store } = getDependencies();

  const owner = await plot.fetchProfile(email.email);
  const guest = await blapi.fetchProfile(email.email);

  if (owner || guest) {
    const hid: HID = await store.getHIDForEmail(email, undefined, tx);
    const accounts: HavenServiceAccount[] = [];
    if (guest) {
      accounts.push({
        id: guest.clientId,
        type: IdType.SEAWARE_CLIENT_ID,
      });
    }
    if (owner) {
      accounts.push({
        id: owner.ownerId,
        type: IdType.OWNER_ID,
      });
    }
    return [
      {
        hid: hid,
        title: guest?.title || (owner?.title as string),
        firstName: guest?.firstName || (owner?.firstName as string),
        lastName: guest?.lastName || (owner?.lastName as string),
        email: email.email,
        accounts: accounts,
        migrated: false,
      },
    ];
  }
  return [];
};
