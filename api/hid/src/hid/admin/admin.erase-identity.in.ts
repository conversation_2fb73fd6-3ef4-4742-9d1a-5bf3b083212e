import {
  FastifyPluginAsyncJsonSchemaToTs,
  JsonSchemaToTsProvider,
} from '@fastify/type-provider-json-schema-to-ts';
import { FromSchema } from 'json-schema-to-ts';

import {
  AccessErrorResponseSchema,
  ErrorResponseSchema,
  HeadersSchema,
  registerSchemas,
} from '../schemas.js';
import { FastifyReply, FastifyRequest } from 'fastify';
import { RightToBeForgotten, tx } from 'haven-hid-domain';
import { X_APP_ID_HEADER } from '../header.js';
import { getDependencies } from '../../dependencies/dependencies.js';

export const URI = '/admin/erase-identity';

const RequestSchema = {
  type: 'object',
  $id: 'eraseIdentity',
  description: 'Erases a customers identity related information.',
  properties: {
    email: { type: 'string' },
  },
  required: ['email'],
  additionalProperties: false,
} as const;

const SuccessResponseSchema = {
  type: 'object',
  $id: 'eraseIdentitySuccess',
  description: 'Confirm information erasure confirmation',
  properties: {
    hidInfo: { type: 'string' },
    authenticationAuditInfo: { type: 'string' },
    hidAuditInfo: { type: 'string' },
  },
  required: ['hidInfo', 'authenticationAuditInfo', 'hidAuditInfo'],
  additionalProperties: false,
  example: {
    hidInfo: 'erased',
    authenticationAuditInfo: 'obfuscated',
    hidAuditInfo: 'obfuscated',
  },
} as const;

const eraseIdentityEndpoint = {
  schema: {
    body: RequestSchema,
    response: {
      200: SuccessResponseSchema,
      400: ErrorResponseSchema,
      403: AccessErrorResponseSchema,
    },
    headers: HeadersSchema,
  },
  config: {
    openapi: {
      description: 'Erases customer identity information given an email',
      summary: 'Erase Customer Identity',
      tags: ['Admin'],
      security: [{ appKeyId: [], appKeyAuth: [] }],
    },
    rateLimit: {},
    method: 'POST',
    url: URI,
  },
} as const;

const schemas = [RequestSchema, SuccessResponseSchema, AccessErrorResponseSchema];

export const eraseIdentityForEmail: FastifyPluginAsyncJsonSchemaToTs = (fastify) => {
  const server = fastify.withTypeProvider<JsonSchemaToTsProvider>();
  const { rightToBeForgotten: store } = getDependencies();

  registerSchemas(schemas, fastify);

  server.post<{
    Body: FromSchema<typeof RequestSchema>;
    attachValidation: true;
  }>(URI, eraseIdentityEndpoint, controller(store));

  return Promise.resolve();
};

const controller =
  (store: RightToBeForgotten) =>
  async (req: FastifyRequest, res: FastifyReply): Promise<void> => {
    const source = req.headers[X_APP_ID_HEADER] as string;

    const { email } = req.body as {
      email: string;
    };

    const result = await store.eraseIdentity(email, tx(source));
    await res.send(result);
  };
