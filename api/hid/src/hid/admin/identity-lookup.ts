import {
  HavenServiceAccount,
  HavenServiceAccountIdType,
  HID,
  MigratedHavenIdentity,
  UnmigratedHavenIdentity,
} from 'haven-hid-domain';
import { UnknownIdentityError } from '../../error.js';
import { getDependencies } from '../../dependencies/dependencies.js';

async function findUnmigrated(hid: string) {
  const { hidStore } = getDependencies();

  const unmigrated = await hidStore.findAllForHID(hid);

  if (unmigrated.hid && unmigrated.email) {
    const accounts: HavenServiceAccount[] = [];
    if (unmigrated.seaWareClientId) {
      accounts.push({
        id: unmigrated.seaWareClientId,
        type: HavenServiceAccountIdType.SEAWARE_CLIENT_ID,
      });
    }
    if (unmigrated.plotOwnerId) {
      accounts.push({
        id: unmigrated.plotOwnerId,
        type: HavenServiceAccountIdType.OWNER_ID,
      });
    }
    return {
      hid: hid,
      email: unmigrated.email,
      accounts,
    };
  }

  return undefined;
}

export const identityLookup = async (
  hid: HID,
): Promise<MigratedHavenIdentity | UnmigratedHavenIdentity> => {
  const { identityManagement } = getDependencies();

  const migrated = await identityManagement.findById(hid);
  if (migrated) return { ...migrated, migrated: true };

  const unmigrated = await findUnmigrated(hid);
  if (unmigrated) return { ...unmigrated, migrated: false };

  throw new UnknownIdentityError(`No such HID: ${hid}`);
};
