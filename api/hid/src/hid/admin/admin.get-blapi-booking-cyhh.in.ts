import {
  FastifyPluginAsyncJsonSchemaToTs,
  JsonSchemaToTsProvider,
} from '@fastify/type-provider-json-schema-to-ts';
import { FromSchema } from 'json-schema-to-ts';

import {
  AccessErrorResponseSchema,
  ErrorResponseSchema,
  registerSchemas,
  UserManagementHeadersSchema,
} from '../schemas.js';
import { getDependencies } from '../../dependencies/dependencies.js';
import { HID_VALIDATION_PATTERN } from '../validation.js';
import {
  ADMIN_USER_UPN_HEADER,
  generateBlapiBearerToken,
} from '../../access/admin-blapi-access.js';
import { identityLookup } from './identity-lookup.js';
import { HavenServiceAccountIdType } from 'haven-hid-domain';

export const URI = '/admin/identity/:hid/blapi-booking/:bookingRef/cyhh';

const RequestParamsSchema = {
  $id: 'getBookingCyhhFromBlapi',
  type: 'object',
  properties: {
    hid: {
      type: 'string',
      pattern: HID_VALIDATION_PATTERN,
    },
    bookingRef: { type: 'string' },
  },
  required: ['hid', 'bookingRef'],
} as const;

const SuccessResponseSchema = {
  type: 'object',
  $id: 'getBookingCyhhFromBlapiSuccess',
  description:
    'Return Choose You Holiday Home data for a single Booking from Blapi for a given booking ref',
  properties: {}, // TODO
  additionalProperties: true,
  example: {}, // TODO
} as const;

const GetBlapiBookingCyhhConfig = {
  schema: {
    params: RequestParamsSchema,
    response: {
      200: SuccessResponseSchema,
      400: ErrorResponseSchema,
      403: AccessErrorResponseSchema,
      422: ErrorResponseSchema,
    },
    headers: UserManagementHeadersSchema,
  },
  config: {
    openapi: {
      description: `Fetches Choose Your Holiday Home data for a single booking from Blapi. Note: Requires the X-APP-KEY to have user management permissions.`,
      summary: 'User management api to fetch bookings from Blapi',
      tags: ['Admin'],
      security: [{ appKeyId: [], appKeyAuth: [] }],
    },
    rateLimit: {},
    method: 'GET',
    url: URI,
  },
} as const;

const schemas = [RequestParamsSchema, SuccessResponseSchema, AccessErrorResponseSchema];

export const getBlapiBookingCyhh: FastifyPluginAsyncJsonSchemaToTs = (fastify) => {
  const server = fastify.withTypeProvider<JsonSchemaToTsProvider>();
  const { blapi } = getDependencies();
  registerSchemas(schemas, fastify);

  server.get<{
    Params: FromSchema<typeof RequestParamsSchema>;
    attachValidation: true;
  }>(URI, GetBlapiBookingCyhhConfig, async (req, res): Promise<void> => {
    try {
      const {
        params: { hid, bookingRef },
      } = req;

      const adminUserUpn = req.headers[ADMIN_USER_UPN_HEADER] as string;
      const identity = await identityLookup(hid);
      const seawareAccount = identity.accounts.find(
        ({ type }) => type === HavenServiceAccountIdType.SEAWARE_CLIENT_ID,
      );

      if (!seawareAccount) {
        return await res.send({});
      }

      const blapiBearerToken = await generateBlapiBearerToken(
        {
          hid,
          accounts: identity.accounts,
          migrated: identity.migrated,
        },
        adminUserUpn,
      );
      const cyhh = await blapi.getBookingCyhh(blapiBearerToken, bookingRef);
      return await res.send(cyhh);
    } catch (e) {
      req.log.error(`Error getting cyhh data from Blapi`, e);
      throw e;
    }
  });

  return Promise.resolve();
};
