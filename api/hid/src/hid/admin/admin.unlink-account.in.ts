import {
  FastifyPluginAsyncJsonSchemaToTs,
  JsonSchemaToTsProvider,
} from '@fastify/type-provider-json-schema-to-ts';
import { FromSchema } from 'json-schema-to-ts';

import {
  AccessErrorResponseSchema,
  ErrorResponseSchema,
  registerSchemas,
  UserManagementHeadersSchema,
} from '../schemas.js';
import { HavenServiceAccountIdType as IdType, tx } from 'haven-hid-domain';
import { HID_VALIDATION_PATTERN } from '../validation.js';
import { ValidationError } from '../../error.js';
import { X_APP_ID_HEADER } from '../header.js';
import { getDependencies } from '../../dependencies/dependencies.js';
import { getBearerToken, validateBearerToken } from '../../access/validate-bearer-token.js';
import { UNLINK_ACCOUNT_SOURCE_SUFFIX } from '../../../../../domain/hid/dist/hid/transaction.js';

export const URI = '/admin/hid/account';

const RequestBodySchema = {
  type: 'object',
  $id: 'unlinkAccount',
  description: `Unlink service id to HID.`,
  properties: {
    hid: {
      type: 'string',
      pattern: HID_VALIDATION_PATTERN,
    },
    id: { type: 'number' },
    idType: {
      type: 'string',
      enum: Object.values(IdType),
    },
  },
  required: ['hid', 'id', 'idType'],
  additionalProperties: false,
} as const;

const SuccessResponseSchema = {
  type: 'object',
  $id: 'unlinkAccountSuccess',
  description: 'Account id has been unlinked from HID',
  properties: {
    hid: {
      type: 'string',
      pattern: HID_VALIDATION_PATTERN,
    },
  },
  required: ['hid'],
  additionalProperties: false,
  example: {
    hid: 'A1B2C2DDDDDD',
  },
} as const;

const unlinkAccountConfig = {
  schema: {
    body: RequestBodySchema,
    response: {
      200: SuccessResponseSchema,
      400: ErrorResponseSchema,
      403: AccessErrorResponseSchema,
    },
    headers: {
      ...UserManagementHeadersSchema,
      properties: {
        ...UserManagementHeadersSchema.properties,
        authorization: {
          type: 'string',
          description: 'Authorization header via Bearer Token',
        },
      },
    },
  },
  config: {
    openapi: {
      description: `Unlink service id to HID.`,
      summary: 'Unlink Account',
      tags: ['HID'],
      security: [{ appKeyId: [], appKeyAuth: [] }],
    },
    rateLimit: {},
    method: 'DELETE',
    url: URI,
  },
} as const;

const schemas = [RequestBodySchema, SuccessResponseSchema, AccessErrorResponseSchema];

export const unlinkAccount: FastifyPluginAsyncJsonSchemaToTs = async (fastify) => {
  const server = fastify.withTypeProvider<JsonSchemaToTsProvider>();
  registerSchemas(schemas, fastify);
  const { store, keysProvider } = getDependencies();

  server.delete<{
    Body: FromSchema<typeof RequestBodySchema>;
    attachValidation: true;
  }>(URI, unlinkAccountConfig, async (req, res): Promise<void> => {
    const { hid, idType, id } = req.body;
    const { authorization } = req.headers;

    const jwks = keysProvider.jwks();

    const bearerToken = getBearerToken(authorization);
    const jwt = await validateBearerToken(jwks, bearerToken);
    const source = req.headers[X_APP_ID_HEADER] as string;
    const sourceSuffix = jwt.onBehalfOf ? `${jwt.sub}` : UNLINK_ACCOUNT_SOURCE_SUFFIX;
    const transaction = tx(`${source}:${sourceSuffix}`);

    try {
      switch (idType) {
        case IdType.OWNER_ID:
          const owner = await store.getHIDAndEmailForPlot(id);
          if (owner && owner.hid !== hid) {
            throw new ValidationError(
              `Cannot unlink HID ${hid} from plot ${id}. owner is linked to other hid ${owner.hid}.`,
            );
          }
          await store.unlinkHIDFromPlot(id, transaction);
          break;
        case IdType.SEAWARE_CLIENT_ID:
          const guest = await store.getHIDAndEmailForSeaWare(id);

          if (guest && guest.hid !== hid) {
            throw new ValidationError(
              `Cannot unlink HID ${hid} from client ${id}. guest is linked to other hid ${guest.hid}.`,
            );
          }
          await store.unlinkHIDFromSeaware(id, transaction);
          break;
        default:
          throw new ValidationError(`body/idType unrecognised idType ${idType as string}`);
      }
      return res.send({ hid });
    } catch (e: unknown) {
      req.log.error(`Error unlinking account for ${idType} ${id}`, e);
      throw e;
    }
  });

  return Promise.resolve();
};
