import { HavenServiceAccount, HavenServiceAccountIdType, ValidEmail } from 'haven-hid-domain';
import { getDependencies } from '../../dependencies/dependencies.js';
import { AdminIdentitySearchResult } from './admin.search.in.js';

export const identitySearchByEmail = async (
  email: ValidEmail,
): Promise<AdminIdentitySearchResult[]> => {
  const { identityManagement } = getDependencies();
  const results = await identityManagement.search({ email: email.email });
  return results
    .filter((r) => r.migrated || r.accounts.length > 0)
    .map((r) => ({ ...r, email: r.email.email }));
};

export const identitySearchByAccount = async (
  accounts: HavenServiceAccount[],
): Promise<(AdminIdentitySearchResult | undefined)[]> => {
  const identitiesPromises = accounts.map(async ({ type, id }) => {
    const identity = await findIdentityByAccount({ type, id });
    if (!identity) return;

    const hid = identity.hid;
    const email = identity.email?.email;

    if (!hid || !email) return;

    return {
      hid,
      email,
      accounts: [{ id, type }],
      migrated: identity.identityExists,
    };
  });

  const identities = await Promise.all(identitiesPromises);
  return identities.filter((identity) => !!identity);
};

const findIdentityByAccount = async ({ type, id }: HavenServiceAccount) => {
  const { hidStore } = getDependencies();

  if (type === HavenServiceAccountIdType.OWNER_ID) {
    const plotAccount = await hidStore.findAllForPlotOwnerId(id);
    return plotAccount;
  }

  if (type === HavenServiceAccountIdType.SEAWARE_CLIENT_ID) {
    const seawareAccount = await hidStore.findAllForSeaWareClientId(id);
    return seawareAccount;
  }

  return undefined;
};
