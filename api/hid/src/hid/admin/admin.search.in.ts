import {
  FastifyPluginAsyncJsonSchemaToTs,
  JsonSchemaToTsProvider,
} from '@fastify/type-provider-json-schema-to-ts';
import { FromSchema } from 'json-schema-to-ts';

import {
  AccessErrorResponseSchema,
  ErrorResponseSchema,
  registerSchemas,
  UserManagementHeadersSchema,
} from '../schemas.js';
import {
  identitySearchByAccount as findIdentityByAccount,
  identitySearchByEmail,
} from './identity-search.js';
import { HavenServiceAccount, IdentitySearchResult, tx, ValidEmail } from 'haven-hid-domain';
import { X_APP_ID_HEADER } from '../header.js';
import { identitySearchViaServices } from './identity-search-via-services.js';

export const URI = '/admin/identity/search';

const RequestSchema = {
  $id: 'searchParams',
  type: 'object',
  properties: {
    email: { type: 'string' },
    accounts: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'number' },
          type: { type: 'string' },
        },
        required: ['id', 'type'],
      },
    },
  },
  oneOf: [{ required: ['email'] }, { required: ['accounts'] }],
  additionalProperties: false,
} as const;

const SuccessResponseSchema = {
  type: 'array',
  $id: 'searchIdentitySuccess',
  description: 'Return identity and state of linked accounts',
  properties: {
    items: {
      type: 'object',
      properties: {
        hid: { type: 'string' },
        email: { type: 'string' },
        migrated: { type: 'boolean' },
        title: { type: 'string' },
        name: { type: 'string' },
        firstName: { type: 'string' },
        lastName: { type: 'string' },
        accounts: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'number' },
              type: { type: 'string' },
            },
          },
        },
      },
      additionalProperties: false,
    },
  },
  example: [
    {
      hid: 'A1B2C2DDDDDD',
      email: '<EMAIL>',
      title: 'Mr',
      lastName: 'False',
    },
  ],
} as const;

const Config = {
  schema: {
    body: RequestSchema,
    response: {
      200: SuccessResponseSchema,
      400: ErrorResponseSchema,
      403: AccessErrorResponseSchema,
      422: ErrorResponseSchema,
    },
    headers: UserManagementHeadersSchema,
  },
  config: {
    openapi: {
      description: `Search for user in Haven Identity and Plot/Seaware. Note: Requires the X-APP-KEY to have user management permissions.`,
      summary: 'User management api to search for user',
      tags: ['Admin'],
      security: [{ appKeyId: [], appKeyAuth: [] }],
    },
    rateLimit: {},
    method: 'POST',
    url: URI,
  },
} as const;

export type AdminIdentitySearchResult = Omit<IdentitySearchResult, 'email'> & { email: string };

const schemas = [RequestSchema, SuccessResponseSchema, AccessErrorResponseSchema];

export const searchIdentity: FastifyPluginAsyncJsonSchemaToTs = (fastify) => {
  const server = fastify.withTypeProvider<JsonSchemaToTsProvider>();

  registerSchemas(schemas, fastify);

  server.post<{
    Body: FromSchema<typeof RequestSchema>;
    Reply: FromSchema<typeof SuccessResponseSchema>;
    attachValidation: true;
  }>(URI, Config, async (req, res): Promise<void> => {
    try {
      const appId = req.headers[X_APP_ID_HEADER] as string;
      const {
        body: { email, accounts },
      } = req;

      if (email) {
        const identities = await findIdentityByEmail(email, appId);
        return await res.send(identities);
      }

      if (accounts && accounts.length) {
        const identities = await findIdentityByAccount(accounts as HavenServiceAccount[]);
        return await res.send(identities);
      }

      return await res.status(204).send();
    } catch (e) {
      req.log.error(`Error searching for email`, e);
      throw e;
    }
  });

  return Promise.resolve();
};

const findIdentityByEmail = async (email: string, appId: string) => {
  const validEmail = ValidEmail.newFromSafeInput(email);
  const identities = await identitySearchByEmail(validEmail);

  if (identities.length) return identities;
  return await identitySearchViaServices(validEmail, tx(appId));
};
