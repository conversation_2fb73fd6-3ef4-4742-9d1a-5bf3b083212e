import {
  FastifyPluginAsyncJsonSchemaToTs,
  JsonSchemaToTsProvider,
} from '@fastify/type-provider-json-schema-to-ts';
import { FromSchema } from 'json-schema-to-ts';

import {
  AccessErrorResponseSchema,
  ErrorResponseSchema,
  registerSchemas,
  UserManagementHeadersSchema,
} from '../schemas.js';
import { HID_VALIDATION_PATTERN } from '../validation.js';
import { identitySync } from './identity-sync.js';
import { identityLookup } from './identity-lookup.js';

export const URI = '/admin/identity/:hid';

const RequestParamsSchema = {
  $id: 'updateProfileParams',
  type: 'object',
  properties: {
    hid: {
      type: 'string',
      pattern: HID_VALIDATION_PATTERN,
    },
  },
  required: ['hid'],
} as const;

const SuccessResponseSchema = {
  type: 'object',
  $id: 'getIdentitySuccess',
  description: 'Return identity and state of linked accounts',
  properties: {
    hid: {
      type: 'string',
      pattern: HID_VALIDATION_PATTERN,
    },
    email: { type: 'string' },
    migrated: { type: 'boolean' },
    title: {
      type: 'string',
      enum: ['Rev', 'Sir', 'Miss', 'Ms', 'Mrs', 'Mr', 'Dr', 'Mx'],
    },
    name: { type: 'string' },
    firstName: { type: 'string' },
    lastName: { type: 'string' },
    phoneNumber: { type: 'string' },
    passwordStrength: { type: 'string' },
    address: {
      type: 'object',
      properties: {
        line1: { type: 'string' },
        line2: { type: 'string' },
        city: { type: 'string' },
        county: { type: 'string' },
        postcode: { type: 'string' },
        country: { type: 'string' },
      },
    },
    accounts: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'number' },
          type: { type: 'string' },
          state: { type: 'string' },
          hid: { type: 'string' },
        },
      },
    },
    ownerLegalData: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        title: { type: 'string' },
        firstName: { type: 'string' },
        lastName: { type: 'string' },
        address: {
          type: 'object',
          properties: {
            address1: { type: 'string' },
            address2: { type: 'string' },
            address3: { type: 'string' },
            address4: { type: 'string' },
            postcode: { type: 'string' },
          },
        },
      },
    },
  },
  additionalProperties: false,
  example: {
    hid: 'A1B2C2DDDDDD',
  },
} as const;

const GetProfileConfig = {
  schema: {
    params: RequestParamsSchema,
    response: {
      200: SuccessResponseSchema,
      400: ErrorResponseSchema,
      403: AccessErrorResponseSchema,
      422: ErrorResponseSchema,
    },
    headers: UserManagementHeadersSchema,
  },
  config: {
    openapi: {
      description: `Fetches profile associated with Haven Identity and Plot/Seaware. Note: Requires the X-APP-KEY to have user management permissions.`,
      summary: 'User management api to fetch profile and check with plot/seaware state',
      tags: ['Admin'],
      security: [{ appKeyId: [], appKeyAuth: [] }],
    },
    rateLimit: {},
    method: 'GET',
    url: URI,
  },
} as const;

const schemas = [RequestParamsSchema, SuccessResponseSchema, AccessErrorResponseSchema];
export const getIdentity: FastifyPluginAsyncJsonSchemaToTs = (fastify) => {
  const server = fastify.withTypeProvider<JsonSchemaToTsProvider>();

  registerSchemas(schemas, fastify);

  server.get<{
    Params: FromSchema<typeof RequestParamsSchema>;
    attachValidation: true;
  }>(URI, GetProfileConfig, async (req, res): Promise<void> => {
    const {
      params: { hid },
    } = req;

    try {
      const identity = await identityLookup(hid);
      const result = await identitySync(identity);
      return await res.send(result);
    } catch (e) {
      req.log.error(`Error getting profile for ${hid}`, e);
      throw e;
    }
  });

  return Promise.resolve();
};
