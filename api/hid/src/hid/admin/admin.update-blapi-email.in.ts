import {
  FastifyPluginAsyncJsonSchemaToTs,
  JsonSchemaToTsProvider,
} from '@fastify/type-provider-json-schema-to-ts';
import { FromSchema } from 'json-schema-to-ts';

import {
  AccessErrorResponseSchema,
  ErrorResponseSchema,
  registerSchemas,
  UserManagementHeadersSchema,
} from '../schemas.js';
import { getDependencies } from '../../dependencies/dependencies.js';
import { generateBlapiBearerToken } from '../../access/admin-blapi-access.js';
import { identityLookup } from './identity-lookup.js';
import { HID_VALIDATION_PATTERN } from '../validation.js';
import { HavenServiceAccountIdType } from 'haven-hid-domain';

export const URI = '/admin/identity/update-blapi-email';

const RequestParamsSchema = {
  $id: 'updateBlapiEmail',
  type: 'object',
  properties: {
    email: {
      type: 'string',
    },
    hid: {
      type: 'string',
      pattern: HID_VALIDATION_PATTERN,
    },
    adminUserUpn: {
      type: 'string',
    },
  },
  required: ['email', 'adminUserUpn'],
} as const;

const SuccessResponseSchema = {
  type: 'object',
  $id: 'updateBlapiEmailSuccess',
  description: 'Update email in Blapi',
  properties: {}, // TODO
  additionalProperties: true,
  example: {}, // TODO
} as const;

const updateBlapiEmailConfig = {
  schema: {
    params: RequestParamsSchema,
    response: {
      200: SuccessResponseSchema,
      400: ErrorResponseSchema,
      403: AccessErrorResponseSchema,
      422: ErrorResponseSchema,
    },
    headers: UserManagementHeadersSchema,
  },
  config: {
    openapi: {
      description: `Update email in Blapi. Note: Requires the X-APP-KEY to have user management permissions.`,
      summary: 'User management api to update email in Blapi',
      tags: ['Admin'],
      security: [{ appKeyId: [], appKeyAuth: [] }],
    },
    rateLimit: {},
    method: 'POST',
    url: URI,
  },
} as const;

const schemas = [RequestParamsSchema, SuccessResponseSchema, AccessErrorResponseSchema];
export const updateBalpiEmail: FastifyPluginAsyncJsonSchemaToTs = (fastify) => {
  const server = fastify.withTypeProvider<JsonSchemaToTsProvider>();
  const { blapi } = getDependencies();
  registerSchemas(schemas, fastify);

  server.put<{
    Params: FromSchema<typeof RequestParamsSchema>;
    attachValidation: true;
  }>(URI, updateBlapiEmailConfig, async (req, res): Promise<void> => {
    try {
      const { email, hid, adminUserUpn } = req.body as {
        email: string;
        hid: string;
        adminUserUpn: string;
      };

      const identity = await identityLookup(hid);

      const seawareAccount = identity.accounts.find(
        ({ type }) => type === HavenServiceAccountIdType.SEAWARE_CLIENT_ID,
      );
      if (!seawareAccount) {
        return await res.status(404).send({});
      }

      const blapiBearerToken = await generateBlapiBearerToken(
        {
          hid,
          accounts: identity.accounts,
          migrated: identity.migrated,
        },
        adminUserUpn,
      );

      await blapi.updateProfile(blapiBearerToken, {
        email,
      });
      return await res.send({ success: true });
    } catch (e) {
      req.log.error(`Error getting profile from Blapi`, e);
      throw e;
    }
  });

  return Promise.resolve();
};
