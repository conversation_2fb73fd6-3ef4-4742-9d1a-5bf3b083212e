import {
  FastifyPluginAsyncJsonSchemaToTs,
  JsonSchemaToTsProvider,
} from '@fastify/type-provider-json-schema-to-ts';
import { FromSchema } from 'json-schema-to-ts';

import {
  AccessErrorResponseSchema,
  ErrorResponseSchema,
  registerSchemas,
  UserManagementHeadersSchema,
} from '../schemas.js';
import { getDependencies } from '../../dependencies/dependencies.js';
import { HID_VALIDATION_PATTERN } from '../validation.js';
import { identityLookup } from './identity-lookup.js';
import { HavenServiceAccountIdType } from 'haven-hid-domain';

export const URI = '/admin/identity/:hid/blapi-profile';

const RequestParamsSchema = {
  $id: 'getProfileFromBlapi',
  type: 'object',
  properties: {
    hid: {
      type: 'string',
      pattern: HID_VALIDATION_PATTERN,
    },
  },
  required: ['hid'],
} as const;

const SuccessResponseSchema = {
  type: 'object',
  $id: 'getProfileFromBlapiSuccess',
  description: 'Return Profile details from Blapi',
  properties: {}, // TODO
  additionalProperties: true,
  example: {}, // TODO
} as const;

const GetBlapiProfileConfig = {
  schema: {
    params: RequestParamsSchema,
    response: {
      200: SuccessResponseSchema,
      400: ErrorResponseSchema,
      403: AccessErrorResponseSchema,
      422: ErrorResponseSchema,
    },
    headers: UserManagementHeadersSchema,
  },
  config: {
    openapi: {
      description: `Fetches profile from Blapi. Note: Requires the X-APP-KEY to have user management permissions.`,
      summary: 'User management api to fetch profile from Blapi',
      tags: ['Admin'],
      security: [{ appKeyId: [], appKeyAuth: [] }],
    },
    rateLimit: {},
    method: 'POST',
    url: URI,
  },
} as const;

const schemas = [RequestParamsSchema, SuccessResponseSchema, AccessErrorResponseSchema];
export const getBlapiProfile: FastifyPluginAsyncJsonSchemaToTs = (fastify) => {
  const server = fastify.withTypeProvider<JsonSchemaToTsProvider>();
  const { blapi } = getDependencies();
  registerSchemas(schemas, fastify);

  server.get<{
    Params: FromSchema<typeof RequestParamsSchema>;
    attachValidation: true;
  }>(URI, GetBlapiProfileConfig, async (req, res): Promise<void> => {
    try {
      const {
        params: { hid },
      } = req;
      const identity = await identityLookup(hid);
      const seawareAccount = identity.accounts.find(
        ({ type }) => type === HavenServiceAccountIdType.SEAWARE_CLIENT_ID,
      );

      if (!seawareAccount) {
        return await res.status(404).send({});
      }

      const profile = await blapi.fetchProfile(identity.email.email);
      return await res.send(profile);
    } catch (e) {
      req.log.error(`Error getting profile from Blapi`, e);
      throw e;
    }
  });

  return Promise.resolve();
};
