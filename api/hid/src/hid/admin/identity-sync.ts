import {
  HavenServiceAccount,
  HavenServiceAccountIdType,
  HID,
  MigratedHavenIdentity,
  UnmigratedHavenIdentity,
} from 'haven-hid-domain';
import { getDependencies } from '../../dependencies/dependencies.js';
import { Guest } from 'haven-identity-blapi-client';
import { Owner, OwnerAddress } from 'haven-identity-plot-client';
import { PasswordStrength } from '@havenengineering/module-shared-password-strength';

export enum AccountState {
  MATCHED = 'MATCHED',
  EMAIL_CHANGED = 'EMAIL_CHANGED', // Admin can force change of email to match Identity or unlink them
  UNREGISTERED = 'UNREGISTERED',
  UNLINKED_IN_IDENTITY = 'UNLINKED_IN_IDENTITY', // Admin link them
  OWNED_BY_OTHER_IDENTITY = 'OWNED_BY_OTHER_IDENTITY',
  MISMATCH = 'MISMATCH', // id in identity != id in plot/seaware - Admin can correct  link to match seaware/plot  (not sure how this can happen)
  ERROR = 'ERROR', // Error looking  up the user
}

export interface ValidatedHavenServiceAccount extends HavenServiceAccount {
  state: AccountState;
  hid?: HID;
}

type Lookup<T> = {
  value?: T | undefined;
  error?: Error;
};

const lookupOwner = async (email: string, id: number | undefined): Promise<Lookup<Owner>> => {
  try {
    const { plot } = getDependencies();
    const ownerByEmail = await plot.fetchProfile(email);
    if (ownerByEmail) {
      return { value: ownerByEmail };
    }
    if (id) {
      return {
        value: await plot.fetchProfileById(id),
      };
    }
    return { value: undefined };
  } catch (e) {
    return {
      error: new Error('Failed owner lookup', {
        cause: e,
      }),
    };
  }
};

const lookupGuest = async (email: string, id: number | undefined): Promise<Lookup<Guest>> => {
  try {
    const { blapi, blapiAccount } = getDependencies();
    const guestByEmail = await blapi.fetchProfile(email);
    if (guestByEmail) {
      return { value: guestByEmail };
    }
    if (id) {
      return {
        value: await blapiAccount.fetchProfileById(id),
      };
    }
    return { value: undefined };
  } catch (e) {
    return {
      error: new Error('Failed guest lookup', {
        cause: e,
      }),
    };
  }
};

const lookupIdentityByAccount = async (account: HavenServiceAccount): Promise<HID | undefined> => {
  const { hidStore } = getDependencies();
  switch (account.type) {
    case HavenServiceAccountIdType.OWNER_ID:
      const allByPlotOwnerId = await hidStore.findAllForPlotOwnerId(account.id);
      return allByPlotOwnerId.hid;
    case HavenServiceAccountIdType.SEAWARE_CLIENT_ID:
      const allBySeaWareClientId = await hidStore.findAllForSeaWareClientId(account.id);
      return allBySeaWareClientId.hid;
    default:
      return undefined;
  }
};

const checkAccounts = async (
  email: string,
  accounts: HavenServiceAccount[],
  owner: Owner | Error | undefined,
  guest: Guest | Error | undefined,
): Promise<ValidatedHavenServiceAccount[]> => {
  const updated: ValidatedHavenServiceAccount[] = accounts.map((account) => {
    if (account.type === HavenServiceAccountIdType.SEAWARE_CLIENT_ID) {
      if (!guest) {
        return {
          ...account,
          state: AccountState.UNREGISTERED,
        };
      }
      if (guest && guest instanceof Error) {
        return {
          ...account,
          state: AccountState.ERROR,
        };
      }
      if (guest && guest.email !== email) {
        return {
          ...account,
          state: AccountState.EMAIL_CHANGED,
        };
      }
      if (guest && guest.clientId !== account.id) {
        return {
          ...account,
          state: AccountState.MISMATCH,
        };
      }
    }
    if (account.type === HavenServiceAccountIdType.OWNER_ID) {
      if (!owner) {
        return {
          ...account,
          state: AccountState.UNREGISTERED,
        };
      }
      if (owner && owner instanceof Error) {
        return {
          ...account,
          state: AccountState.ERROR,
        };
      }
      if (owner && owner.email !== email) {
        return {
          ...account,
          state: AccountState.EMAIL_CHANGED,
        };
      }
      if (owner && owner.ownerId !== account.id) {
        return {
          ...account,
          state: AccountState.MISMATCH,
        };
      }
    }
    return {
      ...account,
      state: AccountState.MATCHED,
    };
  });

  if (owner && !accounts.some((it) => it.type === HavenServiceAccountIdType.OWNER_ID)) {
    if (owner instanceof Error) {
      updated.push({
        id: -1,
        type: HavenServiceAccountIdType.OWNER_ID,
        state: AccountState.ERROR,
      });
    } else {
      const account = {
        id: owner.ownerId,
        type: HavenServiceAccountIdType.OWNER_ID,
      };
      const hid = await lookupIdentityByAccount(account);
      if (hid) {
        updated.push({
          ...account,
          state: AccountState.OWNED_BY_OTHER_IDENTITY,
          hid: hid,
        });
      } else {
        updated.push({
          ...account,
          state: AccountState.UNLINKED_IN_IDENTITY,
        });
      }
    }
  }
  if (guest && !accounts.some((it) => it.type === HavenServiceAccountIdType.SEAWARE_CLIENT_ID)) {
    if (guest instanceof Error) {
      updated.push({
        id: -1,
        type: HavenServiceAccountIdType.SEAWARE_CLIENT_ID,
        state: AccountState.ERROR,
      });
    } else {
      const account = {
        id: guest.clientId,
        type: HavenServiceAccountIdType.SEAWARE_CLIENT_ID,
      };
      const hid = await lookupIdentityByAccount(account);
      if (hid) {
        updated.push({
          ...account,
          state: AccountState.OWNED_BY_OTHER_IDENTITY,
          hid: hid,
        });
      } else {
        updated.push({
          ...account,
          state: AccountState.UNLINKED_IN_IDENTITY,
        });
      }
    }
  }
  return updated;
};

export interface SyncedHavenIdentity {
  hid: HID;
  email: string;
  title: string | undefined;
  firstName: string | undefined;
  lastName: string | undefined;
  phoneNumber: string | undefined;
  passwordStrength: PasswordStrength | undefined;
  accounts: ValidatedHavenServiceAccount[];
  migrated: boolean;
  address:
    | {
        line1: string;
        line2: string | undefined;
        city: string;
        county: string | undefined;
        postcode: string;
        country: string;
      }
    | undefined;
  ownerLegalData?: {
    name: string;
    title: string;
    firstName: string;
    lastName: string;
    address: OwnerAddress;
  };
}

export const identitySync = async (
  identity: MigratedHavenIdentity | UnmigratedHavenIdentity,
): Promise<SyncedHavenIdentity> => {
  const migrated = identity.migrated;
  const hid = identity.hid;
  const email = identity.email.email;
  const title = migrated ? identity.title : undefined;
  const firstName = migrated ? identity.firstName : undefined;
  const lastName = migrated ? identity.lastName : undefined;
  const phoneNumber = migrated ? identity.phoneNumber : undefined;
  const passwordStrength = migrated ? identity.passwordStrength : undefined;
  const address = migrated ? identity.address : undefined;

  const ownerId = identity.accounts.find(
    (it) => it.type === HavenServiceAccountIdType.OWNER_ID,
  )?.id;
  const seawareId = identity.accounts.find(
    (it) => it.type === HavenServiceAccountIdType.SEAWARE_CLIENT_ID,
  )?.id;
  const { value: owner, error: ownerError } = await lookupOwner(email, ownerId);
  const { value: guest, error: guestError } = await lookupGuest(email, seawareId);
  const accounts = await checkAccounts(
    email,
    identity.accounts,
    owner || ownerError,
    guest || guestError,
  );

  return {
    hid,
    email,
    passwordStrength,
    migrated,
    title: title || guest?.title || owner?.title,
    firstName: firstName || guest?.firstName || owner?.firstName,
    lastName: lastName || guest?.lastName || owner?.lastName,
    phoneNumber:
      phoneNumber ||
      guest?.phoneNumber ||
      owner?.mobilePhone ||
      owner?.dayPhone ||
      owner?.eveningPhone,
    address: address
      ? {
          line1: address.address_line1,
          line2: address.address_line2,
          city: address.address_city,
          county: address.address_county,
          postcode: address.address_postcode,
          country: address.address_country,
        }
      : undefined,
    accounts,
    ...(owner
      ? {
          ownerLegalData: {
            name: owner.name,
            title: owner.title,
            firstName: owner.firstName,
            lastName: owner.lastName,
            address: {
              address1: owner.address?.address1 || '',
              address2: owner.address?.address2 || '',
              address3: owner.address?.address3 || '',
              address4: owner.address?.address4 || '',
              postcode: owner.address?.postcode || '',
            },
          },
        }
      : {}),
  };
};
