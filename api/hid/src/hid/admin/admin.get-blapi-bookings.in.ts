import {
  FastifyPluginAsyncJsonSchemaToTs,
  JsonSchemaToTsProvider,
} from '@fastify/type-provider-json-schema-to-ts';
import { FromSchema } from 'json-schema-to-ts';

import {
  AccessErrorResponseSchema,
  ErrorResponseSchema,
  registerSchemas,
  UserManagementHeadersSchema,
} from '../schemas.js';
import { getDependencies } from '../../dependencies/dependencies.js';
import { HID_VALIDATION_PATTERN } from '../validation.js';
import {
  ADMIN_USER_UPN_HEADER,
  generateBlapiBearerToken,
} from '../../access/admin-blapi-access.js';
import { identityLookup } from './identity-lookup.js';
import { HavenServiceAccountIdType } from 'haven-hid-domain';

export const URI = '/admin/identity/:hid/blapi-bookings';

const RequestParamsSchema = {
  $id: 'getBookingsFromBlapi',
  type: 'object',
  properties: {
    hid: {
      type: 'string',
      pattern: HID_VALIDATION_PATTERN,
    },
  },
  required: ['hid'],
} as const;

const SuccessResponseSchema = {
  type: 'object',
  $id: 'getBookingsFromBlapiSuccess',
  description: 'Return Bookings from Blapi',
  properties: {}, // TODO
  additionalProperties: true,
  example: {}, // TODO
} as const;

const GetBlapiBookingsConfig = {
  schema: {
    params: RequestParamsSchema,
    response: {
      200: SuccessResponseSchema,
      400: ErrorResponseSchema,
      403: AccessErrorResponseSchema,
      422: ErrorResponseSchema,
    },
    headers: UserManagementHeadersSchema,
  },
  config: {
    openapi: {
      description: `Fetches bookings from Blapi. Note: Requires the X-APP-KEY to have user management permissions.`,
      summary: 'User management api to fetch bookings from Blapi',
      tags: ['Admin'],
      security: [{ appKeyId: [], appKeyAuth: [] }],
    },
    rateLimit: {},
    method: 'GET',
    url: URI,
  },
} as const;

const schemas = [RequestParamsSchema, SuccessResponseSchema, AccessErrorResponseSchema];

export const getBlapiBookings: FastifyPluginAsyncJsonSchemaToTs = (fastify) => {
  const server = fastify.withTypeProvider<JsonSchemaToTsProvider>();
  const { blapi } = getDependencies();
  registerSchemas(schemas, fastify);

  server.get<{
    Params: FromSchema<typeof RequestParamsSchema>;
    attachValidation: true;
  }>(URI, GetBlapiBookingsConfig, async (req, res): Promise<void> => {
    try {
      const {
        params: { hid },
      } = req;

      const adminUserUpn = req.headers[ADMIN_USER_UPN_HEADER] as string;
      const identity = await identityLookup(hid);
      const seawareAccount = identity.accounts.find(
        ({ type }) => type === HavenServiceAccountIdType.SEAWARE_CLIENT_ID,
      );

      if (!seawareAccount) {
        return await res.send({ bookings: [] });
      }

      const params = {
        responseincludes: 'balance,cabinCategory,fullguest,cabinAssigned',
        sortField: 'arrivalDate',
        sortDirection: 'ASC',
        statusFilter: 'BK,OF',
      };

      const blapiBearerToken = await generateBlapiBearerToken(
        {
          hid,
          accounts: identity.accounts,
          migrated: identity.migrated,
        },
        adminUserUpn,
      );
      const bookings = await blapi.getBookings(blapiBearerToken, params);
      return await res.send({ bookings });
    } catch (e) {
      req.log.error(`Error getting bookings from Blapi`, e);
      throw e;
    }
  });

  return Promise.resolve();
};
