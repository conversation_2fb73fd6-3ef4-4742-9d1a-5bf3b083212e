import {
  FastifyPluginAsyncJsonSchemaToTs,
  JsonSchemaToTsProvider,
} from '@fastify/type-provider-json-schema-to-ts';
import { FromSchema } from 'json-schema-to-ts';
import { ValidEmail, EmailVerificationTokenType } from 'haven-hid-domain';
import { validateEmail } from '../validation.js';
import {
  AccessErrorResponseSchema,
  ErrorResponseSchema,
  HeadersSchema,
  registerSchemas,
} from '../schemas.js';
import { getDependencies } from '../../dependencies/dependencies.js';

export const URI = '/hid/email-verify';

const RequestSchema = {
  type: 'object',
  $id: 'generateEmailVerificationToken',
  description: 'Given an email, create an email verification token and a HID if not exist',
  properties: { email: { type: 'string' } },
  required: ['email'],
  additionalProperties: false,
} as const;

const SuccessResponseSchema = {
  type: 'object',
  $id: 'generateEmailVerificationTokenSuccess',
  description: 'Return emailVerificationToken for email',
  properties: {
    emailVerificationToken: { type: 'string', nullable: true },
  },
  required: ['emailVerificationToken'],
  additionalProperties: false,
  example: {
    emailVerificationToken: '01JBXMPRD3XH31WNTJH9DQ9RX1',
  },
} as const;

const generateEmailVerificationTokenEndpoint = {
  schema: {
    body: RequestSchema,
    response: {
      200: SuccessResponseSchema,
      400: ErrorResponseSchema,
      403: AccessErrorResponseSchema,
    },
    headers: HeadersSchema,
  },
  config: {
    openapi: {
      description: 'Given an email, create an email verification token and HID if not exist',
      summary: 'Generate Email Verification Token',
      tags: ['Identity'],
      security: [{ appKeyId: [], appKeyAuth: [] }],
    },
    rateLimit: {},
    method: 'POST',
    url: URI,
  },
} as const;

export const generateEmailVerificationToken: FastifyPluginAsyncJsonSchemaToTs = (fastify) => {
  const server = fastify.withTypeProvider<JsonSchemaToTsProvider>();
  const schemas = [RequestSchema, SuccessResponseSchema, AccessErrorResponseSchema];
  const { emailVerification } = getDependencies();

  const generateEmailVerificationToken = async (validEmail: ValidEmail): Promise<string | null> => {
    try {
      const returnUrl = '';
      const token = await emailVerification.generateRequestToken(
        validEmail,
        returnUrl,
        EmailVerificationTokenType.EMAIL,
      );
      return token.token;
    } catch (error) {
      return null;
    }
  };

  registerSchemas(schemas, server);

  server.post<{
    Body: FromSchema<typeof RequestSchema>;
    attachValidation: true;
  }>(URI, generateEmailVerificationTokenEndpoint, async (req, res): Promise<void> => {
    const { email } = req.body;
    const validEmail = validateEmail(email);
    const token = await generateEmailVerificationToken(validEmail);

    if (!token) {
      return res.status(500).send({
        code: 'UNKNOWN_ERROR',
        message: 'Failed to generate email verification token',
        statusCode: 500,
      });
    }

    return res.send({
      emailVerificationToken: token,
    });
  });

  return Promise.resolve();
};
