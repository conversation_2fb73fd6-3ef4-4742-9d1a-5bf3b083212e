import {
  FastifyPluginAsyncJsonSchemaToTs,
  JsonSchemaToTsProvider,
} from '@fastify/type-provider-json-schema-to-ts';
import { logger } from '@havenengineering/module-haven-logging';
import {
  HID,
  Transaction,
  ValidEmail,
  HavenServiceAccountIdType as IdType,
  tx,
} from 'haven-hid-domain';
import { FromSchema } from 'json-schema-to-ts';
import { X_APP_ID_HEADER } from '../header.js';
import { AccessErrorResponseSchema, ErrorResponseSchema, HeadersSchema } from '../schemas.js';
import { validateEmail, validateId } from '../validation.js';
import { getDependencies } from '../../dependencies/dependencies.js';

export const URI = '/hid/email';

const RequestSchema = {
  type: 'object',
  $id: 'HIDForEmail',
  description:
    'Generate or retrieve HID for email.  An optional pair of idType/id can also be provided to support changes of email for registered users such as Owners or Haven guests.',
  properties: {
    email: { type: 'string' },
    idType: {
      type: 'string',
      enum: Object.values(IdType),
    },
    id: { type: 'number' },
  },
  required: ['email'],
  additionalProperties: false,
} as const;

const SuccessResponseSchema = {
  type: 'object',
  $id: 'HIDForEmailSuccess',
  description: 'Return HID',
  properties: {
    hid: {
      type: 'string',
    },
  },
  required: ['hid'],
  additionalProperties: false,
  example: {
    hid: 'A123DF3FG7Z0',
  },
} as const;

const HIDForEmailEndpoint = {
  schema: {
    body: RequestSchema,
    response: {
      200: SuccessResponseSchema,
      400: ErrorResponseSchema,
      403: AccessErrorResponseSchema,
    },
    headers: HeadersSchema,
  },
  config: {
    openapi: {
      description:
        'Generate or retrieve a unique Haven Identity (HID) given an Email. See repo for more [examples](https://github.com/HavenEngineering/haven-identity/tree/master/api/hid/examples).',
      summary: 'Get HID for Email',
      tags: ['HID'],
      security: [{ appKeyId: [], appKeyAuth: [] }],
    },
    rateLimit: {},
    method: 'POST',
    url: URI,
  },
} as const;

export const HIDForEmail: FastifyPluginAsyncJsonSchemaToTs = (fastify) => {
  const server = fastify.withTypeProvider<JsonSchemaToTsProvider>();
  const { store } = getDependencies();

  const generateOrRetrieve = async (
    email: ValidEmail,
    type: IdType,
    id: number | undefined,
    tx: Transaction,
  ): Promise<HID> => {
    if (id) {
      switch (type) {
        case IdType.OWNER_ID:
          return store.getHIDForEmailAndPlot(email, undefined, id, tx);
        case IdType.SEAWARE_CLIENT_ID:
          return store.getHIDForEmailAndSeaWare(email, undefined, id, tx);
      }
    }
    return store.getHIDForEmail(email, undefined, tx);
  };

  for (const schema of [RequestSchema, SuccessResponseSchema, AccessErrorResponseSchema] as const) {
    server.addSchema(schema);
  }

  server.post<{
    Body: FromSchema<typeof RequestSchema>;
    attachValidation: true;
  }>(URI, HIDForEmailEndpoint, async (request, reply): Promise<void> => {
    const email: string = request.body.email;
    const idType = request.body.idType as IdType;
    const id = request.body.id;

    validateId(idType, id);

    const validEmail = validateEmail(email);
    const source = request.headers[X_APP_ID_HEADER] as string;

    try {
      const result = await generateOrRetrieve(validEmail, idType, id, tx(source));
      void reply.send({ hid: result });
    } catch (error: any) {
      logger.error('Failed to get/generate HID', {
        email,
        idType,
        id,
        cause: error.reason,
      });
      throw error;
    }
  });
  return Promise.resolve();
};
