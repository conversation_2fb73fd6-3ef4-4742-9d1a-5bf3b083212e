import {
  FastifyPluginAsyncJsonSchemaToTs,
  JsonSchemaToTsProvider,
} from '@fastify/type-provider-json-schema-to-ts';
import { HID_LENGTH } from 'haven-hid-domain';
import { FromSchema } from 'json-schema-to-ts';
import { validateHID } from '../validation.js';
import { AccessErrorResponseSchema, ErrorResponseSchema, HeadersSchema } from '../schemas.js';
import { getDependencies } from '../../dependencies/dependencies.js';

export const URI = '/email/hid';

const RequestSchema = {
  type: 'object',
  $id: 'EmailForHID',
  description: 'Given a HID return the clean validated email currently linked to that HID',
  properties: {
    hid: {
      type: 'string',
      pattern: `[A-Z0-9]{${HID_LENGTH}}`,
    },
  },
  required: ['hid'],
  additionalProperties: false,
} as const;

const SuccessResponseSchema = {
  type: 'object',
  $id: 'EmailForHIDSuccess',
  description: 'Return Email',
  properties: {
    email: {
      type: 'string',
    },
  },
  required: ['email'],
  additionalProperties: false,
  example: {
    email: '<EMAIL>',
  },
} as const;

const EmailForHIDEndpoint = {
  schema: {
    body: RequestSchema,
    response: {
      200: SuccessResponseSchema,
      400: ErrorResponseSchema,
      403: AccessErrorResponseSchema,
    },
    headers: HeadersSchema,
  },
  config: {
    openapi: {
      description:
        'Given a valid Haven Identity (HID) return the current associated email.  This email may change if the HID is associated with an owner or seaware client and that user changes their email.',
      summary: 'Get Email for HID',
      tags: ['HID'],
      security: [{ appKeyId: [], appKeyAuth: [] }],
    },
    rateLimit: {},
    method: 'POST',
    url: URI,
  },
} as const;

export const EmailForHID: FastifyPluginAsyncJsonSchemaToTs = (fastify) => {
  const server = fastify.withTypeProvider<JsonSchemaToTsProvider>();
  const { store } = getDependencies();

  for (const schema of [RequestSchema, SuccessResponseSchema, AccessErrorResponseSchema] as const) {
    server.addSchema(schema);
  }

  server.post<{
    Body: FromSchema<typeof RequestSchema>;
    attachValidation: true;
  }>(URI, EmailForHIDEndpoint, async (request, reply): Promise<void> => {
    const hid = request.body.hid;
    validateHID(hid);

    const email = await store.getEmailForHID(hid);
    if (email) {
      void reply.send({
        email: email.email,
      });
    } else {
      void reply.code(400).send({
        code: 'BAD_REQUEST',
        message: 'No email for HID',
        statusCode: 400,
      });
    }
  });

  return Promise.resolve();
};
