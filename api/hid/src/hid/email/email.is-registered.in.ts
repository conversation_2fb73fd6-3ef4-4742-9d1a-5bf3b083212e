import {
  FastifyPluginAsyncJsonSchemaToTs,
  JsonSchemaToTsProvider,
} from '@fastify/type-provider-json-schema-to-ts';
import { FromSchema } from 'json-schema-to-ts';
import { ValidEmail } from 'haven-hid-domain';

import { validateEmail } from '../validation.js';
import {
  AccessErrorResponseSchema,
  ErrorResponseSchema,
  HeadersSchema,
  registerSchemas,
} from '../schemas.js';
import { getDependencies } from '../../dependencies/dependencies.js';

export const URI = '/identity/is-registered';

const RequestSchema = {
  type: 'object',
  $id: 'isEmailRegistered',
  description:
    'Given an Email, check for an associated registered account in PLOT SEAWARE or Haven Identity',
  properties: { email: { type: 'string' } },
  required: ['email'],
  additionalProperties: false,
} as const;

const SuccessResponseSchema = {
  type: 'object',
  $id: 'isEmailRegisteredSuccess',
  description:
    'Return isRegistered boolean, source info and hid if Haven identity available for email',
  properties: {
    isRegistered: { type: 'boolean' },
    hid: { type: 'string', nullable: true },
    source: {
      type: 'string',
      enum: ['plot', 'seaware', 'identity'],
      nullable: true,
    },
  },
  required: ['isRegistered', 'hid', 'source'],
  additionalProperties: false,
  example: {
    isRegistered: true,
    hid: '1234',
    source: 'plot',
  },
} as const;

const isEmailRegisteredEndpoint = {
  schema: {
    body: RequestSchema,
    response: {
      200: SuccessResponseSchema,
      400: ErrorResponseSchema,
      403: AccessErrorResponseSchema,
    },
    headers: HeadersSchema,
  },
  config: {
    openapi: {
      description:
        'Checks if an email is registered in PLOT, SEAWARE, of has an associated Haven Identity Profile',
      summary: 'Check if Email is registered',
      tags: ['Identity'],
      security: [{ appKeyId: [], appKeyAuth: [] }],
    },
    rateLimit: {},
    method: 'POST',
    url: URI,
  },
} as const;

export const isEmailRegistered: FastifyPluginAsyncJsonSchemaToTs = (fastify) => {
  const server = fastify.withTypeProvider<JsonSchemaToTsProvider>();
  const schemas = [RequestSchema, SuccessResponseSchema, AccessErrorResponseSchema];
  const { identityManagement: identityStore, plot, blapi } = getDependencies();

  const isRegistered = async (
    validEmail: ValidEmail,
  ): Promise<{
    isRegistered: boolean;
    source: 'plot' | 'seaware' | 'identity' | null;
    hid: string | null;
  }> => {
    const storedIdentity = await identityStore.findByEmail(validEmail);
    if (storedIdentity)
      return {
        isRegistered: true,
        source: 'identity',
        hid: storedIdentity.hid,
      };
    if (await plot.isRegistered(validEmail.email))
      return {
        isRegistered: true,
        source: 'plot',
        hid: null,
      };
    if (await blapi.isRegistered(validEmail.email))
      return {
        isRegistered: true,
        source: 'seaware',
        hid: null,
      };

    return {
      isRegistered: false,
      source: null,
      hid: null,
    };
  };

  registerSchemas(schemas, server);

  server.post<{
    Body: FromSchema<typeof RequestSchema>;
    attachValidation: true;
  }>(URI, isEmailRegisteredEndpoint, async (req, res): Promise<void> => {
    const { email } = req.body;
    const validEmail = validateEmail(email);
    const registered = await isRegistered(validEmail);
    return res.send(registered);
  });

  return Promise.resolve();
};
