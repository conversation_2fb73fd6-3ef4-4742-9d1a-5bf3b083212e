import {
  FastifyPluginAsyncJsonSchemaToTs,
  JsonSchemaToTsProvider,
} from '@fastify/type-provider-json-schema-to-ts';
import {
  DomainError,
  HavenServiceAccountIdType as IdType,
  HID,
  HIDForEmailPlotAndSeaWare,
  Transaction,
  tx,
  ValidEmail,
} from 'haven-hid-domain';
import { FromSchema, JSONSchema } from 'json-schema-to-ts';
import { X_APP_ID_HEADER } from '../header.js';
import { validateBatch, validateEmail } from '../validation.js';
import { AccessErrorResponseSchema, ErrorResponseSchema, HeadersSchema } from '../schemas.js';
import { ValidationError } from '../../error.js';
import { getDependencies } from '../../dependencies/dependencies.js';

export const URI = '/hid/account/email-updates';
export const MAX_BATCH_SIZE: number = Number.parseInt(process.env.MAX_BATCH_SIZE || '', 10) || 1000;

const RequestSchema: JSONSchema = {
  $id: 'HIDEmailUpdates',
  description: `Email update notification for an Owner or Seaware account`,
  type: 'array',
  items: {
    type: 'object',
    required: ['email', 'id', 'idType', 'updatedAt'],
    properties: {
      email: { type: 'string' },
      idType: {
        type: 'string',
        enum: Object.values(IdType),
      },
      id: { type: 'number' },
      updatedAt: {
        type: 'string',
        format: 'date-time',
      },
    },
  },
} as const;

const SuccessResponseSchema = {
  type: 'object',
  $id: 'HIDForEmailSuccess',
  description: 'Return HID',
  properties: {
    success: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          email: { type: 'string' },
          hid: { type: 'string' },
        },
      },
    },
    failure: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          email: { type: 'string' },
          code: { type: 'string' },
          message: { type: 'string' },
        },
      },
    },
  },
  additionalProperties: false,
  example: {
    success: [
      {
        email: '<EMAIL>',
        hid: 'A123DF3FG7Z0',
      },
    ],
    failure: [
      {
        email: 'dave@examplecom',
        code: 'STALE_EMAIL_UPDATE',
        message: 'HID/EMAIL updated more recently',
      },
    ],
  },
} as const;

const HIDsForEmailsEndpoint = {
  schema: {
    body: RequestSchema,
    response: {
      200: SuccessResponseSchema,
      400: ErrorResponseSchema,
      403: AccessErrorResponseSchema,
    },
    headers: HeadersSchema,
  },
  config: {
    openapi: {
      description: `This notifies the HID service of "potential" updates to emails for registered users.  In most cases the HID services would have processed these changes already from the WEB, however if change was made via customer services (or any other means), this endpoint will update the email for the account and if necessary generate or move the HID.

A new HID will  only be generated if the plot account is new OR the HID the plot account is currently associated with is also linked to a Seaware client id.

If the email does not match the current email and the updatedAt timestamp is not after the HID service timestamp for the EMAIL_TO_HID relationship then the request will be rejected with an error STALE_EMAIL_UPDATE.
`,
      summary:
        'Batch endpoint to notify of Email updates for owner and seaware accounts from back office',
      tags: ['HID'],
      security: [{ appKeyId: [], appKeyAuth: [] }],
    },
    rateLimit: {
      max: 1,
      timeWindow: '1 second',
    },
    method: 'POST',
    url: URI,
  },
} as const;

export type EmailUpdate = {
  email: string;
  idType: IdType;
  id: number;
  updatedAt: string;
};

type HidSuccess = {
  email: string;
  hid: string;
};

type HidFailure = {
  email: string;
  code: string;
  message: string;
};

type BulkResponse = {
  success: HidSuccess[];
  failure: HidFailure[];
};

export const generateOrRetrieve = async (
  store: HIDForEmailPlotAndSeaWare,
  email: ValidEmail,
  updatedAt: Date,
  type: IdType,
  id: number,
  tx: Transaction,
): Promise<HID> => {
  switch (type) {
    case IdType.OWNER_ID:
      return store.getHIDForEmailAndPlot(email, updatedAt, id, tx);
    case IdType.SEAWARE_CLIENT_ID:
      return store.getHIDForEmailAndSeaWare(email, updatedAt, id, tx);
    default:
      throw new ValidationError(`body/idType unrecognised idType ${type as string}`);
  }
};

export const HIDsForEmailUpdates: FastifyPluginAsyncJsonSchemaToTs = (fastify) => {
  const server = fastify.withTypeProvider<JsonSchemaToTsProvider>();
  const { store } = getDependencies();

  for (const schema of [RequestSchema, SuccessResponseSchema, AccessErrorResponseSchema] as const) {
    server.addSchema(schema);
  }

  const onlyUnique = (value: EmailUpdate, index: number, self: EmailUpdate[]) =>
    index ===
    self.findIndex(
      (t) => t.email === value.email && t.id === value.id && t.idType === value.idType,
    );

  server.post<{
    Body: FromSchema<typeof RequestSchema>;
    attachValidation: true;
  }>(URI, HIDsForEmailsEndpoint, async (request, reply): Promise<void> => {
    const batch = request.body as EmailUpdate[];

    const emails = batch.filter(onlyUnique);

    validateBatch(batch);

    const source = request.headers[X_APP_ID_HEADER] as string;
    const transaction = tx(source);
    const result: BulkResponse = {
      success: [],
      failure: [],
    };

    const lookups = emails.map(async (update: EmailUpdate): Promise<void> => {
      try {
        const email = validateEmail(update.email);
        const hidResult = await generateOrRetrieve(
          store,
          email,
          new Date(update.updatedAt),
          update.idType,
          update.id,
          transaction,
        );
        if (hidResult) {
          result.success.push({
            email: update.email,
            hid: hidResult,
          });
        }
      } catch (err: unknown) {
        const { code, message } = err as ValidationError;
        const { reason } = err as DomainError;
        result.failure.push({
          email: update.email,
          code: code || 'ERROR',
          message: reason || message,
        });
      }
    });

    await Promise.allSettled(lookups);

    void reply.send(result);
  });
  return Promise.resolve();
};
