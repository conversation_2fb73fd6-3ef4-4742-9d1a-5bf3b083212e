import {
  FastifyPluginAsyncJsonSchemaToTs,
  JsonSchemaToTsProvider,
} from '@fastify/type-provider-json-schema-to-ts';
import { FromSchema, JSONSchema } from 'json-schema-to-ts';
import { X_APP_ID_HEADER } from '../header.js';
import { validateBatch, validateEmail } from '../validation.js';
import { AccessErrorResponseSchema, ErrorResponseSchema, HeadersSchema } from '../schemas.js';
import { ValidationError } from '../../error.js';
import { DomainError, HID, Transaction, tx, ValidEmail } from 'haven-hid-domain';
import { getDependencies } from '../../dependencies/dependencies.js';

export const URI = '/hid/emails';

const RequestSchema: JSONSchema = {
  $id: 'HIDsForEmails',
  description: 'Given a batch of emails return the assigned HIDs or generate and store new HIDS',
  type: 'array',
  items: {
    type: 'object',
    required: ['email'],
    properties: {
      email: {
        type: 'string',
      },
    },
  },
} as const;

const SuccessResponseSchema = {
  type: 'object',
  $id: 'HIDForEmailSuccess',
  description: 'Return HID',
  properties: {
    success: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          email: { type: 'string' },
          hid: { type: 'string' },
        },
      },
    },
    failure: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          email: { type: 'string' },
          code: { type: 'string' },
          message: { type: 'string' },
        },
      },
    },
  },
  additionalProperties: false,
  example: {
    success: [
      {
        email: '<EMAIL>',
        hid: 'A123DF3FG7Z0',
      },
    ],
    failure: [
      {
        email: 'dave@examplecom',
        code: 'INVALID_EMAIL',
        message: 'Invalid email',
      },
    ],
  },
} as const;

const HIDsForEmailsEndpoint = {
  schema: {
    body: RequestSchema,
    response: {
      200: SuccessResponseSchema,
      400: ErrorResponseSchema,
      403: AccessErrorResponseSchema,
    },
    headers: HeadersSchema,
  },
  config: {
    openapi: {
      description:
        'Generate or retrieve unique Haven Identity (HIDs) given a list of Emails. See repo for more [examples](https://github.com/HavenEngineering/haven-identity/tree/master/api/hid/examples).',
      summary: 'Batch endpoint to get HIDs for Emails',
      tags: ['HID'],
      security: [{ appKeyId: [], appKeyAuth: [] }],
    },
    rateLimit: {
      max: 1,
      timeWindow: '1 second',
    },
    method: 'POST',
    url: URI,
  },
} as const;

type Email = {
  email: string;
};

type HidSuccess = {
  email: string;
  hid: string;
};

type HidFailure = {
  email: string;
  code: string;
  message?: string;
};

type BulkResponse = {
  success: HidSuccess[];
  failure: HidFailure[];
};

export const HIDsForEmails: FastifyPluginAsyncJsonSchemaToTs = (fastify) => {
  const server = fastify.withTypeProvider<JsonSchemaToTsProvider>();
  const { store } = getDependencies();

  const generateOrRetrieve = async (email: ValidEmail, tx: Transaction): Promise<HID> => {
    return await store.getHIDForEmail(email, undefined, tx);
  };

  for (const schema of [RequestSchema, SuccessResponseSchema, AccessErrorResponseSchema] as const) {
    server.addSchema(schema);
  }

  const onlyUnique = (value: string, index: number, array: string[]) =>
    array.indexOf(value) === index;

  server.post<{
    Body: FromSchema<typeof RequestSchema>;
    attachValidation: true;
  }>(URI, HIDsForEmailsEndpoint, async (request, reply): Promise<void> => {
    const batch = request.body as Email[];
    const emails = batch.map((e) => e.email).filter(onlyUnique);

    validateBatch(emails);

    const source = request.headers[X_APP_ID_HEADER] as string;
    const transaction = tx(source);
    const result: BulkResponse = {
      success: [],
      failure: [],
    };

    const lookups = emails.map(async (email): Promise<void> => {
      try {
        const hid = await generateOrRetrieve(validateEmail(email), transaction);
        result.success.push({
          email,
          hid: hid,
        });
      } catch (err) {
        const { code, message } = err as ValidationError;
        const { reason } = err as DomainError;

        result.failure.push({
          email,
          code: code || 'ERROR',
          message: reason || message,
        });
      }
    });

    for (const lookup of lookups) {
      await lookup;
    }

    void reply.send(result);
  });

  return Promise.resolve();
};
