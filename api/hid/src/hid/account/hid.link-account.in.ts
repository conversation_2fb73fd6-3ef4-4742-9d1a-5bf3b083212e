import {
  FastifyPluginAsyncJsonSchemaToTs,
  JsonSchemaToTsProvider,
} from '@fastify/type-provider-json-schema-to-ts';
import { FromSchema } from 'json-schema-to-ts';

import {
  AccessErrorResponseSchema,
  ErrorResponseSchema,
  registerSchemas,
  UserManagementHeadersSchema,
} from '../schemas.js';
import { HavenServiceAccountIdType as IdType, tx } from 'haven-hid-domain';
import { HID_VALIDATION_PATTERN } from '../validation.js';
import { ValidationError } from '../../error.js';
import { X_APP_ID_HEADER } from '../header.js';
import { getDependencies } from '../../dependencies/dependencies.js';

export const URI = '/hid/account';

const RequestBodySchema = {
  type: 'object',
  $id: 'linkAccount',
  description: `Link service id to HID.`,
  properties: {
    hid: {
      type: 'string',
      pattern: HID_VALIDATION_PATTERN,
    },
    id: { type: 'number' },
    idType: {
      type: 'string',
      enum: Object.values(IdType),
    },
  },
  required: ['hid', 'id', 'idType'],
  additionalProperties: false,
} as const;

const SuccessResponseSchema = {
  type: 'object',
  $id: 'linkAccountSuccess',
  description: 'Account id has been linked to HID',
  properties: {
    hid: {
      type: 'string',
      pattern: HID_VALIDATION_PATTERN,
    },
  },
  required: ['hid'],
  additionalProperties: false,
  example: {
    hid: 'A1B2C2DDDDDD',
  },
} as const;

const linkAccountConfig = {
  schema: {
    body: RequestBodySchema,
    response: {
      200: SuccessResponseSchema,
      400: ErrorResponseSchema,
      403: AccessErrorResponseSchema,
    },
    headers: UserManagementHeadersSchema,
  },
  config: {
    openapi: {
      description: `Link service id to HID.`,
      summary: 'Link Account',
      tags: ['HID'],
      security: [{ appKeyId: [], appKeyAuth: [] }],
    },
    rateLimit: {},
    method: 'POST',
    url: URI,
  },
} as const;

const schemas = [RequestBodySchema, SuccessResponseSchema, AccessErrorResponseSchema];

export const linkAccount: FastifyPluginAsyncJsonSchemaToTs = (fastify) => {
  const server = fastify.withTypeProvider<JsonSchemaToTsProvider>();
  registerSchemas(schemas, fastify);
  const { store } = getDependencies();

  server.post<{
    Body: FromSchema<typeof RequestBodySchema>;
    attachValidation: true;
  }>(URI, linkAccountConfig, async (req, res): Promise<void> => {
    const { hid, idType, id } = req.body;
    const source = req.headers[X_APP_ID_HEADER] as string;
    const transaction = tx(source);

    try {
      switch (idType) {
        case IdType.OWNER_ID:
          await store.linkHIDToPlot(hid, id, transaction);
          break;
        case IdType.SEAWARE_CLIENT_ID:
          await store.linkHIDToSeaware(hid, id, transaction);
          break;
        default:
          throw new ValidationError(`body/idType unrecognised idType ${idType as string}`);
      }
      return res.send({ hid });
    } catch (e: unknown) {
      req.log.error(`Error linking account for ${idType} ${id}`, e);
      throw e;
    }
  });

  return Promise.resolve();
};
