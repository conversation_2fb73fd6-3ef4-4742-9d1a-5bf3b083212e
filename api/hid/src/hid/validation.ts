import { ValidationError } from '../error.js';
import {
  HID,
  ValidEmail,
  HID_LENGTH,
  HavenServiceAccountIdType as IdType,
  isValidPhoneNumber,
} from 'haven-hid-domain';
import { checkPassword, PasswordStrength } from '@havenengineering/module-shared-password-strength';
import { logger } from '@havenengineering/module-haven-logging';

export const HID_VALIDATION_PATTERN = `[A-Z0-9]{${HID_LENGTH}}`;
export const MAX_BATCH_SIZE: number = Number.parseInt(process.env.MAX_BATCH_SIZE || '', 10) || 1000;

export const validateId = (idType: IdType | undefined, id: number | undefined) => {
  if (idType && !id) {
    throw new ValidationError(`Missing id for idType="${idType}"`);
  }
  if (id && !idType) {
    throw new ValidationError(`Missing idType for id="${id}"`);
  }
};

export const validateEmail = (email: string): ValidEmail => {
  const valid = ValidEmail.new(email);
  if (valid instanceof ValidEmail) {
    return valid;
  }
  logger.warn('Email validation failed', {
    email: email,
  });
  throw new ValidationError('body/email is not a valid email');
};

export const validateHID = (hid: string): HID => {
  if (!hid) throw new ValidationError('HID validation failed');
  return hid;
};

export const validateBatch = (updates: unknown[]) => {
  if (updates.length > MAX_BATCH_SIZE) {
    throw new ValidationError(`Maximum batch size (${MAX_BATCH_SIZE}) exceeded`);
  }
};

/**
 *
 * @throws {ValidationError}
 */
export const validatePassword = (password: string): PasswordStrength => {
  const { feedback, strength, pass } = checkPassword(password);

  if (!pass) {
    throw new ValidationError('body/password is too weak', {
      strength: strength,
      warning: feedback.warning || '',
      suggestions: feedback.suggestions || [],
    });
  }

  return strength;
};

export const validatePhoneNumber = (phoneNumber: string): string | never => {
  if (isValidPhoneNumber(phoneNumber)) return phoneNumber;
  throw new ValidationError('body/phoneNumber is not valid');
};
