import { FastifyInstance } from 'fastify/types/instance.js';
import { X_APP_ID_HEADER, X_APP_KEY_HEADER } from './header.js';

export const registerSchemas = (schemas: object[], server: FastifyInstance) =>
  schemas.forEach((schema) => server.addSchema(schema));

export const AccessErrorResponseSchema = {
  type: 'object',
  $id: 'AccessError',
  description: 'Access error response',
  properties: {
    code: { type: 'string', enum: ['FORBIDDEN'] },
    message: { type: 'string' },
    statusCode: { type: 'integer', enum: [403] },
  },
  example: {
    code: 'FORBIDDEN',
    message: 'api key is missing',
    statusCode: 403,
  },
} as const;

export const HeadersSchema = {
  type: 'object',
  properties: {
    'X-APP-ID': {
      type: 'string',
      pattern: '[a-zA-Z-]+',
      description: 'Application identifier',
    },
    'X-APP-KEY': {
      type: 'string',
      pattern: '[a-zA-Z-]+',
      description: 'Application Key',
    },
  },
  required: [X_APP_ID_HEADER, X_APP_KEY_HEADER],
} as const;

export const UserManagementHeadersSchema = {
  type: 'object',
  properties: {
    'X-APP-ID': {
      type: 'string',
      pattern: '[a-zA-Z-]+',
      description: 'Application identifier',
    },
    'X-APP-KEY': {
      type: 'string',
      pattern: '[a-zA-Z-]+',
      description: 'Application Key',
    },
  },
  required: [X_APP_ID_HEADER, X_APP_KEY_HEADER],
} as const;

export const ErrorResponseSchema = {
  type: 'object',
  $id: 'GenericError',
  description: 'Some error description',
  properties: {
    code: { type: 'string' },
    message: { type: 'string' },
    statusCode: { type: 'number' },
  },
  example: {
    code: 'ERROR_CODE',
    message: 'Something wrong with your request',
    statusCode: 400,
  },
} as const;

export const ErrorResponse500Schema = {
  type: 'object',
  $id: 'GenericError',
  description: 'Some error description',
  properties: {
    code: { type: 'string' },
    message: { type: 'string' },
    details: { errorMessages: { type: 'array', items: 'string' } },
    statusCode: { type: 'number' },
  },
  example: {
    code: 'ERROR_CODE',
    message: 'Something wrong with your request',
    details: { errorMessages: ['error message'] },
    statusCode: 500,
  },
} as const;
