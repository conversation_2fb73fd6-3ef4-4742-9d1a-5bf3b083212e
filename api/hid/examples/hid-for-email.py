#!/usr/bin/env python3

from urllib import request, parse
import pprint
import json

pp = pprint.PrettyPrinter(indent=4)
serviceUrl = 'https://service-haven-identity-hid.dev.haven-leisure.com/hid/email'

data = { 'email': '<EMAIL>' }

data = json.dumps(data)
data = str(data)
data = data.encode('utf-8')

getHID = request.Request(serviceUrl, data=data)
getHID.add_header('Content-Type', 'application/json')
getHID.add_header('X-APP-ID', 'python-client')

response = request.urlopen(getHID)
jsonResponse = json.loads(response.read())

pp.pprint(jsonResponse)
