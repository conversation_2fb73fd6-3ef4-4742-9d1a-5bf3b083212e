#!/bin/bash

URL=${1:-"https://service-haven-identity-hid.dev.haven-leisure.com"}

FORMAT_COMMAND=tee
which jq >/dev/null 2>&1
if [ $? -eq 0 ]
then
  FORMAT_COMMAND="jq ."
fi

curl -s \
    -H "Content-Type: application/json" \
    -H 'X-APP-ID: ohai' \
    --data '[{ "email": "<EMAIL>" }, { "email": "<EMAIL>" }, { "email": "<EMAIL>" }, { "email": "invalid-email" }]' \
    "$URL/hid/emails" | $FORMAT_COMMAND
