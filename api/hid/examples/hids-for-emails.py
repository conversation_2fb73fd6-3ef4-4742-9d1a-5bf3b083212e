#!/usr/bin/env python3

from urllib import request, parse
import json
import pprint

pp = pprint.PrettyPrinter(indent=4)
serviceUrl = 'https://service-haven-identity-hid.dev.haven-leisure.com/hid/emails'

data = [
    { 'email': '<EMAIL>' },
    { 'email': '<EMAIL>' },
    { 'email': '<EMAIL>' },
    { 'email': 'invalid-email' }
]

data = json.dumps(data)
data = str(data)
data = data.encode('utf-8')

getHIDs = request.Request(serviceUrl, data=data)
getHIDs.add_header('Content-Type', 'application/json')
getHIDs.add_header('X-APP-ID', 'python-client')

response = request.urlopen(getHIDs)
jsonResponse = json.loads(response.read())

pp.pprint(jsonResponse)
