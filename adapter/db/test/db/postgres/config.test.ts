import casual from 'casual';
import { isMacOS } from 'haven-identity-docker-util';
import { afterEach, describe, expect, it, vi } from 'vitest';
import {
  DEFAULT_ADMIN_DATABASE,
  DEFAULT_ADMIN_PASSWORD,
  DEFAULT_ADMIN_USER,
  DEFAULT_POSTGRES_CONTAINERNAME,
  DEFAULT_POSTGRES_IMAGE,
  DEFAULT_POSTGRES_PORT,
  ENV_KEYS,
  defaultPostgresConfig,
} from '../../../src/db/postgres/config.js';
import { nextDatabase } from '../db.test.js';

describe('postgres configuration scenarios', () => {
  afterEach(() => {
    ENV_KEYS.forEach((key) => {
      delete process.env[key];
    });
  });

  it('has reasonable defaults', () => {
    const actual = defaultPostgresConfig([nextDatabase()]);

    expect(actual.adminDatabase.password).toEqual(DEFAULT_ADMIN_PASSWORD);
    expect(actual.containerName).toEqual(DEFAULT_POSTGRES_CONTAINERNAME);
    expect(actual.imageName).toEqual(DEFAULT_POSTGRES_IMAGE);
  });

  it('can override container name using env', () => {
    const expected = casual.word;
    process.env.POSTGRES_CONTAINERNAME = expected;

    const actual = defaultPostgresConfig([nextDatabase()]);

    expect(actual.containerName).toEqual(expected);
  });

  it('can override image using env', () => {
    const expected = casual.word;
    process.env.POSTGRES_IMAGE = expected;

    const actual = defaultPostgresConfig([nextDatabase()]);

    expect(actual.imageName).toEqual(expected);
  });

  it('can override admin password using env', () => {
    const expected = casual.word;
    process.env.POSTGRES_PASSWORD = expected;

    const actual = defaultPostgresConfig([nextDatabase()]);

    expect(actual.adminDatabase.password).toEqual(expected);
  });
});

describe('specific database env scenarios', () => {
  afterEach(() => {
    ENV_KEYS.forEach((key) => {
      delete process.env[key];
    });
  });

  vi.mock('haven-identity-docker-util', async (original) => {
    const mod: Object = await original();
    return {
      ...mod,
      isMacOS: vi.fn(),
    };
  });

  it('can generate the env for a specific database on linux', async () => {
    const ip = casual.ip;
    const database = nextDatabase();
    vi.mocked(isMacOS).mockReturnValue(false);

    const actual = defaultPostgresConfig([database]);

    expect(actual.env(database.name, ip)).toMatchObject({
      DATABASE_URL: `postgres://${database.user}:${database.password}@${ip}:${DEFAULT_POSTGRES_PORT}/${database.name}`,
      DATABASE_TEST_URL: `postgres://${database.user}:${database.password}@${ip}:${DEFAULT_POSTGRES_PORT}/${database.name}_test`,
      OIDC_DATABASE_URL: `postgres://${database.user}:${database.password}@${ip}:${DEFAULT_POSTGRES_PORT}/${database.name}?schema=oidc`,
    });
  });

  it('can generate envs for multiple databases', async () => {
    const ip = casual.ip;
    const [database1, database2] = [nextDatabase(), nextDatabase()];
    vi.mocked(isMacOS).mockReturnValue(false);

    const actual = defaultPostgresConfig([database1, database2]);

    expect(actual.env(database1.name, ip)).toBeDefined();
    expect(actual.env(database2.name, ip)).toBeDefined();
  });

  it('can generate server admin env on linux', async () => {
    const ip = casual.ip;
    const database = nextDatabase();
    vi.mocked(isMacOS).mockReturnValue(false);

    const actual = defaultPostgresConfig([database]);

    expect(actual.adminEnv(ip)).toMatchObject({
      DATABASE_URL: `postgres://${DEFAULT_ADMIN_USER}:${DEFAULT_ADMIN_PASSWORD}@${ip}:${DEFAULT_POSTGRES_PORT}/${DEFAULT_ADMIN_DATABASE}`,
    });
  });
});
