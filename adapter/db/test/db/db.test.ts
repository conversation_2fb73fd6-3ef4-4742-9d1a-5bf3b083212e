import casual from 'casual';
import { I<PERSON>ontainer, findOrCreateAndWaitForContainers, isMacOS } from 'haven-identity-docker-util';
import { okAsync } from 'neverthrow';
import { describe, expect, it, vi } from 'vitest';
import { Database, start } from '../../src/db/db.js';
import { DEFAULT_POSTGRES_IMAGE, LOCALHOST } from '../../src/db/postgres/config.js';

describe('database scenarios', () => {
  vi.mock('haven-identity-docker-util', async (original) => {
    const mod: Object = await original();
    return {
      ...mod,
      isMacOS: vi.fn(),
      findOrCreateAndWaitForContainers: vi.fn(),
      setUpDotEnv: vi.fn(() => {
        return okAsync(undefined);
      }),
    };
  });

  it('new postgresql container uses default docker image', async () => {
    const container = {
      ip: casual.ip,
    } as IContainer;
    vi.mocked(findOrCreateAndWaitForContainers).mockReturnValue(okAsync([container]));

    await start([nextDatabase()]);

    const config = vi.mocked(findOrCreateAndWaitForContainers).mock.calls[0][0][0];
    expect(config.image.name).toEqual(DEFAULT_POSTGRES_IMAGE);
  });

  it('new postgresql container on linux uses container ip address', async () => {
    vi.mocked(isMacOS).mockReturnValue(false);

    const container = {
      ip: casual.ip,
    } as IContainer;
    vi.mocked(findOrCreateAndWaitForContainers).mockReturnValue(okAsync([container]));

    const postgresql = (await start([nextDatabase()]))._unsafeUnwrap();

    expect(postgresql.ip()).toEqual(container.ip);
  });

  it('new postgresql container on macos uses localhost ip address', async () => {
    vi.mocked(isMacOS).mockReturnValue(true);

    const container = {
      ip: casual.ip,
    } as IContainer;
    vi.mocked(findOrCreateAndWaitForContainers).mockReturnValue(okAsync([container]));

    const postgresql = (await start([nextDatabase()]))._unsafeUnwrap();

    expect(postgresql.ip()).toEqual(LOCALHOST);
  });

  it('returns error if no database configurations are provided', async () => {
    const actual = await start([]);
    expect(actual.isErr()).toBeTruthy();
  });

  it('returns empty env for unknown database', async () => {
    const database = nextDatabase();

    const container = {
      ip: casual.ip,
    } as IContainer;
    vi.mocked(findOrCreateAndWaitForContainers).mockReturnValue(okAsync([container]));

    const postgresql = (await start([database]))._unsafeUnwrap();
    const actual = postgresql.env(casual.word);

    expect(actual).toMatchObject({});
  });
});

export const nextDatabase = (): Database => {
  return {
    name: casual.word,
    user: casual.username.toLocaleLowerCase(),
    password: casual.password,
  };
};
