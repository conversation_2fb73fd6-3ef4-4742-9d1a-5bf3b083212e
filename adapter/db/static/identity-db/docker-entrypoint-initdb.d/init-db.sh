#!/bin/bash
set -e

psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
	CREATE USER hid WITH PASSWORD 'hid';
	CREATE DATABASE identity;
    CREATE DATABASE identity_test;
	GRANT ALL PRIVILEGES ON DATABASE identity TO hid;
	ALTER DATABASE identity OWNER TO hid;
    GRANT ALL PRIVILEGES ON DATABASE identity_test TO hid;
	ALTER DATABASE identity_test OWNER TO hid;
	ALTER USER hid CREATEDB;

  CREATE USER e2e WITH PASSWORD 'e2e';
	CREATE DATABASE e2e;
    CREATE DATABASE e2e_test;
	GRANT ALL PRIVILEGES ON DATABASE e2e TO e2e;
	ALTER DATABASE e2e OWNER TO e2e;
    GRANT ALL PRIVILEGES ON DATABASE e2e_test TO e2e;
	ALTER DATABASE e2e_test OWNER TO e2e;
	ALTER USER e2e CREATEDB;
EOSQL
