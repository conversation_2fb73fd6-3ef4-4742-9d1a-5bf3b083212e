{"name": "haven-identity-database", "version": "0.0.1", "description": "Haven Idenityt PostgreSQL Database", "type": "module", "types": "./dist/exports.d.ts", "exports": {".": "./dist/exports.js"}, "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "build": "tsc", "test": "vitest run --reporter verbose --reporter json --outputFile dist/vitest.json --pool forks --poolOptions.forks.singleFork", "test:unit": "pnpm test", "start": "pnpm build && TESTCONTAINERS_RYUK_DISABLED=true pnpm task --start", "migrate": "pnpm build && TESTCONTAINERS_RYUK_DISABLED=true pnpm task --migrate", "populate": "pnpm build && SIZE=1000000 pnpm task --generate && DELETE_BEFORE_LOAD=true pnpm task --load", "stop": "pnpm task --stop", "task": "node dist/task/main.js", "restart": "TESTCONTAINERS_RYUK_DISABLED=true pnpm task --restart", "full": "pnpm restart && pnpm test"}, "author": "Haven Engineering", "license": "ISC", "dependencies": {"@havenengineering/module-haven-logging": "^0.5.1", "dotenv": "^16.4.5", "env-var": "^7.5.0", "find-config": "^1.0.0", "haven-identity-docker-util": "workspace:*", "humanize-duration": "^3.32.1", "neverthrow": "^6.2.1", "pg": "^8.11.5", "pg-copy-streams": "^6.0.6", "rake-db": "^2.20.14", "ts-opt": "^5.1.1"}, "devDependencies": {"@tsconfig/node20": "^20.1.4", "@types/find-config": "^1.0.4", "@types/humanize-duration": "^3.27.4", "@types/node": "^20.12.13", "@types/pg": "^8.11.6", "casual": "^1.6.2", "mkdirp": "^3.0.1", "rimraf": "^5.0.7", "typescript": "^5.4.5", "vitest": "^3.0.8"}, "engines": {"npm": "use-pnpm", "node": ">=20"}}