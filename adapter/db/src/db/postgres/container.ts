import { IContainer, isMacOS } from 'haven-identity-docker-util';
import { LOCALHOST, PostgreSQLConfig } from './config.js';

export class PostgreSQLContainer {
  constructor(
    public readonly config: PostgreSQLConfig,
    public readonly container: IContainer,
  ) {}

  public env(name: string, useLocalhostForMac: boolean = true): NodeJS.Dict<string> {
    return this.config.env(name, this.ip(useLocalhostForMac));
  }

  public ip(useLocalhostForMac: boolean = true): string {
    return isMacOS() && useLocalhostForMac ? LOCALHOST : this.container.ip;
  }
}
