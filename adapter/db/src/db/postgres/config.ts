import { ContainerConfig, ImageConfig, isMac<PERSON>, projectRoot } from 'haven-identity-docker-util';
import { Opt, opt } from 'ts-opt';
import { Database } from '../db.js';

export const DEFAULT_ADMIN_DATABASE: string = 'postgres';
export const DEFAULT_ADMIN_PASSWORD: string = 'identity-db-admin';
export const DEFAULT_ADMIN_USER: string = 'postgres';
export const DEFAULT_POSTGRES_PORT = 5432;
export const DEFAULT_POSTGRES_IMAGE = 'postgres:14';
export const DEFAULT_POSTGRES_CONTAINERNAME = 'identity-db';
export const ENV_KEYS: string[] = [
  'POSTGRES_ADMIN_DATABASE',
  'POSTGRES_ADMIN_USER',
  'POSTGRES_CONTAINERNAME',
  'POSTGRES_IMAGE',
  'POSTGRES_PASSWORD',
];
export const LOCALHOST: string = '127.0.0.1';

export class PostgreSQLConfig {
  public readonly adminDatabase: Database;
  public readonly containerName: string;
  public readonly containerConfig: ContainerConfig;
  public readonly imageName: string;

  constructor(public readonly databases: Database[]) {
    this.containerName = resolveValue(
      process.env.POSTGRES_CONTAINERNAME,
      DEFAULT_POSTGRES_CONTAINERNAME,
    );
    this.imageName = resolveValue(process.env.POSTGRES_IMAGE, DEFAULT_POSTGRES_IMAGE);

    this.adminDatabase = {
      name: resolveValue(process.env.POSTGRES_ADMIN_DATABASE, DEFAULT_ADMIN_DATABASE),
      user: resolveValue(process.env.POSTGRES_ADMIN_USER, DEFAULT_ADMIN_USER),
      password: resolveValue(process.env.POSTGRES_PASSWORD, DEFAULT_ADMIN_PASSWORD),
    };

    const config = ContainerConfig.new(this.containerName, ImageConfig.use(this.imageName))
      .withEnv('POSTGRES_PASSWORD', this.adminDatabase.password)
      .withBindMount({
        source: `${projectRoot()}/adapter/db/static/identity-db/docker-entrypoint-initdb.d`,
        target: '/docker-entrypoint-initdb.d',
      });
    this.containerConfig = addHostPortIfMacOS(config);
  }

  public adminEnv(ip: string): NodeJS.Dict<string> {
    return {
      DATABASE_URL: toConnectionString(this.adminDatabase, ip),
    };
  }

  public env(databaseName: string, ip: string): NodeJS.Dict<string> {
    const result: NodeJS.Dict<string> = {};

    this.findDatabase(databaseName).map((db) => {
      result['DATABASE_URL'] = toConnectionString(db, ip);
      result['DATABASE_TEST_URL'] = `${toConnectionString(db, ip)}_test`;
      result['OIDC_DATABASE_URL'] = `${toConnectionString(db, ip)}?schema=oidc`;
    });

    return result;
  }

  private findDatabase(name: string): Opt<Database> {
    return opt(
      this.databases.find((item) => {
        return item.name === name;
      }),
    );
  }
}

export const defaultPostgresConfig = (databases: Database[]): PostgreSQLConfig => {
  return new PostgreSQLConfig(databases);
};

const addHostPortIfMacOS = (input: ContainerConfig): ContainerConfig => {
  const containerPort = resolvePort(process.env.POSTGRES_PORT, DEFAULT_POSTGRES_PORT);

  if (isMacOS()) {
    const host = resolvePort(process.env.HOST_POSTGRES_PORT, DEFAULT_POSTGRES_PORT);
    input = input.withExposedPort({
      host,
      container: containerPort,
    });
  } else {
    input.withExposedPort(containerPort);
  }

  return input;
};

const toConnectionString = (db: Database, ip: string): string => {
  return `postgres://${db.user}:${db.password}@${ip}:${DEFAULT_POSTGRES_PORT}/${db.name}`;
};

const resolveValue = (input: string | undefined, fallback: string): string => {
  return input ? input : fallback;
};

const resolvePort = (input: string | undefined, fallback: number): number => {
  return input !== undefined ? Number.parseInt(input) : fallback;
};
