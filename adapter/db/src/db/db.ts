import {
  NoR<PERSON>ult,
  findOrCreateAndWaitForContainers,
  setUpDotEnv,
  setUpOidcAdapterDotEnv,
  stopContainersByName,
} from 'haven-identity-docker-util';
import { ResultAsync, errAsync } from 'neverthrow';
import { defaultPostgresConfig } from './postgres/config.js';
import { PostgreSQLContainer } from './postgres/container.js';

export type Database = {
  name: string;
  user: string;
  password: string;
};

export type StartOptions = {
  dotenv?: string;
  useLocalhostForMac?: boolean;
  setupPrismaOidcEnv?: boolean;
};

export const start = (
  input: Database[],
  options?: StartOptions,
): ResultAsync<PostgreSQLContainer, Error> => {
  if (input.length > 0) {
    const config = defaultPostgresConfig(input);
    return findOrCreateAndWaitForContainers([config.containerConfig])
      .map((containers) => {
        return new PostgreSQLContainer(config, containers[0]);
      })
      .andThen((postgresql) => {
        const data = postgresql.env(input[0].name, options?.useLocalhostForMac);
        if (options?.setupPrismaOidcEnv) {
          setUpOidcAdapterDotEnv(data).map(() => postgresql);
        }

        return setUpDotEnv(data, options?.dotenv).map(() => postgresql);
      });
  } else {
    return errAsync(new Error('POSTGRESQL | No database configurations provided'));
  }
};

export const stop = (): ResultAsync<NoResult, Error> => {
  const config = defaultPostgresConfig([]);
  return stopContainersByName([config.containerName]);
};
