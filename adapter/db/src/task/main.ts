import { logger } from '@havenengineering/module-haven-logging';
import { start, stop } from 'haven-identity-database';
import humanizeDuration from 'humanize-duration';
import { ResultAsync, okAsync } from 'neverthrow';

export type NoResult = void;

export enum Mode {
  START = 'START',
  RESTART = 'RESTART',
  STOP = 'STOP',
}

export const toError = (e: unknown): Error => {
  logger.error(e as Error);
  return new Error('Unexpected Error', {
    cause: e,
  });
};

const handleError = (error: Error) => {
  logger.error(error.message);
  process.exit(1);
};

const noopTask = (): ResultAsync<undefined, Error> => {
  return okAsync(undefined);
};

const stopTask = (): ResultAsync<void, Error> => {
  return stop();
};

const startTask = (): ResultAsync<unknown, Error> => {
  return start([
    {
      user: 'hid',
      password: 'hid',
      name: 'identity',
    },
  ]);
};

const runTask = (): ResultAsync<unknown, Error> => {
  switch (mode()) {
    case Mode.STOP:
      return stopTask();
    case Mode.START:
      return startTask();
    case Mode.RESTART:
      return noopTask();
  }
};

const mode = (): Mode => {
  let mode: keyof typeof Mode;
  for (mode in Mode) {
    if (process.argv.includes(`--${mode.toLocaleLowerCase()}`)) {
      return Mode[mode];
    }
  }
  return Mode.START;
};

export const main = async (): Promise<void> => {
  const start = new Date().getTime();
  logger.info('POSTGRESQL | Load configuration');

  await runTask()
    .map(() => {
      const end = new Date().getTime();
      const duration = end - start;
      logger.info(`POSTGRESQL | ${mode()} Complete in ${humanizeDuration(duration)}`);
      process.exit(0);
    })
    .mapErr(handleError)
    .unwrapOr(undefined);
};

await main();
