import * as dotenv from 'dotenv';
import findConfig from 'find-config';
import * as pgsql from 'pg';

export class Database {
  private static _instance: Database;
  private hasBeenReset: boolean = false;
  public readonly pool: pgsql.Pool;
  private constructor() {
    const path: string = findConfig('.env') || '.env';
    dotenv.config({ path });

    const { Pool } = require('pg');

    this.pool = new Pool({
      connectionString: `${process.env.DATABASE_TEST_URL}?schema=oidc`,
      schema: 'oidc',
      ssl: false,
      max: 20,
      idleTimeoutMillis: 1000,
      connectionTimeoutMillis: 1000,
    });
  }

  public async reset() {
    if (!this.hasBeenReset) {
      this.hasBeenReset = true;
      await this.pool.query('DELETE FROM "oidc"."grants";');
      await this.pool.query('DELETE FROM "oidc"."client";');
      await this.pool.query('DELETE FROM "oidc"."client_credentials";');
      await this.pool.query('DELETE FROM "oidc"."device_code";');
      await this.pool.query('DELETE FROM "oidc"."access_token";');
      await this.pool.query('DELETE FROM "oidc"."authorization_code";');
      await this.pool.query('DELETE FROM "oidc"."refresh_token";');
      await this.pool.query('DELETE FROM "oidc"."registration_access_token";');
      await this.pool.query('DELETE FROM "oidc"."backchannel_authentication_request";');
      await this.pool.query('DELETE FROM "oidc"."initial_access_token";');
      await this.pool.query('DELETE FROM "oidc"."interaction";');
      await this.pool.query('DELETE FROM "oidc"."replay_detection";');
      await this.pool.query('DELETE FROM "oidc"."pushed_authorization_request";');
      await this.pool.query('DELETE FROM "oidc"."session";');
    }
  }

  public static get Instance() {
    return this._instance || (this._instance = new this());
  }
}

export const getDatabase = async (): Promise<pgsql.Pool> => {
  return Database.Instance.pool;
};

export const resetDatabase = async (): Promise<void> => {
  await Database.Instance.reset();
};
