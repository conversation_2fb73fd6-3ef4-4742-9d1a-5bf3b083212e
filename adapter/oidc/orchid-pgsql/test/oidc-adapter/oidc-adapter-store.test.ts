import { describe, expect, it } from 'vitest';
import { fail } from 'assert';
import { PgOidcAdapter as OidcAdapterStore } from '../../src/oidc-adapter/oidc-adapter-store.js';
import { nextGrantId, nextId, nextUid, nextUserCode } from '../setup/helper.js';
import util from 'util';
import { obfuscateError } from 'haven-hid-domain';

describe('adapter store', () => {
  describe.each([
    ['Session'],
    ['Client'],
    ['ClientCredentials'],
    ['DeviceCode'],
    ['AccessToken'],
    ['AuthorizationCode'],
    ['RefreshToken'],
    ['RegistrationAccessToken'],
    ['BackchannelAuthenticationRequest'],
    ['InitialAccessToken'],
    ['Interaction'],
    ['ReplayDetection'],
    ['PushedAuthorizationRequest'],
    ['Grant'],
  ])(`%s`, async (type) => {
    const store = new OidcAdapterStore(type);
    const id = nextId();
    const data = {
      uid: nextUid(),
      some: 'value',
      userCode: nextUserCode(),
      grantId: nextGrantId(),
    };

    it('can insert', async () => {
      await store.upsert(id, data, Date.now());
      const found = await store.find(id);
      expect(found).toBeTruthy();
    });

    it('can read existing', async () => {
      const found = await store.find(id);
      expect(found).toEqual(data);
    });

    it('can update existing', async () => {
      const updated = { ...data, updated: true };

      await store.upsert(id, updated, Date.now());

      expect(await store.find(id)).toEqual(updated);
    });

    it('can destroy', async () => {
      await store.destroy(id);
      expect(await store.find(id)).toBeUndefined();
    });

    it('returns undefined when not found by id', async () => {
      const id = nextId();
      const found = await store.find(id);
      expect(found).toBeUndefined();
    });
  });

  describe('tests for consuming', () => {
    it('returns consumed when instance has been consumed', async () => {
      const store = new OidcAdapterStore('Interaction');
      const id = nextId();
      const data = {
        uid: nextUid(),
        some: 'value',
      };
      await store.upsert(id, data, Date.now());

      const found = await store.find(id);
      expect(found.consumed).toBeUndefined();

      await store.consume(id);

      const consumed = await store.find(id);
      expect(consumed).toEqual({
        ...data,
        consumed: true,
      });
    });
  });

  describe('tests for usercode', () => {
    it('you can find by usercode', async () => {
      const id = nextId();
      const store = new OidcAdapterStore('DeviceCode');
      const data = {
        grantId: nextGrantId(),
        userCode: nextUserCode(),
        some: 'value',
      };
      await store.upsert(id, data, Date.now());
      const found = await store.findByUserCode(data.userCode);
      expect(found).toEqual(data);
    });

    it('returns undefined when not found by usercode', async () => {
      const store = new OidcAdapterStore('DeviceCode');
      const id = nextId();

      const found = await store.findByUserCode(id);
      expect(found).toBeUndefined();
    });
  });

  describe('tests for uid', () => {
    it('you can find by uid', async () => {
      const id = nextId();
      const store = new OidcAdapterStore('Session');
      const data = {
        uid: nextUid(),
        some: 'value',
      };
      await store.upsert(id, data, Date.now());
      const found = await store.findByUid(data.uid);
      expect(found).toEqual(data);
    });

    it('returns undefined when not found by uid', async () => {
      const store = new OidcAdapterStore('Session');
      const id = nextId();

      const found = await store.findByUid(id);
      expect(found).toBeUndefined();
    });
  });

  describe('tests for grantables', () => {
    it.each([
      ['DeviceCode'],
      ['AccessToken'],
      ['AuthorizationCode'],
      ['RefreshToken'],
      ['BackchannelAuthenticationRequest'],
    ])('grants can be revoked for %s', async (type) => {
      const id = nextId();
      const store = new OidcAdapterStore(type);
      const data = {
        grantId: nextGrantId(),
        uid: nextUid(),
        userCode: nextUserCode(),
        some: 'value',
      };
      await store.upsert(id, data, Date.now());
      const found = await store.find(id);
      expect(found).toBeTruthy();

      await store.revokeByGrantId(data.grantId);

      expect(await store.find(id)).toBeUndefined();
    });
  });
});

describe('tests for error log processing', () => {
  const store = new OidcAdapterStore('Session');
  const id: string = ''; // deliberate bad id to provoke DB error
  const data = {
    uid: '',
    some: 'value',
    userCode: nextUserCode(),
    grantId: nextGrantId(),
  };

  it('should remove connection string user and password from error log', async () => {
    try {
      await store.upsert(id, data, Date.now());
      fail('invalid upsert should throw');
    } catch (error) {
      const logged: string = util.inspect(error);

      // It's a philosophical conundrum. How to test for absence of username and password
      // without mentioning them in this test?
      // The chosen solution is to read the db connection string form the environment,
      // snip out the username:password section, and the check to see that this does
      // not appear in the logged text.
      // Note that we cannot check for the username and password individually, as the
      // values used in testing often appear in other contexts such as application names,
      // table or schema names. etc.

      const connectionString: string | undefined = process.env.DATABASE_URL;
      expect(connectionString).toBeDefined();
      if (connectionString) {
        const matches = connectionString.match(/\/\/([^@]*)@/);
        expect(matches).toBeDefined();
        if (matches) {
          const usernameAndPassword: string = matches[1];
          expect(logged.match(usernameAndPassword)).toBeFalsy();
        }
      }
    }
  });

  it('should correctly deal with simple errors', async () => {
    function brokenExample() {
      throw 'something went wrong';
    }

    try {
      brokenExample();
      fail('broken example should throw');
    } catch (error) {
      const transformed: Error = obfuscateError(error);
      expect(transformed.message).toEqual('something went wrong');
    }
  });
});
