/*
 * This is a very rough-edged example, the idea is to still work with the fact that oidc-provider
 * has a rather "dynamic" schema. This example uses sequelize with postgresql, and all dynamic data
 * uses JSON fields. id is set to be the primary key, grantId should be additionaly indexed for
 * models where these fields are set (grantId-able models). userCode should be additionaly indexed
 * for DeviceCode model. uid should be additionaly indexed for Session model. For sequelize
 * migrations @see https://github.com/Rogger794/node-oidc-provider/tree/examples/example/migrations/sequelize
 */

import { Adapter } from 'oidc-provider';
import { Db } from '../../db/db.js';
import { DbTable } from 'orchid-orm';
import { JsonTable } from '../tables/tables.js';
import { obfuscateError } from 'haven-hid-domain';

interface OidcRecord {
  grantId: string;
  userCode: string;
  uid: string;
}

export class PgOidcAdapter implements Adapter {
  private name: string;
  private table: DbTable<typeof JsonTable>;

  constructor(name: string) {
    this.name = name;
    this.table = Db.getTable(name); //as DbTable<any>
  }

  async upsert(id: string, data: Partial<OidcRecord>, expiresIn: number) {
    const record = {
      id,
      data,
      ...(data.grantId ? { grantId: data.grantId } : undefined),
      ...(data.userCode ? { userCode: data.userCode } : undefined),
      ...(data.uid ? { uid: data.uid } : undefined),
      ...(expiresIn
        ? {
            expiresAt: new Date(Date.now() + expiresIn * 1000),
          }
        : undefined),
    };
    try {
      await this.table.findBy({ id }).upsert({
        update: record,
        create: record,
      });
    } catch (e) {
      throw obfuscateError(e);
    }
  }

  async find(id: string) {
    try {
      const found = await this.table.findByOptional({ id: id });
      if (!found) return undefined;
      return {
        ...(found.data || {}),
        ...(found.consumedAt ? { consumed: true } : undefined),
      };
    } catch (e) {
      throw obfuscateError(e);
    }
  }

  async findByUserCode(userCode: string) {
    try {
      const found = await this.table.findByOptional({ userCode: userCode });
      if (!found) return undefined;
      return {
        ...(found.data || {}),
        ...(found.consumedAt ? { consumed: true } : undefined),
      };
    } catch (e) {
      throw obfuscateError(e);
    }
  }

  async findByUid(uid: string) {
    if (!uid) {
      return undefined;
    }
    try {
      const found = await this.table.findByOptional({ uid: uid });
      if (!found) return undefined;
      return {
        ...(found.data || {}),
        ...(found.consumedAt ? { consumed: true } : undefined),
      };
    } catch (e) {
      throw obfuscateError(e);
    }
  }

  async destroy(id: string) {
    try {
      await this.table.where({ id }).delete();
    } catch (e) {
      throw obfuscateError(e);
    }
  }

  async consume(id: string) {
    try {
      await this.table.findBy({ id }).update({ consumedAt: new Date() });
    } catch (e) {
      throw obfuscateError(e);
    }
  }

  async revokeByGrantId(grantId: string) {
    try {
      await this.table.where({ grantId }).delete();
    } catch (e) {
      throw obfuscateError(e);
    }
  }
}
