import { SystemError } from 'haven-hid-domain';
import { logger } from '@havenengineering/module-haven-logging';

interface PgError {
  query: string;
  message: string;
}

export const toSystemError =
  (message: string) =>
  (e: SystemError | PgError): SystemError => {
    // Handle conversion to system error in one place
    if (e instanceof SystemError) return e;

    if (e.query) {
      logger.error(message, { cause: e.message });
      return new SystemError(message, {
        cause: e.message,
      });
    }

    logger.error(message, e);
    return new SystemError(message, { cause: e });
  };
