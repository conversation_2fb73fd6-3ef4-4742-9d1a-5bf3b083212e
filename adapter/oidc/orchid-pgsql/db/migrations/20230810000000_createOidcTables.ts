import { rakeDb } from 'rake-db';
import { getDbConfig } from '../config.js';
import { rakeConfig } from '../db-migrate.js';

rakeDb(getDbConfig().allDatabases, rakeConfig(), ['migrate'])(async (db) => {
  const MAX_ID_LENGTH = 30; // grants id = 43 33 + 10
  const MAX_GRANTID_LENGTH = 50; // grants id = 43 33 + 10
  const MAX_UID_LENGTH = 30; // nanoid current default  21
  const MAX_USERCODE_LENGTH = 20; // depends on mask used in device flow feature configuration (default is ****-****)

  // @ts-ignore
  await db.createTable('device_code', (t) => ({
    id: t.varchar(MAX_ID_LENGTH).primaryKey().index(),
    userCode: t.varchar(MAX_USERCODE_LENGTH).index(),
    grantId: t.varchar(MAX_ID_LENGTH).index(),
    data: t.jsonText(),
    expiresAt: t.timestamp(),
    consumedAt: t.timestamp().nullable(),
  }));

  // @ts-ignore
  await db.createTable('session', (t) => ({
    id: t.varchar(MAX_ID_LENGTH).primaryKey().index(),
    uid: t.varchar(MAX_UID_LENGTH).index(),
    data: t.jsonText(),
    expiresAt: t.timestamp(),
    consumedAt: t.timestamp().nullable(),
  }));

  await Promise.all(
    [
      'access_token',
      'authorization_code',
      'refresh_token',
      'backchannel_authentication_request',
    ].map(async (name) => {
      // @ts-ignore
      return db.createTable(name, (t) => ({
        id: t.varchar(MAX_ID_LENGTH).primaryKey().index(),
        grantId: t.varchar(MAX_GRANTID_LENGTH).index(),
        data: t.jsonText(),
        expiresAt: t.timestamp(),
        consumedAt: t.timestamp().nullable(),
      }));
    }),
  );

  await Promise.all(
    [
      'client_credentials',
      'client',
      'initial_access_token',
      'registration_access_token',
      'interaction',
      'replay_detection',
      'pushed_authorization_request',
    ].map(async (name) => {
      // @ts-ignore
      return db.createTable(name, (t) => ({
        id: t.varchar(MAX_ID_LENGTH).primaryKey().index(),
        data: t.jsonText(),
        expiresAt: t.timestamp(),
        consumedAt: t.timestamp().nullable(),
      }));
    }),
  );

  await Promise.all(
    ['grants'].map(async (name) => {
      // @ts-ignore
      return db.createTable(name, (t) => ({
        id: t.varchar(MAX_GRANTID_LENGTH).primaryKey().index(),
        data: t.jsonText(),
        expiresAt: t.timestamp(),
        consumedAt: t.timestamp().nullable(),
      }));
    }),
  );
});
