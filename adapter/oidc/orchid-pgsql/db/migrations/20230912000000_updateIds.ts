import { rakeDb } from 'rake-db';
import { getDbConfig } from '../config.js';
import { rakeConfig } from '../db-migrate.js';

rakeDb(getDbConfig().allDatabases, rakeConfig(), ['migrate'])(async (db) => {
  const MAX_ID_LENGTH = 30; // grants id = 43 33 + 10
  const NEW_ID_LENGTH = 50; // grants id = 43 33 + 10

  await db.changeTable('device_code', (t) => ({
    id: t.change(t.varchar(MAX_ID_LENGTH), t.varchar(NEW_ID_LENGTH)),
  }));

  await db.changeTable('session', (t) => ({
    id: t.change(t.varchar(MAX_ID_LENGTH), t.varchar(NEW_ID_LENGTH)),
  }));

  await Promise.all(
    [
      'access_token',
      'authorization_code',
      'refresh_token',
      'backchannel_authentication_request',
    ].map(async (name) => {
      return db.changeTable(name, (t) => ({
        id: t.change(t.varchar(MAX_ID_LENGTH), t.varchar(NEW_ID_LENGTH)),
      }));
    }),
  );

  await Promise.all(
    [
      'client_credentials',
      'client',
      'initial_access_token',
      'registration_access_token',
      'interaction',
      'replay_detection',
      'pushed_authorization_request',
    ].map(async (name) => {
      return db.changeTable(name, (t) => ({
        id: t.change(t.varchar(MAX_ID_LENGTH), t.varchar(NEW_ID_LENGTH)),
      }));
    }),
  );
});
