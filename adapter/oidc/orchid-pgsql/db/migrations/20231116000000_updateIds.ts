import { rakeDb } from 'rake-db';
import { getDbConfig } from '../config.js';
import { rakeConfig } from '../db-migrate.js';

rakeDb(getDbConfig().allDatabases, rakeConfig(), ['migrate'])(async (db) => {
  const NEW_ID_LENGTH = 50; // missed changes

  await db.changeTable('device_code', (t) => ({
    grantId: t.change(t.varchar(30), t.varchar(NEW_ID_LENGTH)),
  }));

  await db.changeTable('session', (t) => ({
    uid: t.change(t.varchar(30), t.varchar(NEW_ID_LENGTH)),
  }));
});
