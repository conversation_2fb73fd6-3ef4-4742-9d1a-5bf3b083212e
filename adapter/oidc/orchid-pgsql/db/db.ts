import dotenv from 'dotenv';
import findConfig from 'find-config';
import { DbTable, OrchidORM, orchidORM } from 'orchid-orm';
import { getDbConfig } from './config.js';
import {
  AccessTokenTable,
  AuthorizationCodeTable,
  BackchannelAuthenticationRequestTable,
  ClientCredentialsTable,
  ClientTable,
  DeviceCodeTable,
  GrantTable,
  InitialAccessTokenTable,
  InteractionTable,
  JsonTable,
  PushedAuthorizationRequestTable,
  RefreshTokenTable,
  RegistrationAccessTokenTable,
  ReplayDetectionTable,
  SessionTable,
} from './tables/tables.js';

type OidcTables = {
  session: typeof SessionTable;
  client: typeof ClientTable;
  clientCredentials: typeof ClientCredentialsTable;
  deviceCode: typeof DeviceCodeTable;
  accessToken: typeof AccessTokenTable;
  authorizationCode: typeof AuthorizationCodeTable;
  refreshToken: typeof RefreshTokenTable;
  registrationAccessToken: typeof RegistrationAccessTokenTable;
  backchannelAuthenticationRequest: typeof BackchannelAuthenticationRequestTable;
  initialAccessToken: typeof InitialAccessTokenTable;
  interaction: typeof InteractionTable;
  replayDetection: typeof ReplayDetectionTable;
  pushedAuthorizationRequest: typeof PushedAuthorizationRequestTable;
  grant: typeof GrantTable;
};

export class Db {
  private static _instance: OrchidORM<OidcTables>;
  public static get instance(): OrchidORM<OidcTables> {
    if (!this._instance) {
      const path: string = findConfig('.env') || '.env';
      dotenv.config({ path });
      this._instance = orchidORM(getDbConfig().database, {
        session: SessionTable,
        client: ClientTable,
        clientCredentials: ClientCredentialsTable,
        deviceCode: DeviceCodeTable,
        accessToken: AccessTokenTable,
        authorizationCode: AuthorizationCodeTable,
        refreshToken: RefreshTokenTable,
        registrationAccessToken: RegistrationAccessTokenTable,
        backchannelAuthenticationRequest: BackchannelAuthenticationRequestTable,
        initialAccessToken: InitialAccessTokenTable,
        interaction: InteractionTable,
        replayDetection: ReplayDetectionTable,
        pushedAuthorizationRequest: PushedAuthorizationRequestTable,
        grant: GrantTable,
      });
    }
    return this._instance;
  }

  static getTable(name: string): DbTable<typeof JsonTable> {
    const key = name.substring(0, 1).toLowerCase() + name.substring(1);

    if (Object.hasOwn(Db.instance, key)) {
      return Db.instance[key as keyof typeof Db.instance] as DbTable<typeof JsonTable>;
    } else {
      throw new Error(`Unknown table ${name}`);
    }
  }
}
