import { createBaseTable, Table } from 'orchid-orm';
import {} from 'pqb'; // See https://github.com/microsoft/TypeScript/issues/47663#issuecomment-1519138189
export type TableConfig = Table['columns'];

const MAX_ID_LENGTH = 50;

const BaseTable = createBaseTable({
  snakeCase: true,
  columnTypes: (t) => ({
    ...t,
    timestamp: <P extends number>(precision?: P) => t.timestamp<P>(precision).asDate(),
  }),
});

export class JsonTable extends BaseTable {
  columns: TableConfig = this.setColumns((t) => ({
    id: t.varchar(MAX_ID_LENGTH).primaryKey().index(),
    data: t.jsonText(),
    expiresAt: t.timestamp(),
    consumedAt: t.timestamp().nullable(),
  }));
}

export class DeviceCodeTable extends BaseTable {
  readonly table = 'device_code';
  columns = this.setColumns((t) => ({
    ...{
      id: t.varchar(MAX_ID_LENGTH).primaryKey().index(),
      data: t.jsonText(),
      expiresAt: t.timestamp(),
      consumedAt: t.timestamp().nullable(),
      grantId: t.varchar(MAX_ID_LENGTH).index(),
    },
    userCode: t.varchar(),
  }));
}

export class AccessTokenTable extends BaseTable {
  readonly table = 'access_token';
  columns: TableConfig = this.setColumns((t) => ({
    id: t.varchar(MAX_ID_LENGTH).primaryKey().index(),
    data: t.jsonText(),
    expiresAt: t.timestamp(),
    consumedAt: t.timestamp().nullable(),
    grantId: t.varchar(MAX_ID_LENGTH).index(),
  }));
}

export class AuthorizationCodeTable extends BaseTable {
  readonly table = 'authorization_code';
  columns: TableConfig = this.setColumns((t) => ({
    id: t.varchar(MAX_ID_LENGTH).primaryKey().index(),
    data: t.jsonText(),
    expiresAt: t.timestamp(),
    consumedAt: t.timestamp().nullable(),
    grantId: t.varchar(MAX_ID_LENGTH).index(),
  }));
}

export class RefreshTokenTable extends BaseTable {
  readonly table = 'refresh_token';
  columns: TableConfig = this.setColumns((t) => ({
    id: t.varchar(MAX_ID_LENGTH).primaryKey().index(),
    data: t.jsonText(),
    expiresAt: t.timestamp(),
    consumedAt: t.timestamp().nullable(),
    grantId: t.varchar(MAX_ID_LENGTH).index(),
  }));
}

export class BackchannelAuthenticationRequestTable extends BaseTable {
  readonly table = 'backchannel_authentication_request';
  columns: TableConfig = this.setColumns((t) => ({
    id: t.varchar(MAX_ID_LENGTH).primaryKey().index(),
    data: t.jsonText(),
    expiresAt: t.timestamp(),
    consumedAt: t.timestamp().nullable(),
    grantId: t.varchar(MAX_ID_LENGTH).index(),
  }));
}

export class SessionTable extends BaseTable {
  readonly table = 'session';
  columns: TableConfig = this.setColumns((t) => ({
    ...{
      id: t.varchar(MAX_ID_LENGTH).primaryKey().index(),
      data: t.jsonText(),
      expiresAt: t.timestamp(),
      consumedAt: t.timestamp().nullable(),
    },
    uid: t.varchar(),
  }));
}

export class ClientCredentialsTable extends BaseTable {
  readonly table = 'client_credentials';
  columns: TableConfig = this.setColumns((t) => ({
    id: t.varchar(MAX_ID_LENGTH).primaryKey().index(),
    data: t.jsonText(),
    expiresAt: t.timestamp(),
    consumedAt: t.timestamp().nullable(),
  }));
}

export class ClientTable extends BaseTable {
  readonly table = 'client';
  columns: TableConfig = this.setColumns((t) => ({
    id: t.varchar(MAX_ID_LENGTH).primaryKey().index(),
    data: t.jsonText(),
    expiresAt: t.timestamp(),
    consumedAt: t.timestamp().nullable(),
  }));
}

export class InitialAccessTokenTable extends JsonTable {
  readonly table = 'initial_access_token';
  columns: TableConfig = this.setColumns((t) => ({
    id: t.varchar(MAX_ID_LENGTH).primaryKey().index(),
    data: t.jsonText(),
    expiresAt: t.timestamp(),
    consumedAt: t.timestamp().nullable(),
  }));
}

export class RegistrationAccessTokenTable extends BaseTable {
  readonly table = 'registration_access_token';
  columns: TableConfig = this.setColumns((t) => ({
    id: t.varchar(MAX_ID_LENGTH).primaryKey().index(),
    data: t.jsonText(),
    expiresAt: t.timestamp(),
    consumedAt: t.timestamp().nullable(),
  }));
}

export class InteractionTable extends BaseTable {
  readonly table = 'interaction';
  columns: TableConfig = this.setColumns((t) => ({
    id: t.varchar(MAX_ID_LENGTH).primaryKey().index(),
    data: t.jsonText(),
    expiresAt: t.timestamp(),
    consumedAt: t.timestamp().nullable(),
  }));
}

export class ReplayDetectionTable extends BaseTable {
  readonly table = 'replay_detection';
  columns: TableConfig = this.setColumns((t) => ({
    id: t.varchar(MAX_ID_LENGTH).primaryKey().index(),
    data: t.jsonText(),
    expiresAt: t.timestamp(),
    consumedAt: t.timestamp().nullable(),
  }));
}

export class PushedAuthorizationRequestTable extends BaseTable {
  readonly table = 'pushed_authorization_request';
  columns: TableConfig = this.setColumns((t) => ({
    id: t.varchar(MAX_ID_LENGTH).primaryKey().index(),
    data: t.jsonText(),
    expiresAt: t.timestamp(),
    consumedAt: t.timestamp().nullable(),
  }));
}

export class GrantTable extends BaseTable {
  readonly table = 'grants';
  columns: TableConfig = this.setColumns((t) => ({
    id: t.varchar(MAX_ID_LENGTH).primaryKey().index(),
    data: t.jsonText(),
    expiresAt: t.timestamp(),
    consumedAt: t.timestamp().nullable(),
  }));
}
