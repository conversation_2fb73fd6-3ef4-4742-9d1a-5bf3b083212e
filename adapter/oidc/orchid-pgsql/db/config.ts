import { logger } from '@havenengineering/module-haven-logging';
import { PoolConfig } from 'pg';

type AdapterPoolConfig = Omit<PoolConfig, 'types' | 'connectionString' | 'log'>;
type Database = AdapterPoolConfig & {
  databaseURL: string;
  schema: string;
};
type DatabaseConfig = {
  allDatabases: Database[];
  database: Database;
};

export enum Mode {
  SERVER = 'SERVER',
  MIGRATE = 'MIGRATE',
}

class DbConfig {
  private static _instance: DbConfig;

  readonly config: DatabaseConfig;

  constructor(mode: Mode) {
    logger.info(`ADAPTER | Database mode [${mode}]`);
    const poolConfig = getPoolConfig(mode);

    if (!process.env.DATABASE_URL) {
      throw new Error('ADAPTER | DATABASE_URL is required');
    }
    const database = getDatabase(process.env.DATABASE_URL, poolConfig);

    this.config = {
      allDatabases: [database],
      database: database,
    };

    if (process.env.DATABASE_TEST_URL) {
      const testDatabase = getDatabase(process.env.DATABASE_TEST_URL, poolConfig);
      this.config.allDatabases.unshift(testDatabase);
      if (process.env.NODE_ENV === 'test') {
        this.config.database = testDatabase;
      }
    } else {
      if (process.env.NODE_ENV === 'test') {
        throw new Error('ADAPTER | DATABASE_TEST_URL is required for test env');
      }
    }
  }

  public static Instance(mode: Mode = Mode.SERVER) {
    return this._instance || (this._instance = new this(mode));
  }
}

export const getDbConfig = (mode: Mode = Mode.SERVER) => {
  return DbConfig.Instance(mode).config;
};

const getDatabase = (url: string, config: AdapterPoolConfig): Database => {
  return {
    databaseURL: url,
    schema: 'oidc',
    ...config,
  };
};

const getPoolConfig = (mode: Mode): AdapterPoolConfig => {
  switch (mode) {
    case Mode.SERVER:
      return serverConfig();
    case Mode.MIGRATE:
      return migrationConfig();
  }
};

const serverConfig = (): AdapterPoolConfig => {
  return {
    // give the application a name so that you can track who has connections to the db
    application_name: 'oidc-adapter',
    // keep idle connections in pool for reuse
    idleTimeoutMillis: 60000,
    // size the connection pool based on max number of connections and max number of pods
    max: 45,
    // don't hang - set timeouts on db calls
    connectionTimeoutMillis: 10000,
    statement_timeout: 10000,
    query_timeout: 10000,
  };
};

const migrationConfig = () => {
  return {
    application_name: 'oidc-adapter-migration',
    idleTimeoutMillis: 60000,
    max: 10,
    connectionTimeoutMillis: 10000,
    statement_timeout: 300000,
    query_timeout: 300000,
  };
};
