import { AdapterFactory } from 'oidc-provider';
import { SessionRepository } from './repositories/session.js';
import { AccessTokenRepository } from './repositories/access_token.js';
import { AuthorizationCodeRepository } from './repositories/authorization_code.js';
import { BackchannelAuthenticationRequestRepository } from './repositories/backchannel_authentication_request.js';
import { ClientRepository } from './repositories/client.js';
import { ClientCredentialsRepository } from './repositories/client_credentials.js';
import { DeviceCodeRepository } from './repositories/device_code.js';
import { GrantsRepository } from './repositories/grants.js';
import { InitialAccessTokenRepository } from './repositories/initial_access_token.js';
import { InteractionRepository } from './repositories/interaction.js';
import { PushedAuthorizationRequestRepository } from './repositories/pushed_authorization_request.js';
import { RefreshTokenRepository } from './repositories/refresh_token.js';
import { RegistrationAccessTokenRepository } from './repositories/registration_access_token.js';
import { ReplayDetectionRepository } from './repositories/replay_detection.js';

export const PrismaAdapterFactory: AdapterFactory = (name: string) => {
  switch (name) {
    case 'Session':
      return SessionRepository();
    case 'AccessToken':
      return AccessTokenRepository();
    case 'AuthorizationCode':
      return AuthorizationCodeRepository();
    case 'RefreshToken':
      return RefreshTokenRepository();
    case 'DeviceCode':
      return DeviceCodeRepository();
    case 'ClientCredentials':
      return ClientCredentialsRepository();
    case 'Client':
      return ClientRepository();
    case 'InitialAccessToken':
      return InitialAccessTokenRepository();
    case 'RegistrationAccessToken':
      return RegistrationAccessTokenRepository();
    case 'Interaction':
      return InteractionRepository();
    case 'ReplayDetection':
      return ReplayDetectionRepository();
    case 'PushedAuthorizationRequest':
      return PushedAuthorizationRequestRepository();
    case 'Grant':
      return GrantsRepository();
    case 'BackchannelAuthenticationRequest':
      return BackchannelAuthenticationRequestRepository();
    default:
      throw new Error('Invalid model name');
  }
};
