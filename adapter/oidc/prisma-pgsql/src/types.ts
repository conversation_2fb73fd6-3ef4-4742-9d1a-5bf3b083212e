import {
  session,
  access_token,
  authorization_code,
  backchannel_authentication_request,
  client,
  client_credentials,
  device_code,
  grants,
  initial_access_token,
  interaction,
  pushed_authorization_request,
  refresh_token,
  registration_access_token,
  replay_detection,
} from '@prisma/client';

export type PrismaModelTypes =
  | session
  | access_token
  | authorization_code
  | backchannel_authentication_request
  | client
  | client_credentials
  | device_code
  | grants
  | initial_access_token
  | interaction
  | pushed_authorization_request
  | refresh_token
  | registration_access_token
  | replay_detection;
