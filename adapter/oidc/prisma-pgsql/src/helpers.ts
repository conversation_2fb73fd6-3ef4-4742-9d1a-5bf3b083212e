import { PrismaModelTypes } from './types.js';

export const prepare = (doc: PrismaModelTypes) => {
  const payload =
    doc.data && typeof doc.data === 'object' && !Array.isArray(doc.data) ? doc.data : {};
  return {
    ...payload,
    ...(doc.consumed_at ? { consumed: true } : {}),
  };
};

export const expiresAt = (expiresIn?: number) => {
  if (!expiresIn) {
    return null;
  }
  return new Date(Date.now() + expiresIn * 1000);
};
