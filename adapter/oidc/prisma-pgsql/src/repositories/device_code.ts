import { Prisma } from '@prisma/client';
import { Adapter, AdapterPayload } from 'oidc-provider';
import { expiresAt, prepare } from '../helpers.js';
import prisma from '../libs/prisma.js';

export const DeviceCodeRepository = (): Adapter => {
  return {
    upsert: async (id: string, payload: AdapterPayload, expiresIn?: number) => {
      const data = {
        data: payload as Prisma.InputJsonValue,
        grant_id: payload.grantId as string,
        user_code: payload.userCode as string,
        expires_at: expiresAt(expiresIn) || new Date(),
      };

      await prisma.device_code.upsert({
        where: {
          id,
        },
        update: {
          ...data,
        },
        create: {
          id,
          ...data,
        },
      });
    },
    find: async (id: string) => {
      const doc = await prisma.device_code.findUnique({
        where: {
          id,
        },
      });

      if (!doc || (doc.expires_at && doc.expires_at < new Date())) {
        return undefined;
      }

      return prepare(doc);
    },
    findByUserCode: async (userCode: string) => {
      const doc = await prisma.device_code.findFirst({
        where: {
          user_code: userCode,
        },
      });

      if (!doc || (doc.expires_at && doc.expires_at < new Date())) {
        return undefined;
      }

      return prepare(doc);
    },
    findByUid: async () => {
      return Promise.resolve(undefined);
    },
    consume: async (id: string) => {
      await prisma.device_code.update({
        where: {
          id,
        },
        data: {
          consumed_at: new Date(),
        },
      });
    },
    destroy: async (id: string) => {
      await prisma.device_code.delete({
        where: {
          id,
        },
      });
    },
    revokeByGrantId: async (grantId: string) => {
      await prisma.device_code.deleteMany({
        where: {
          grant_id: grantId,
        },
      });
    },
  };
};
