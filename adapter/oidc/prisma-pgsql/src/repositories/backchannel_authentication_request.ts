import { Prisma } from '@prisma/client';
import { Adapter, AdapterPayload } from 'oidc-provider';
import { expiresAt, prepare } from '../helpers.js';
import prisma from '../libs/prisma.js';

export const BackchannelAuthenticationRequestRepository = (): Adapter => {
  return {
    upsert: async (id: string, payload: AdapterPayload, expiresIn?: number) => {
      const data = {
        data: payload as Prisma.InputJsonValue,
        grant_id: payload.grantId as string,
        expires_at: expiresAt(expiresIn) || new Date(),
      };

      await prisma.backchannel_authentication_request.upsert({
        where: {
          id,
        },
        update: {
          ...data,
        },
        create: {
          id,
          ...data,
        },
      });
    },
    find: async (id: string) => {
      const doc = await prisma.backchannel_authentication_request.findUnique({
        where: {
          id,
        },
      });

      if (!doc || (doc.expires_at && doc.expires_at < new Date())) {
        return undefined;
      }

      return prepare(doc);
    },
    findByUserCode: async () => {
      return Promise.resolve(undefined);
    },
    findByUid: async () => {
      return Promise.resolve(undefined);
    },
    consume: async (id: string) => {
      await prisma.backchannel_authentication_request.update({
        where: {
          id,
        },
        data: {
          consumed_at: new Date(),
        },
      });
    },
    destroy: async (id: string) => {
      await prisma.backchannel_authentication_request.delete({
        where: {
          id,
        },
      });
    },
    revokeByGrantId: async (grantId: string) => {
      await prisma.backchannel_authentication_request.deleteMany({
        where: {
          grant_id: grantId,
        },
      });
    },
  };
};
