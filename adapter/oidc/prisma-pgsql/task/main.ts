import { logger } from '@havenengineering/module-haven-logging';
import { start, stop } from 'haven-identity-database';
import humanizeDuration from 'humanize-duration';
import { ResultAsync, okAsync } from 'neverthrow';
import { Config, Mode, getConfig } from './config.js';

export type NoResult = void;

export const toError = (e: unknown): Error => {
  logger.error(e as Error);
  return new Error(`Unexpected Error: ${JSON.stringify(e)}`, { cause: e });
};

const handleError = (error: Error) => {
  logger.error(error.message);
  process.exit(1);
};

const noopTask = (): ResultAsync<NoResult, Error> => {
  return okAsync(undefined);
};

const stopTask = (): ResultAsync<NoResult, Error> => {
  return stop();
};

const startTask = (config: Config): ResultAsync<NoResult, Error> => {
  return start([config.database], {
    setupPrismaOidcEnv: true,
    useLocalhostForMac: true,
  }).map(() => undefined);
};

const runTask = (config: Config): ResultAsync<NoResult, Error> => {
  switch (config.mode) {
    case Mode.STOP:
      return stopTask();
    case Mode.START:
      return startTask(config);
    case Mode.RESTART:
      return noopTask();
  }
};

export const main = async (): Promise<NoResult> => {
  const start = new Date().getTime();
  logger.info('OIDC ADAPTER | Load configuration');
  const config = getConfig();

  await runTask(config)
    .map(() => {
      const end = new Date().getTime();
      const duration = end - start;
      logger.info(`OIDC ADAPTER | ${config.mode} Complete in ${humanizeDuration(duration)}`);
      process.exit(0);
    })
    .mapErr(handleError)
    .unwrapOr(undefined);
};

await main();
