import { logger } from '@havenengineering/module-haven-logging';
import dotenv from 'dotenv';
import { Database } from 'haven-identity-database';

export enum Mode {
  START = 'START',
  RESTART = 'RESTART',
  STOP = 'STOP',
}

export class Config {
  public database: Database = {
    name: 'identity',
    user: 'hid',
    password: 'hid',
  };

  public ci: boolean = process.env.CI ? true : false;

  get mode(): Mode {
    let mode: keyof typeof Mode;
    for (mode in Mode) {
      if (process.argv.includes(`--${mode.toLocaleLowerCase()}`)) {
        return Mode[mode];
      }
    }
    return Mode.START;
  }
}

export const getConfig = (): Config => {
  dotenv.config();
  const result = new Config();

  logger.info(`CONFIG | Mode is ${result.mode}`);

  return result;
};
