{"name": "haven-identity-oidc-prisma-pgsql-adapter", "description": "Haven Identity OIDC Adapter for PostgreSQL using Prisma-ORM", "version": "1.0.0", "main": "index.js", "type": "module", "types": "./dist/src/exports.d.ts", "exports": {".": "./dist/src/exports.js", "./adapter.js": "./dist/src/exports.js"}, "scripts": {"test": "vitest run --reporter verbose --reporter json --outputFile dist/vitest.json", "build": "prisma generate && tsc", "generate": "prisma generate", "migrate": "prisma migrate deploy", "clean": "<PERSON><PERSON><PERSON> dist", "start": "TESTCONTAINERS_RYUK_DISABLED=true pnpm task --start", "stop": "pnpm task --stop", "task": "node dist/task/main.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@havenengineering/module-haven-logging": "^0.5.1", "@prisma/client": "6.4.1", "prisma": "^6.4.1", "dotenv": "^16.4.5", "haven-identity-database": "workspace:*", "neverthrow": "^6.2.1"}, "devDependencies": {"@types/humanize-duration": "^3.27.4", "@types/oidc-provider": "^8.4.4", "humanize-duration": "^3.32.1", "nanoid": "^5.1.3", "oidc-provider": "^8.4.6", "rimraf": "^6.0.1", "typescript": "^5.4.5", "vitest": "^1.6.0", "vitest-mock-extended": "^1.3.1"}, "engines": {"npm": "use-pnpm", "node": ">=20"}}