import { beforeEach, describe, expect, it, vi } from 'vitest';
import { expiresAt, prepare } from '../src/helpers.js';
import { PrismaModelTypes } from '../src/types.js';

describe('Helpers tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('expiresAt', () => {
    it('should return null if expiresIn is not provided', () => {
      expect(expiresAt()).toBeNull();
    });

    it('should return null if expiresIn is 0', () => {
      expect(expiresAt(0)).toBeNull();
    });

    it('should return a date in the future', () => {
      const expiresIn = 60;
      const now = Date.now();
      const expires = expiresAt(expiresIn);

      expect(expires).toBeInstanceOf(Date);

      const expiresTime = expires?.getTime();
      expect(expiresTime).toBeGreaterThan(now);
      expect(expiresTime).toBeLessThanOrEqual(now + expiresIn * 1000);
    });
  });

  describe('prepare', () => {
    const mockJsonData: PrismaModelTypes = {
      id: 'id',
      data: { foo: 'bar' },
      expires_at: new Date(),
      consumed_at: null,
    };
    it('should return the data object if it is an object', () => {
      expect(prepare(mockJsonData)).toEqual({ foo: 'bar' });
    });

    it('should return the data object if it is an object and consumed_at is set', () => {
      expect(prepare({ ...mockJsonData, consumed_at: new Date() })).toEqual({
        foo: 'bar',
        consumed: true,
      });
    });

    it('should return an empty object if data is not an object', () => {
      const doc = { data: 'foo' };
      expect(prepare({ ...mockJsonData, data: 'foo' })).toEqual({});
    });

    it('should return an empty object if data is an array', () => {
      expect(prepare({ ...mockJsonData, data: ['foo'] })).toEqual({});
    });
  });
});
