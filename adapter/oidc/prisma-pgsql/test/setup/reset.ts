import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async () => {
  await prisma.$transaction([
    prisma.access_token.deleteMany(),
    prisma.authorization_code.deleteMany(),
    prisma.backchannel_authentication_request.deleteMany(),
    prisma.client_credentials.deleteMany(),
    prisma.client.deleteMany(),
    prisma.device_code.deleteMany(),
    prisma.grants.deleteMany(),
    prisma.initial_access_token.deleteMany(),
    prisma.interaction.deleteMany(),
    prisma.pushed_authorization_request.deleteMany(),
    prisma.refresh_token.deleteMany(),
    prisma.registration_access_token.deleteMany(),
    prisma.replay_detection.deleteMany(),
    prisma.session.deleteMany(),
  ]);
};
