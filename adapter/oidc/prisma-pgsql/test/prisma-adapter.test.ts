import { beforeEach, describe, expect, it, vi } from 'vitest';
import { PrismaAdapterFactory } from '../src/prisma-adapter.js';

describe('PrismaAdapterFactory tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it.each([
    'Session',
    'AccessToken',
    'AuthorizationCode',
    'RefreshToken',
    'DeviceCode',
    'ClientCredentials',
    'Client',
    'InitialAccessToken',
    'RegistrationAccessToken',
    'Interaction',
    'ReplayDetection',
    'PushedAuthorizationRequest',
    'Grant',
    'BackchannelAuthenticationRequest',
  ])('should return the correct repository for %s', (name) => {
    const adapter = PrismaAdapterFactory(name);
    expect(adapter).toBeDefined();
  });

  it('should throw an error if the model name is invalid', () => {
    expect(() => PrismaAdapterFactory('Invalid')).toThrowError('Invalid model name');
  });
});
