import { describe, expect, it } from 'vitest';
import { PrismaAdapterFactory } from '../../src/prisma-adapter.js';
import { nextGrantId, nextId } from '../setup/helper.js';
import prisma from '../setup/prisma.js';

describe('backchannel_authentication_request repository', () => {
  const adapter = PrismaAdapterFactory('BackchannelAuthenticationRequest');
  const id = nextId();
  const data = {
    grantId: nextGrantId(),
    foo: 'bar',
  };

  it('can insert and read existing', async () => {
    await adapter.upsert(id, data, 60);
    const found = await adapter.find(id);
    expect(found).toEqual(data);
  });

  it('can update existing', async () => {
    await adapter.upsert(id, data, 60);
    const found = await adapter.find(id);
    expect(found).toEqual(data);

    const updated = { ...data, updated: true };
    await adapter.upsert(id, updated, 60);

    const result = await adapter.find(id);
    expect(result).toEqual(updated);
  });

  it('can destroy', async () => {
    const id = nextId();
    const data = {
      grantId: nextGrantId(),
      foo: 'bar',
    };
    await adapter.upsert(id, data, 60);
    const found = await adapter.find(id);
    expect(found).toEqual(data);

    await adapter.destroy(id);
    const result = await adapter.find(id);
    expect(result).toBeUndefined();
  });

  it('returns undefined when not found by id', async () => {
    const id = nextId();
    const found = await adapter.find(id);
    expect(found).toBeUndefined();
  });

  it('returns undefined when expired', async () => {
    const id = nextId();
    const grantId = nextGrantId();
    await prisma.backchannel_authentication_request.create({
      data: {
        id,
        data: {
          grantId,
          foo: 'bar',
        },
        grant_id: grantId,
        expires_at: new Date('2021-01-01'),
        consumed_at: null,
      },
    });
    const found = await adapter.find(id);
    expect(found).toBeUndefined();
  });

  it('can consume', async () => {
    const id = nextId();
    const data = {
      grantId: nextGrantId(),
      foo: 'bar',
    };
    await adapter.upsert(id, data, 60);
    await adapter.consume(id);

    const found = await adapter.find(id);
    expect(found).toEqual({
      ...data,
      consumed: true,
    });
  });

  it('can revoke by grantId', async () => {
    const id = nextId();
    const grantId = nextGrantId();
    const data = {
      grantId,
      foo: 'bar',
    };

    await adapter.upsert(id, data, 60);
    const check = await adapter.find(id);
    expect(check).toEqual(data);

    await adapter.revokeByGrantId(grantId);

    const result = await adapter.find(id);
    expect(result).toBeUndefined();
  });
});
