import { describe, expect, it } from 'vitest';
import { PrismaAdapterFactory } from '../../src/prisma-adapter.js';
import { nextId } from '../setup/helper.js';
import prisma from '../setup/prisma.js';

describe('interaction repository', () => {
  const adapter = PrismaAdapterFactory('Interaction');
  const id = nextId();
  const data = {
    foo: 'bar',
  };

  it('can insert and read existing', async () => {
    await adapter.upsert(id, data, 60);
    const found = await adapter.find(id);
    expect(found).toEqual(data);
  });

  it('can update existing', async () => {
    await adapter.upsert(id, data, 60);
    const found = await adapter.find(id);
    expect(found).toEqual(data);

    const updated = { ...data, updated: true };
    await adapter.upsert(id, updated, 60);

    const result = await adapter.find(id);
    expect(result).toEqual(updated);
  });

  it('can destroy', async () => {
    const id = nextId();
    const data = {
      foo: 'bar',
    };
    await adapter.upsert(id, data, 60);
    const found = await adapter.find(id);
    expect(found).toEqual(data);

    await adapter.destroy(id);
    const result = await adapter.find(id);
    expect(result).toBeUndefined();
  });

  it('returns undefined when not found by id', async () => {
    const id = nextId();
    const found = await adapter.find(id);
    expect(found).toBeUndefined();
  });

  it('returns undefined when expired', async () => {
    const id = nextId();
    await prisma.interaction.create({
      data: {
        id,
        data: {
          foo: 'bar',
        },
        expires_at: new Date('2021-01-01'),
        consumed_at: null,
      },
    });
    const found = await adapter.find(id);
    expect(found).toBeUndefined();
  });

  it('can consume', async () => {
    const id = nextId();
    const data = {
      foo: 'bar',
    };
    await adapter.upsert(id, data, 60);
    await adapter.consume(id);

    const found = await adapter.find(id);
    expect(found).toEqual({
      ...data,
      consumed: true,
    });
  });
});
