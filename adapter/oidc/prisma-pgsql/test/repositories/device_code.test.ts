import { describe, expect, it } from 'vitest';
import { PrismaAdapterFactory } from '../../src/prisma-adapter.js';
import { nextGrantId, nextId, nextUserCode } from '../setup/helper.js';
import prisma from '../setup/prisma.js';

describe('device_code repository', () => {
  const adapter = PrismaAdapterFactory('DeviceCode');
  const id = nextId();
  const data = {
    grantId: nextGrantId(),
    userCode: nextUserCode(),
    foo: 'bar',
  };

  it('can insert and read existing', async () => {
    await adapter.upsert(id, data, 60);
    const found = await adapter.find(id);
    expect(found).toEqual(data);
  });

  it('can update existing', async () => {
    await adapter.upsert(id, data, 60);
    const found = await adapter.find(id);
    expect(found).toEqual(data);

    const updated = { ...data, updated: true };
    await adapter.upsert(id, updated, 60);

    const result = await adapter.find(id);
    expect(result).toEqual(updated);
  });

  it('can destroy', async () => {
    const id = nextId();
    const data = {
      grantId: nextGrantId(),
      userCode: nextUserCode(),
      foo: 'bar',
    };
    await adapter.upsert(id, data, 60);
    const found = await adapter.find(id);
    expect(found).toEqual(data);

    await adapter.destroy(id);
    const result = await adapter.find(id);
    expect(result).toBeUndefined();
  });

  it('returns undefined when not found by id', async () => {
    const id = nextId();
    const found = await adapter.find(id);
    expect(found).toBeUndefined();
  });

  it('returns undefined when expired', async () => {
    const id = nextId();
    const grantId = nextGrantId();
    const userCode = nextUserCode();
    await prisma.device_code.create({
      data: {
        id,
        data: {
          grantId,
          foo: 'bar',
          userCode,
        },
        grant_id: grantId,
        user_code: userCode,
        expires_at: new Date('2021-01-01'),
        consumed_at: null,
      },
    });
    const found = await adapter.find(id);
    expect(found).toBeUndefined();
  });

  it('can consume', async () => {
    const id = nextId();
    const data = {
      grantId: nextGrantId(),
      userCode: nextUserCode(),
      foo: 'bar',
    };
    await adapter.upsert(id, data, 60);
    await adapter.consume(id);

    const found = await adapter.find(id);
    expect(found).toEqual({
      ...data,
      consumed: true,
    });
  });

  it('can find by userCode', async () => {
    const id = nextId();
    const userCode = nextUserCode();
    const data = {
      grantId: nextGrantId(),
      userCode,
      foo: 'bar',
    };
    await adapter.upsert(id, data, 60);
    const found = await adapter.findByUserCode(userCode);
    expect(found).toEqual(data);
  });

  it('returns undefined when not found by userCode', async () => {
    const userCode = nextUserCode();
    const found = await adapter.findByUserCode(userCode);
    expect(found).toBeUndefined();
  });

  it('can revoke by grantId', async () => {
    const id = nextId();
    const grantId = nextGrantId();
    const userCode = nextUserCode();
    const data = {
      grantId,
      userCode,
      foo: 'bar',
    };

    await adapter.upsert(id, data, 60);
    const check = await adapter.find(id);
    expect(check).toEqual(data);

    await adapter.revokeByGrantId(grantId);

    const result = await adapter.find(id);
    expect(result).toBeUndefined();
  });
});
