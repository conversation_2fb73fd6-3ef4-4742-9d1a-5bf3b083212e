generator client {
  provider = "prisma-client-js"
  binaryTargets = ["native", "linux-musl-openssl-3.0.x", "linux-musl-arm64-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("OIDC_DATABASE_URL")
}

model access_token {
  id          String    @id @db.VarChar(50)
  grant_id    String    @db.VarChar(50)
  data        Json      @db.Json
  expires_at  DateTime  @db.Timestamptz(6)
  consumed_at DateTime? @db.Timestamptz(6)

  @@index([grant_id])
  @@index([id])
}

model authorization_code {
  id          String    @id @db.VarChar(50)
  grant_id    String    @db.VarChar(50)
  data        Json      @db.Json
  expires_at  DateTime  @db.Timestamptz(6)
  consumed_at DateTime? @db.Timestamptz(6)

  @@index([grant_id])
  @@index([id])
}

model backchannel_authentication_request {
  id          String    @id @db.<PERSON>ar<PERSON>har(50)
  grant_id    String    @db.Var<PERSON>har(50)
  data        Json      @db.Json
  expires_at  DateTime  @db.Timestamptz(6)
  consumed_at DateTime? @db.Timestamptz(6)

  @@index([grant_id])
  @@index([id])
}

model client {
  id          String    @id @db.VarChar(50)
  data        Json      @db.Json
  expires_at  DateTime  @db.Timestamptz(6)
  consumed_at DateTime? @db.Timestamptz(6)

  @@index([id])
}

model client_credentials {
  id          String    @id @db.VarChar(50)
  data        Json      @db.Json
  expires_at  DateTime  @db.Timestamptz(6)
  consumed_at DateTime? @db.Timestamptz(6)

  @@index([id])
}

model device_code {
  id          String    @id @db.VarChar(50)
  user_code   String    @db.VarChar(20)
  grant_id    String    @db.VarChar(50)
  data        Json      @db.Json
  expires_at  DateTime  @db.Timestamptz(6)
  consumed_at DateTime? @db.Timestamptz(6)

  @@index([grant_id])
  @@index([id])
  @@index([user_code])
}

model grants {
  id          String    @id @db.VarChar(50)
  data        Json      @db.Json
  expires_at  DateTime  @db.Timestamptz(6)
  consumed_at DateTime? @db.Timestamptz(6)

  @@index([id])
}

model initial_access_token {
  id          String    @id @db.VarChar(50)
  data        Json      @db.Json
  expires_at  DateTime  @db.Timestamptz(6)
  consumed_at DateTime? @db.Timestamptz(6)

  @@index([id])
}

model interaction {
  id          String    @id @db.VarChar(50)
  data        Json      @db.Json
  expires_at  DateTime  @db.Timestamptz(6)
  consumed_at DateTime? @db.Timestamptz(6)

  @@index([id])
}

model pushed_authorization_request {
  id          String    @id @db.VarChar(50)
  data        Json      @db.Json
  expires_at  DateTime  @db.Timestamptz(6)
  consumed_at DateTime? @db.Timestamptz(6)

  @@index([id])
}

model refresh_token {
  id          String    @id @db.VarChar(50)
  grant_id    String    @db.VarChar(50)
  data        Json      @db.Json
  expires_at  DateTime  @db.Timestamptz(6)
  consumed_at DateTime? @db.Timestamptz(6)

  @@index([grant_id])
  @@index([id])
}

model registration_access_token {
  id          String    @id @db.VarChar(50)
  data        Json      @db.Json
  expires_at  DateTime  @db.Timestamptz(6)
  consumed_at DateTime? @db.Timestamptz(6)

  @@index([id])
}

model replay_detection {
  id          String    @id @db.VarChar(50)
  data        Json      @db.Json
  expires_at  DateTime  @db.Timestamptz(6)
  consumed_at DateTime? @db.Timestamptz(6)

  @@index([id])
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model schemaMigrations {
  version String

  @@ignore
}

model session {
  id          String    @id @db.VarChar(50)
  uid         String    @db.VarChar(50)
  data        Json      @db.Json
  expires_at  DateTime  @db.Timestamptz(6)
  consumed_at DateTime? @db.Timestamptz(6)

  @@index([id])
  @@index([uid])
}
