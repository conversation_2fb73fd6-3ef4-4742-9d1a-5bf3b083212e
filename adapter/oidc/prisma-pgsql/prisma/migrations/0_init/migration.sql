-- CreateTable
CREATE TABLE "access_token" (
    "id" VARCHAR(50) NOT NULL,
    "grant_id" VARCHAR(50) NOT NULL,
    "data" JSON NOT NULL,
    "expires_at" TIMESTAMPTZ(6) NOT NULL,
    "consumed_at" TIMESTAMPTZ(6),

    CONSTRAINT "access_token_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "authorization_code" (
    "id" VARCHAR(50) NOT NULL,
    "grant_id" VARCHAR(50) NOT NULL,
    "data" JSON NOT NULL,
    "expires_at" TIMESTAMPTZ(6) NOT NULL,
    "consumed_at" TIMESTAMPTZ(6),

    CONSTRAINT "authorization_code_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "backchannel_authentication_request" (
    "id" VARCHAR(50) NOT NULL,
    "grant_id" VARCHAR(50) NOT NULL,
    "data" JSON NOT NULL,
    "expires_at" TIMESTAMPTZ(6) NOT NULL,
    "consumed_at" TIMESTAMPTZ(6),

    CONSTRAINT "backchannel_authentication_request_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "client" (
    "id" VARCHAR(50) NOT NULL,
    "data" JSON NOT NULL,
    "expires_at" TIMESTAMPTZ(6) NOT NULL,
    "consumed_at" TIMESTAMPTZ(6),

    CONSTRAINT "client_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "client_credentials" (
    "id" VARCHAR(50) NOT NULL,
    "data" JSON NOT NULL,
    "expires_at" TIMESTAMPTZ(6) NOT NULL,
    "consumed_at" TIMESTAMPTZ(6),

    CONSTRAINT "client_credentials_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "device_code" (
    "id" VARCHAR(50) NOT NULL,
    "user_code" VARCHAR(20) NOT NULL,
    "grant_id" VARCHAR(50) NOT NULL,
    "data" JSON NOT NULL,
    "expires_at" TIMESTAMPTZ(6) NOT NULL,
    "consumed_at" TIMESTAMPTZ(6),

    CONSTRAINT "device_code_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "grants" (
    "id" VARCHAR(50) NOT NULL,
    "data" JSON NOT NULL,
    "expires_at" TIMESTAMPTZ(6) NOT NULL,
    "consumed_at" TIMESTAMPTZ(6),

    CONSTRAINT "grants_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "initial_access_token" (
    "id" VARCHAR(50) NOT NULL,
    "data" JSON NOT NULL,
    "expires_at" TIMESTAMPTZ(6) NOT NULL,
    "consumed_at" TIMESTAMPTZ(6),

    CONSTRAINT "initial_access_token_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "interaction" (
    "id" VARCHAR(50) NOT NULL,
    "data" JSON NOT NULL,
    "expires_at" TIMESTAMPTZ(6) NOT NULL,
    "consumed_at" TIMESTAMPTZ(6),

    CONSTRAINT "interaction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "pushed_authorization_request" (
    "id" VARCHAR(50) NOT NULL,
    "data" JSON NOT NULL,
    "expires_at" TIMESTAMPTZ(6) NOT NULL,
    "consumed_at" TIMESTAMPTZ(6),

    CONSTRAINT "pushed_authorization_request_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "refresh_token" (
    "id" VARCHAR(50) NOT NULL,
    "grant_id" VARCHAR(50) NOT NULL,
    "data" JSON NOT NULL,
    "expires_at" TIMESTAMPTZ(6) NOT NULL,
    "consumed_at" TIMESTAMPTZ(6),

    CONSTRAINT "refresh_token_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "registration_access_token" (
    "id" VARCHAR(50) NOT NULL,
    "data" JSON NOT NULL,
    "expires_at" TIMESTAMPTZ(6) NOT NULL,
    "consumed_at" TIMESTAMPTZ(6),

    CONSTRAINT "registration_access_token_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "replay_detection" (
    "id" VARCHAR(50) NOT NULL,
    "data" JSON NOT NULL,
    "expires_at" TIMESTAMPTZ(6) NOT NULL,
    "consumed_at" TIMESTAMPTZ(6),

    CONSTRAINT "replay_detection_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "schemaMigrations" (
    "version" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "session" (
    "id" VARCHAR(50) NOT NULL,
    "uid" VARCHAR(50) NOT NULL,
    "data" JSON NOT NULL,
    "expires_at" TIMESTAMPTZ(6) NOT NULL,
    "consumed_at" TIMESTAMPTZ(6),

    CONSTRAINT "session_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "access_token_grant_id_idx" ON "access_token"("grant_id");

-- CreateIndex
CREATE INDEX "access_token_id_idx" ON "access_token"("id");

-- CreateIndex
CREATE INDEX "authorization_code_grant_id_idx" ON "authorization_code"("grant_id");

-- CreateIndex
CREATE INDEX "authorization_code_id_idx" ON "authorization_code"("id");

-- CreateIndex
CREATE INDEX "backchannel_authentication_request_grant_id_idx" ON "backchannel_authentication_request"("grant_id");

-- CreateIndex
CREATE INDEX "backchannel_authentication_request_id_idx" ON "backchannel_authentication_request"("id");

-- CreateIndex
CREATE INDEX "client_id_idx" ON "client"("id");

-- CreateIndex
CREATE INDEX "client_credentials_id_idx" ON "client_credentials"("id");

-- CreateIndex
CREATE INDEX "device_code_grant_id_idx" ON "device_code"("grant_id");

-- CreateIndex
CREATE INDEX "device_code_id_idx" ON "device_code"("id");

-- CreateIndex
CREATE INDEX "device_code_user_code_idx" ON "device_code"("user_code");

-- CreateIndex
CREATE INDEX "grants_id_idx" ON "grants"("id");

-- CreateIndex
CREATE INDEX "initial_access_token_id_idx" ON "initial_access_token"("id");

-- CreateIndex
CREATE INDEX "interaction_id_idx" ON "interaction"("id");

-- CreateIndex
CREATE INDEX "pushed_authorization_request_id_idx" ON "pushed_authorization_request"("id");

-- CreateIndex
CREATE INDEX "refresh_token_grant_id_idx" ON "refresh_token"("grant_id");

-- CreateIndex
CREATE INDEX "refresh_token_id_idx" ON "refresh_token"("id");

-- CreateIndex
CREATE INDEX "registration_access_token_id_idx" ON "registration_access_token"("id");

-- CreateIndex
CREATE INDEX "replay_detection_id_idx" ON "replay_detection"("id");

-- CreateIndex
CREATE INDEX "session_id_idx" ON "session"("id");

-- CreateIndex
CREATE INDEX "session_uid_idx" ON "session"("uid");

