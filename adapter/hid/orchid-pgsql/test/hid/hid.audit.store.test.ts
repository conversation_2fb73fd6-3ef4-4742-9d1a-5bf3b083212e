import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  nextTx,
  nextHIDToEmail,
  // @ts-ignore
} from 'haven-hid-domain/mock.js';
import { PgHidAuditStore } from '../../src/hid/hid.audit.store.js';
import { PgHIDStore } from '../../src/hid/hid-store.js';
import { PgHIDToEmailStore } from '../../src/hid/hid-to-email-store.js';
import { getDatabase } from './db.js';

describe('HID Audit tests', async () => {
  const hidAudit = new PgHidAuditStore();
  const hidStore = new PgHIDStore();
  const emailStore = new PgHIDToEmailStore();
  const client = await getDatabase();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('obfuscate the audit record', async () => {
    const tx = nextTx('hid-audit-store-test');
    const hid = await hidStore.next();
    const input = nextHIDToEmail({ hid });
    await emailStore.add(input, tx);

    await hidAudit.obfuscate({
      email: input.email.email,
      hid: input.hid,
    });

    const audit = await lookupRecord(hid);
    expect(audit[0]).toEqual({
      from: 'FORGOTTEN',
      hid: hid,
      hid_audit_id: expect.anything(),
      source: tx.source,
      timestamp: expect.anything(),
      to: 'FORGOTTEN',
      tx: tx.id,
      type: 'email',
    });
  });

  it('obfuscate the audit record without a HID', async () => {
    const tx = nextTx('hid-audit-store-test');
    const hid = await hidStore.next();
    const input = nextHIDToEmail({ hid });
    await emailStore.add(input, tx);

    await hidAudit.obfuscate({
      email: input.email.email,
    });
    const audit = await lookupRecord(hid);
    expect(audit[0]).toEqual({
      from: 'FORGOTTEN',
      hid: hid,
      hid_audit_id: expect.anything(),
      source: tx.source,
      timestamp: expect.anything(),
      to: 'FORGOTTEN',
      tx: tx.id,
      type: 'email',
    });
  });

  it('obfuscate the emailverified audit record without a HID', async () => {
    const tx = nextTx('hid-audit-store-test');
    const hid = await hidStore.next();
    const input = nextHIDToEmail({
      hid,
      emailVerified: true,
    });
    await emailStore.add(input, tx);

    await hidAudit.obfuscate({
      email: input.email.email,
    });
    const audit = await lookupRecord(hid);
    expect(audit.length).toEqual(2);
    expect(audit[0]).toEqual({
      from: 'FORGOTTEN',
      hid: hid,
      hid_audit_id: expect.anything(),
      source: tx.source,
      timestamp: expect.anything(),
      to: 'FORGOTTEN',
      tx: tx.id,
      type: 'email',
    });
    expect(audit[1]).toEqual({
      from: 'FORGOTTEN',
      hid: hid,
      hid_audit_id: expect.anything(),
      source: tx.source,
      timestamp: expect.anything(),
      to: 'FORGOTTEN',
      tx: tx.id,
      type: 'email_verified',
    });
  });

  const lookupRecord = async (hid: string) => {
    const actual = await client.query(`SELECT *
                                       FROM HID_AUDIT
                                       WHERE hid = '${hid}';`);
    return actual.rows;
  };
});
