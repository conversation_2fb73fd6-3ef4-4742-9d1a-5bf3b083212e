import { describe, it, expect, vi, beforeEach } from 'vitest';
import { nextValidEmail, nextHID } from 'haven-hid-domain/mock.js';
import { PgSecurityAuditStore } from '../../src/hid/security.audit.store.js';
import { SecurityAudit } from 'haven-hid-domain';
import { getDatabase } from './db.js';
import { logger } from '@havenengineering/module-haven-logging';

const warn = vi.spyOn(logger, 'warn');
const error = vi.spyOn(logger, 'error');

describe('Security Audit tests', async () => {
  const securityAudit = new PgSecurityAuditStore();
  const client = await getDatabase();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('stores the audit record', async () => {
    const hid = nextHID();
    const email = nextValidEmail().email;
    const record: SecurityAudit = {
      hid,
      email,
      source: 'source',
      reason: 'reason',
      ipAddress: '127.0.0.1',
      clientId: 'client-id',
      userAgent: 'Chrome',
    };
    await securityAudit.add(record);

    const stored = await lookupRecord(record.email);
    expect(stored.length).toBe(1);
    expect(stored[0]).toEqual({
      email: record.email,
      hid: record.hid,
      source: record.source,
      reason: record.reason,
      user_agent: record.userAgent,
      ip_address: record.ipAddress,
      timestamp: expect.anything(),
      client_id: record.clientId,
      id: expect.anything(),
    });
    expect(stored[0].timestamp).not.toBeUndefined();
    expect(error).not.toHaveBeenCalled();
    expect(warn).toHaveBeenCalledWith(`source: ${record.source} | reason: ${record.reason}`, {
      client: record.clientId,
      source: record.source,
      ip: record.ipAddress,
      ua: record.userAgent,
    });
  });

  it('stores the audit record without a HID', async () => {
    const email = nextValidEmail().email;
    const record: SecurityAudit = {
      email,
      source: 'source',
      reason: 'reason',
      ipAddress: '127.0.0.1',
      clientId: 'client-id',
      userAgent: 'Chrome',
      hid: undefined,
    };
    await securityAudit.add(record);

    const stored = await lookupRecord(record.email);
    expect(stored.length).toBe(1);
    expect(stored[0].hid).toBeNull();
  });

  it('trims excessively long userAgents', async () => {
    const email = nextValidEmail().email;
    const record: SecurityAudit = {
      email,
      source: 'source',
      reason: 'reason',
      ipAddress: '127.0.0.1',
      clientId: 'client-id',
      userAgent: 'x'.repeat(256),
      hid: undefined,
    };
    await securityAudit.add(record);

    const stored = await lookupRecord(record.email);
    expect(stored.length).toBe(1);
    expect(stored[0].user_agent.length).toEqual(255);
    expect(stored[0].user_agent).toEqual(record.userAgent.substring(0, 255));
  });

  it('exception recording audit is logged but ignored', async () => {
    const email = nextValidEmail().email;
    await securityAudit.add({
      email,
      source: 'source',
    } as SecurityAudit);
    expect(warn).toHaveBeenCalledWith(`source: source | reason: undefined`, {
      source: 'source',
      client: undefined,
      ip: undefined,
      ua: undefined,
    });
    expect(error).toHaveBeenCalledWith(
      'Could not save security audit',
      new TypeError("Cannot read properties of undefined (reading 'substring')"),
    );
  });

  const lookupRecord = async (email: string) => {
    const actual = await client.query(`SELECT *
                                       FROM SECURITY_AUDIT
                                       WHERE email = '${email}';`);
    return actual.rows;
  };
});
