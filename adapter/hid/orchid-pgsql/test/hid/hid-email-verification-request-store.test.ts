// @ts-ignore
import { nextHID, nextHIDToPassword, nextToken, nextTx } from 'haven-hid-domain/mock.js';
import { describe, expect, it } from 'vitest';
import { PgHIDEmailVerificationRequestStore } from '../../src/hid/hid-email-verification-request-store.js';
import casual from 'casual';
import { EmailVerificationTokenType, HIDEmailVerificationRequest } from 'haven-hid-domain';

describe('email verification request scenarios', async () => {
  const store = new PgHIDEmailVerificationRequestStore();

  it('store and find request token', async () => {
    const request = nextVerificationRequest();

    await store.add(request);
    const actual = await store.find(request.token);

    expect(actual).toMatchObject(request);
  });

  it('cannot find token when token is undefined', async () => {
    const request = nextVerificationRequest();

    await store.add(request);

    // @ts-ignore
    expect(await store.find(undefined)).toBeNull();
  });

  it('cannot find token when token is null', async () => {
    const request = nextVerificationRequest();

    await store.add(request);

    // @ts-ignore
    expect(await store.find(null)).toBeNull();
  });

  it('cannot find token when token is empty string', async () => {
    const request = nextVerificationRequest();

    await store.add(request);

    expect(await store.find('')).toBeNull();
  });

  it('sets created_at to now', async () => {
    const before = Date.now() - 1000;
    const request = nextVerificationRequest();
    await store.add(request);
    const actual = await store.find(request.token);

    // the 1000 is a fiddle to allow for small differences in database time
    const after = Date.now() + 1000;

    expect(after).toBeGreaterThan(before);
    expect(actual?.createdAt.getTime()).toBeGreaterThanOrEqual(before);
    expect(actual?.createdAt.getTime()).toBeLessThanOrEqual(after);
  });

  it('cannot add duplicate tokens', async () => {
    const request = nextVerificationRequest();

    await store.add(request);
    await expect(async () => await store.add(request)).rejects.toThrow(
      'Error creating email verification request',
    );
  });

  it('can delete token for hid', async () => {
    const request = {
      token: nextToken(),
      hid: nextHID(),
      returnUrl: casual.url,
      type: EmailVerificationTokenType.PASSWORD_RESET,
    };
    await store.add(request);

    const actual = await store.deleteAll(request.hid, EmailVerificationTokenType.PASSWORD_RESET);
    expect(actual).toEqual(1);

    const missing = await store.find(request.token);
    expect(missing).toBeNull();
  });

  it('can delete all tokens of a type associated with a hid', async () => {
    const resetPassword = {
      hid: nextHID(),
      returnUrl: casual.url,
      type: EmailVerificationTokenType.PASSWORD_RESET,
    };

    const request1 = {
      ...resetPassword,
      token: nextToken(),
    };
    const request2 = {
      ...resetPassword,
      token: nextToken(),
    };
    const nonPasswordRequest = {
      ...resetPassword,
      type: EmailVerificationTokenType.EMAIL,
      token: nextToken(),
    };

    await store.add(request1);
    await store.add(request2);
    await store.add(nonPasswordRequest);

    const actual = await store.deleteAll(
      resetPassword.hid,
      EmailVerificationTokenType.PASSWORD_RESET,
    );
    expect(actual).toEqual(2);
    expect(await store.find(request1.token)).toBeNull();
    expect(await store.find(request2.token)).toBeNull();
    expect(await store.find(nonPasswordRequest.token)).not.toBeNull();
  });
});

const nextVerificationRequest = (
  args: Partial<HIDEmailVerificationRequest> = {},
): HIDEmailVerificationRequest => ({
  token: nextToken(),
  hid: nextHID(),
  returnUrl: casual.url,
  type: EmailVerificationTokenType.PASSWORD_RESET,
  ...args,
});
