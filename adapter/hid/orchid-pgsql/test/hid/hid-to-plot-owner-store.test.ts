import {
  HID,
  Transaction,
  HIDToPlotOwner,
  PlotOwnerId,
  RTBF_TRANSACTION_SOURCE_SUFFIX,
} from 'haven-hid-domain';
// @ts-ignore
import { nextHIDToPlotOwner, nextTx } from 'haven-hid-domain/mock.js';
import * as pgsql from 'pg';
import { beforeEach, describe, expect, it } from 'vitest';
import { PgHIDStore } from '../../src/hid/hid-store.js';
import { PgHIDToPlotOwnerStore } from '../../src/hid/hid-to-plot-owner-store.js';
import { getDatabase } from './db.js';

interface HIDStoreContext {
  store: PgHIDToPlotOwnerStore;
  hidStore: PgHIDStore;
  client: pgsql.Pool;
  hid: HID;
  tx: Transaction;
}

describe('hid-to-plot-owner store scenarios', () => {
  beforeEach<HIDStoreContext>(async (context) => {
    context.hidStore = new PgHIDStore();
    context.hid = await context.hidStore.next();
    context.store = new PgHIDToPlotOwnerStore();
    context.client = await getDatabase();
    context.tx = nextTx('hid-to-plot-owner-store');
  });

  describe('hid-to-plot-owner add and find scenarios', () => {
    it<HIDStoreContext>('store a plot owner against a hid', async ({ store, client, hid, tx }) => {
      const input = nextHIDToPlotOwner({
        hid: hid,
      });

      await store.add(input, tx);

      const actual = await directGetByHID(client, input.hid);
      expect(actual).toMatchObject(input);
    });

    it<HIDStoreContext>('get hid for plot owner', async ({ store, client, hid, tx }) => {
      const input = nextHIDToPlotOwner({
        hid: hid,
      });
      await client.query(
        `INSERT INTO HID_TO_PLOT_OWNER (hid, plot_owner_id) VALUES ('${input.hid}', '${input.plotOwnerId}');`,
      );

      const result = await store.findByPlotOwnerId(input.plotOwnerId);
      expect(result).toStrictEqual(input);
    });

    it<HIDStoreContext>('add fails if hid does not exist', async ({ store, tx }) => {
      const input = nextHIDToPlotOwner({
        hid: 'DOESNOTEXIST',
      });
      try {
        await store.add(input, tx);
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
  });

  describe('hid-to-plot-owner update scenarios', () => {
    it<HIDStoreContext>('update hid for plot_owner_id', async ({ store, client, hid, tx }) => {
      const original = nextHIDToPlotOwner({
        hid: hid,
      });
      await client.query(
        `INSERT INTO HID_TO_PLOT_OWNER (hid, plot_owner_id) VALUES ('${original.hid}', '${original.plotOwnerId}');`,
      );

      const update = nextHIDToPlotOwner({
        hid: original.hid,
      });
      await store.update(update, tx);

      const actual = await directGetByHID(client, update.hid);
      expect(actual).toStrictEqual(update);
    });

    it<HIDStoreContext>('update plot_owner_id for hid', async ({
      store,
      client,
      hid,
      hidStore,
      tx,
    }) => {
      const original = nextHIDToPlotOwner({
        hid: hid,
      });
      await client.query(
        `INSERT INTO HID_TO_PLOT_OWNER (hid, plot_owner_id) VALUES ('${original.hid}', '${original.plotOwnerId}');`,
      );

      const otherHid = await hidStore.next();
      const update = {
        hid: otherHid,
        plotOwnerId: original.plotOwnerId,
      };
      await store.update(update, tx);

      const actual = await directGetByHID(client, otherHid);
      expect(actual).toStrictEqual(update);
    });

    it<HIDStoreContext>('update fails if hid and plot_owner_id are the same', async ({
      store,
      client,
      hid,
      tx,
    }) => {
      const original = nextHIDToPlotOwner({
        hid: hid,
      });
      await client.query(
        `INSERT INTO HID_TO_PLOT_OWNER (hid, plot_owner_id) VALUES ('${original.hid}', '${original.plotOwnerId}');`,
      );

      try {
        await store.update(original, tx);
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });

    it<HIDStoreContext>('update fails if neither hid nor plot_owner_id are found', async ({
      store,
      hid,
      tx,
    }) => {
      const original = nextHIDToPlotOwner({
        hid: hid,
      });

      try {
        await store.update(original, tx);
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });

    it<HIDStoreContext>('update fails if hid does not exist', async ({
      store,
      client,
      hid,
      tx,
    }) => {
      const update = nextHIDToPlotOwner({
        hid: 'DOESNOTEXIST',
      });
      try {
        await store.update(update, tx);
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
  });

  describe('hid-to-plot-owner delete scenarios', () => {
    it<HIDStoreContext>('delete plot owner id', async ({ store, client, hid, tx }) => {
      const input = nextHIDToPlotOwner({
        hid: hid,
      });
      await client.query(
        `INSERT INTO HID_TO_PLOT_OWNER (hid, plot_owner_id) VALUES ('${input.hid}', '${input.plotOwnerId}');`,
      );

      expect(await store.delete(input, tx)).toBeTruthy();
      await expectHIDAndPlotOwnerIdToNotExist(client, input);
    });

    it<HIDStoreContext>('delete non-existent plot owner id', async ({ store, hid, tx }) => {
      const input = nextHIDToPlotOwner({
        hid: hid,
      });

      expect(await store.delete(input, tx)).toBeFalsy();
    });
  });

  describe('auditing', () => {
    it<HIDStoreContext>('add is audited', async ({ store, client, hid, tx }) => {
      const input = nextHIDToPlotOwner({
        hid: hid,
      });

      await store.add(input, tx);

      expect(await store.audit.findAllByTransaction(tx)).toMatchObject([
        {
          tx: tx.id,
          hid: input.hid,
          to: input.plotOwnerId,
          from: null,
          type: 'plot_owner',
        },
      ]);
    });

    it<HIDStoreContext>('change plot owner is audited', async ({
      hidStore,
      store,
      client,
      hid,
      tx,
    }) => {
      const original = nextHIDToPlotOwner({
        hid: hid,
      });
      await client.query(
        `INSERT INTO HID_TO_PLOT_OWNER (hid, plot_owner_id) VALUES ('${original.hid}', '${original.plotOwnerId}');`,
      );

      const update = nextHIDToPlotOwner({
        hid: original.hid,
      });
      await store.update(update, tx);

      expect(await store.audit.findAllByTransaction(tx)).toMatchObject([
        {
          tx: tx.id,
          hid: original.hid,
          to: update.plotOwnerId,
          from: original.plotOwnerId,
          type: 'plot_owner',
        },
      ]);
    });

    it<HIDStoreContext>('change hid is audited', async ({ hidStore, store, client, hid, tx }) => {
      const original = nextHIDToPlotOwner({
        hid: hid,
      });
      const update = nextHIDToPlotOwner({
        plotOwnerId: original.plotOwnerId,
      });
      await client.query(
        `INSERT INTO HID_TO_PLOT_OWNER (hid, plot_owner_id) VALUES ('${original.hid}', '${original.plotOwnerId}');`,
      );
      await client.query(`INSERT INTO HID (hid) VALUES ('${update.hid}');`);

      await store.update(update, tx);

      expect(await store.audit.findAllByTransaction(tx)).toMatchObject([
        {
          tx: tx.id,
          hid: original.hid,
          to: null,
          from: original.plotOwnerId,
          type: 'plot_owner',
        },
        {
          tx: tx.id,
          hid: update.hid,
          to: original.plotOwnerId,
          from: null,
          type: 'plot_owner',
        },
      ]);
    });

    it<HIDStoreContext>('delete is audited', async ({ store, client, hid, tx }) => {
      const input = nextHIDToPlotOwner({
        hid: hid,
      });
      await client.query(
        `INSERT INTO HID_TO_PLOT_OWNER (hid, plot_owner_id) VALUES ('${input.hid}', '${input.plotOwnerId}');`,
      );

      await store.delete(input, tx);

      expect(await store.audit.findAllByTransaction(tx)).toMatchObject([
        {
          tx: tx.id,
          source: tx.source,
          hid: hid,
          from: input.plotOwnerId,
          to: null,
          type: 'plot_owner',
        },
      ]);
    });

    it<HIDStoreContext>('delete is not audited if nothing happens', async ({
      store,
      client,
      hid,
      tx,
    }) => {
      const input = nextHIDToPlotOwner();
      await store.delete(input, tx);

      expect(await store.audit.findAllByTransaction(tx)).toMatchObject([]);
    });

    it<HIDStoreContext>('delete is not audited if transaction source is RTBF', async ({
      store,
      client,
      hid,
      tx,
    }) => {
      const input = nextHIDToPlotOwner({
        hid: hid,
      });
      await client.query(
        `INSERT INTO HID_TO_PLOT_OWNER (hid, plot_owner_id) VALUES ('${input.hid}', '${input.plotOwnerId}');`,
      );
      const rtbfTx = nextTx(RTBF_TRANSACTION_SOURCE_SUFFIX);
      await store.delete(input, rtbfTx);

      expect(await store.audit.findAllByTransaction(rtbfTx)).toMatchObject([]);
    });
  });
});

const directGetByHID = async (client: pgsql.Pool, hid: HID): Promise<HIDToPlotOwner> => {
  const actual = await client.query(
    `SELECT hid, plot_owner_id FROM HID_TO_PLOT_OWNER WHERE hid = '${hid}';`,
  );
  expect(actual.rowCount).toBe(1);
  const row = actual.rows[0];
  return {
    hid: row.hid,
    plotOwnerId: row.plot_owner_id,
  };
};

const directHIDDoesNotExist = async (client: pgsql.Pool, hid: HID): Promise<any> => {
  const actual = await client.query(
    `SELECT hid, plot_owner_id FROM HID_TO_PLOT_OWNER WHERE hid = '${hid}';`,
  );
  expect(actual.rowCount).toBe(0);
};

const directPlotOwnerIdDoesNotExist = async (
  client: pgsql.Pool,
  plotOwnerId: PlotOwnerId,
): Promise<any> => {
  const actual = await client.query(
    `SELECT hid, plot_owner_id FROM HID_TO_PLOT_OWNER WHERE plot_owner_id = '${plotOwnerId}';`,
  );
  expect(actual.rowCount).toBe(0);
};

const expectHIDAndPlotOwnerIdToNotExist = async (
  client: pgsql.Pool,
  input: HIDToPlotOwner,
): Promise<any> => {
  await directHIDDoesNotExist(client, input.hid);
  await directPlotOwnerIdDoesNotExist(client, input.plotOwnerId);
};
