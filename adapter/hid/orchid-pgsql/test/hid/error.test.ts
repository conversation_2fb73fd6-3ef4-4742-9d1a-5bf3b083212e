import { beforeEach, describe, expect, it, vi } from 'vitest';
import { toSystemError } from '../../src/hid/error.js';
import { logger } from '@havenengineering/module-haven-logging';
import { SystemError } from 'haven-hid-domain';

describe('error conversion from DB errors', () => {
  const actualLogger = vi.spyOn(logger, 'error');

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should transform db errors', async () => {
    const dbError = {
      message: 'some db error',
      query: { db: 'internals' },
    };
    const systemError = toSystemError('error message')(dbError);

    expect(systemError.message).toEqual('error message');
    // @ts-ignore -- not sure why this complains in IDE
    expect(systemError.cause).toEqual(dbError.message);
    expect(actualLogger).toHaveBeenCalledWith('error message: some db error', {
      cause: { message: 'some db error' },
    });
  });

  it('should keep non db errors as is', async () => {
    const error = new Error('some error');
    const systemError = toSystemError('error message')(error);

    expect(systemError.message).toEqual('error message');
    // @ts-ignore -- not sure why this complains in IDE
    expect(systemError.cause).toEqual(error);
    expect(actualLogger).toHaveBeenCalledWith('error message', error);
  });

  it('system errors do not need converting', async () => {
    const error = new SystemError('some error');
    const systemError = toSystemError('error message')(error);

    expect(systemError).toBe(error);
    expect(actualLogger).not.toHaveBeenCalled();
  });
});
