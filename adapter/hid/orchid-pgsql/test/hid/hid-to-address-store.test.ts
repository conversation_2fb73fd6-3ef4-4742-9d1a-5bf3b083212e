import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>dd<PERSON>, H<PERSON>ToAddress, Transaction } from 'haven-hid-domain';
import * as pgsql from 'pg';
import { afterEach, beforeEach, describe, expect, it } from 'vitest';
import { PgHIDStore } from '../../src/hid/hid-store.js';
import { PgHIDToAddressStore } from '../../src/hid/hid-to-address-store.js';
import { getDatabase } from './db.js';
import MockDate from 'mockdate';
import { nextTx } from 'haven-hid-domain/mock.js';

interface HIDAddressStoreContext {
  store: PgHIDToAddressStore;
  client: pgsql.Pool;
  hid: HID;
  hidStore: PgHIDStore;
  older: HIDToAddress;
  newer: HIDToAddress;
  tx: Transaction;
}

const NOW = new Date();

describe('hid-to-address store scenarios', async () => {
  beforeEach<HIDAddressStoreContext>(async (context) => {
    context.client = await getDatabase();
    context.store = new PgHIDToAddressStore();
    context.hidStore = new PgHIDStore();
    context.hid = await context.hidStore.next();
    context.tx = nextTx('hid-to-address-store');

    context.older = {
      hid: context.hid,
      address_line1: '221b Baker Street',
      address_city: 'Marylebone',
      address_county: 'London',
      address_country: 'GB',
      address_postcode: 'NW1 6XE',
      address_effective_from: new Date('2022-02-02T10:00:00.000Z'),
    };

    context.newer = {
      hid: context.hid,
      address_line1: 'Flat 5',
      address_line2: 'Apollo House',
      address_city: 'Croydon',
      address_county: 'London',
      address_country: 'GB',
      address_postcode: 'CR0 9YA',
      address_effective_from: new Date('2025-02-26T16:00:00.000Z'),
    };

    await context.store.deleteAll('I hate all addresses');

    MockDate.set(NOW);
  });

  afterEach(() => {
    MockDate.reset();
  });

  describe('hid-to-address add and find scenarios', () => {
    it<HIDAddressStoreContext>('store and fetch an address for a hid', async ({
      store,
      hid,
      older,
      tx,
    }) => {
      await store.add(older, tx);
      const found: HIDToAddress | null = await store.findCurrentAddressForHID(hid);
      expect(found).toEqual(expect.objectContaining(older));
    });

    it<HIDAddressStoreContext>('throws for invalid hid', async ({ store }) => {
      await expect(() => store.findCurrentAddressForHID('')).rejects.toThrowError(/undefined hid/);
      await expect(() => store.findCurrentAddressForHID(null as any)).rejects.toThrowError(
        /undefined hid/,
      );
    });

    it<HIDAddressStoreContext>('returns null for non-existent hid', async ({ store }) => {
      const found = await store.findCurrentAddressForHID('UNKNOWN');
      expect(found).toBeNull();
    });

    it<HIDAddressStoreContext>('fetch most recent address for a hid', async ({
      store,
      hid,
      older,
      newer,
      tx,
    }) => {
      await store.add(older, tx);
      await store.add(newer, tx);

      const found: HIDToAddress | undefined | null = await store.findCurrentAddressForHID(hid);
      expect(found).toEqual(expect.objectContaining(newer));
    });

    it<HIDAddressStoreContext>('fetch/delete specific addresses for a hid', async ({
      store,
      hid,
      older,
      newer,
      tx,
    }) => {
      await store.add(older, tx);

      const foundOlder: HIDToAddress | null = await store.findCurrentAddressForHID(hid);
      expect(foundOlder).toEqual(expect.objectContaining(older));

      await store.add(newer, tx);

      const foundNewer: HIDToAddress | null = await store.findCurrentAddressForHID(hid);
      expect(foundNewer).toEqual(expect.objectContaining(newer));

      expect((await store.findAllAddressesForHID(hid)).length).toEqual(2);

      if (foundNewer) await store.deleteAddress(foundNewer, tx);

      expect(await store.findCurrentAddressForHID(hid)).toEqual(expect.objectContaining(older));

      expect((await store.findAllAddressesForHID(hid)).length).toEqual(1);
    });

    it<HIDAddressStoreContext>('delete all addresses for a hid', async ({
      store,
      hid,
      older,
      newer,
      tx,
    }) => {
      await store.add(older, tx);
      await store.add(newer, tx);

      expect((await store.findAllAddressesForHID(hid)).length).toEqual(2);

      await store.deleteAllAddressesForHID(hid, tx);

      expect((await store.findAllAddressesForHID(hid)).length).toEqual(0);
    });
  });
});
