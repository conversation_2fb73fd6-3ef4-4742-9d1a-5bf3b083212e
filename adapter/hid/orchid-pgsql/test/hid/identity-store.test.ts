import { describe, it, expect, vi, Mock } from 'vitest';
import { PgHavenIdentityTransactionalStore } from '../../src/hid/hid.out.pgsql.js';
// @ts-ignore
import { nextIdentity, nextTx, nextValidEmail, nextHIDToEmail } from 'haven-hid-domain/mock.js';
import { PgHIDToEmailStore } from '../../src/hid/hid-to-email-store.js';
import { PgHIDStore } from '../../src/hid/hid-store.js';
import { PgHIDToPasswordStore } from '../../src/hid/hid-to-password-store.js';

vi.mock('../../src/hid/hid-to-password-store.js', async () => {
  const mod = await vi.importActual<typeof import('../../src/hid/hid-to-password-store.js')>(
    '../../src/hid/hid-to-password-store.js',
  );
  const original = mod.PgHIDToPasswordStore.prototype.add;
  mod.PgHIDToPasswordStore.prototype.add = vi.fn().mockImplementation(original);
  return {
    ...mod,
  };
});

describe('IDENTITY test with PG stores', () => {
  const identity = PgHavenIdentityTransactionalStore();
  const emailStore = new PgHIDToEmailStore();

  it('returns undefined for unknown id', async () => {
    const actual = await identity.findById('xxx');
    expect(actual).toBeUndefined();
  });

  it('returns undefined for unknown email', async () => {
    const hidStore = new PgHIDStore();
    const hid = await hidStore.next();
    const input = nextHIDToEmail({ hid });
    await emailStore.add(input, nextTx());

    const actual = await identity.findByEmail(input.email);
    expect(actual).toBeUndefined();
  });

  it('returns undefined for known email but unregistered', async () => {
    const actual = await identity.findByEmail(nextValidEmail());
    expect(actual).toBeUndefined();
  });

  it('accounts can be created', async () => {
    const expected = nextIdentity();
    const actual = await identity.createIdentity(expected, nextTx());
    expect(actual).toEqual({
      hid: expect.anything(),
      ...expected,
    });
  });

  it('errors in storing password when creating identity rolls back changes', async () => {
    (PgHIDToPasswordStore.prototype.add as Mock).mockRejectedValueOnce(new Error('test error'));
    const expected = nextIdentity({
      title: '',
      firstName: '',
    });
    try {
      await identity.createIdentity(expected, nextTx());
    } catch (error: any) {
      expect(error.message).toMatch('test error');
    }

    const emailStore = new PgHIDToEmailStore();
    const result = await emailStore.findByEmail(expected.email);
    expect(result).toEqual(undefined);
  });

  it('accounts can be found using id', async () => {
    const profile = nextIdentity({
      emailVerified: false,
    }); // TODO - need to store emailverified
    const created = await identity.createIdentity(profile, nextTx());
    const actual = await identity.findById(created.hid);
    expect(actual).toEqual(created);
  });

  it('accounts can be found using email', async () => {
    const profile = nextIdentity({
      emailVerified: false,
    }); // TODO - need to store emailverified
    const created = await identity.createIdentity(profile, nextTx());
    const actual = await identity.findByEmail(created.email);
    expect(actual).toEqual(created);
  });

  it('accounts can be created for federated identity', async () => {
    const profile = nextIdentity();
    const created = await identity.findOrCreateFederatedIdentity(
      'google',
      {
        sub: 'google-id',
        email: profile.email.email,
        name: 'Fred Smith',
        given_name: 'Freddy',
        family_name: 'Smith third',
      },
      nextTx(),
    );
    expect(created).toEqual({
      hid: expect.anything(),
      email: profile.email,
      emailVerified: true,
      name: 'Fred Smith',
      lastName: 'Smith third',
      firstName: 'Freddy',
      title: '',
      accounts: [],
    });
  });

  it('existing accounts with matching email can be authenticated via trusted federated identity', async () => {
    const profile = nextIdentity();
    const created = await identity.createIdentity(profile, nextTx());
    const found = await identity.findOrCreateFederatedIdentity(
      'google',
      {
        sub: 'google-id',
        email: profile.email.email,
        name: 'Fred Smith',
      },
      nextTx(),
    );

    expect(found).toEqual({
      ...created,
      emailVerified: true,
    });

    const foundAgain = await identity.findByEmail(profile.email);
    expect(foundAgain).toEqual({
      ...found,
    });
  });
});
