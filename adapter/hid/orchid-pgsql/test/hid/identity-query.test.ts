import { HavenServiceAccountIdType, HID, HIDAddress, Transaction } from 'haven-hid-domain';
// @ts-ignore
import {
  nextIdentity,
  nextPlotOwnerId,
  nextSeaWareClientId,
  nextTx,
} from 'haven-hid-domain/mock.js';
import { PasswordStrength } from '@havenengineering/module-shared-password-strength';
import { beforeEach, describe, expect, it } from 'vitest';
import { PgHIDStore } from '../../src/hid/hid-store.js';
import { PgIdentityQuery } from '../../src/hid/identity-query.js';
import { PgHIDToEmailStore } from '../../src/hid/hid-to-email-store.js';
import { PgHIDToPasswordStore } from '../../src/hid/hid-to-password-store.js';
import { PgHIDToProfileStore } from '../../src/hid/hid-to-profile-store.js';
import { PgHIDToSeaWareClientStore } from '../../src/hid/hid-to-seaware-client-store.js';
import { PgHIDToPlotOwnerStore } from '../../src/hid/hid-to-plot-owner-store.js';
import { PgHIDToAddressStore } from '../../src/hid/hid-to-address-store.js';

interface TestContext {
  hid: HID;
  tx: Transaction;
}

describe('identity query scenarios', async () => {
  const store = new PgIdentityQuery();
  const emailStore = new PgHIDToEmailStore();
  const passwordStore = new PgHIDToPasswordStore();
  const profileStore = new PgHIDToProfileStore();
  const addressStore = new PgHIDToAddressStore();
  const hidStore = new PgHIDStore();
  const seawareStore = new PgHIDToSeaWareClientStore();
  const plotStore = new PgHIDToPlotOwnerStore();

  beforeEach<TestContext>(async (context) => {
    context.hid = await hidStore.next();
    context.tx = nextTx('hid-to-seaware-client-store');
  });

  describe('identity-query', () => {
    it<TestContext>('identity can be found by HID', async ({ hid, tx }) => {
      const expected = nextIdentity({
        accounts: [],
      });

      await emailStore.add(
        {
          hid: hid,
          email: expected.email,
          updatedAt: undefined,
        },
        tx,
      );

      await passwordStore.add(
        {
          hid: hid,
          hash: expected.passwordHash as string,
          strength: expected.passwordStrength as PasswordStrength,
        },
        tx,
      );
      await profileStore.add({ hid: hid, ...expected }, tx);

      const identity = await store.findById(hid);

      expect(identity).toEqual({
        hid: hid,
        ...expected,
        emailVerified: false,
      });
    });

    it<TestContext>('identity can be found by email', async ({ hid, tx }) => {
      const expected = nextIdentity({
        accounts: [],
        emailVerified: false,
      });

      await emailStore.add(
        {
          hid: hid,
          email: expected.email,
          updatedAt: undefined,
        },
        tx,
      );
      await passwordStore.add(
        {
          hid: hid,
          hash: expected.passwordHash as string,
          strength: expected.passwordStrength as PasswordStrength,
        },
        tx,
      );
      await profileStore.add({ hid: hid, ...expected }, tx);

      const identity = await store.findByEmail(expected.email);

      expect(identity).toEqual({
        hid: hid,
        ...expected,
        emailVerified: false,
      });
    });

    it<TestContext>('identity returns undefined if only hid-to-email exists', async ({
      hid,
      tx,
    }) => {
      const expected = nextIdentity();

      await emailStore.add(
        {
          hid: hid,
          email: expected.email,
          updatedAt: undefined,
        },
        tx,
      );

      const identity = await store.findByEmail(expected.email);

      expect(identity).toBeUndefined();
    });

    it<TestContext>('identity returns both owner and seaware associated accounts', async ({
      hid,
      tx,
    }) => {
      const plotId = nextPlotOwnerId();
      const seawareId = nextSeaWareClientId();
      const expected = nextIdentity({
        accounts: [
          {
            id: seawareId,
            type: HavenServiceAccountIdType.SEAWARE_CLIENT_ID,
          },
          {
            id: plotId,
            type: HavenServiceAccountIdType.OWNER_ID,
          },
        ],
      });

      await emailStore.add(
        {
          hid: hid,
          email: expected.email,
          updatedAt: undefined,
        },
        tx,
      );
      await passwordStore.add(
        {
          hid: hid,
          hash: expected.passwordHash as string,
          strength: expected.passwordStrength as PasswordStrength,
        },
        tx,
      );
      await profileStore.add({ hid: hid, ...expected }, tx);
      await seawareStore.add(
        {
          hid: hid,
          seaWareClientId: seawareId,
        },
        tx,
      );
      await plotStore.add({ hid: hid, plotOwnerId: plotId }, tx);

      const identity = await store.findByEmail(expected.email);

      expect(identity).toEqual({
        hid: hid,
        ...expected,
        emailVerified: false, // NOT supported yet
      });
    });

    it<TestContext>('password might not be stored for social signups', async ({ hid, tx }) => {
      // @ts-ignore
      const expected = nextIdentity({
        emailVerified: true,
        passwordHash: undefined,
        passwordStrength: undefined,
        accounts: [],
      });

      await emailStore.add(
        {
          hid: hid,
          email: expected.email,
          emailVerified: expected.emailVerified,
          updatedAt: undefined,
        },
        tx,
      );
      await profileStore.add({ hid: hid, ...expected }, tx);

      const identity = await store.findByEmail(expected.email);

      expect(identity).toEqual({
        hid: hid,
        ...expected,
        emailVerified: true,
      });
    });

    it<TestContext>('can search for migrated users', async ({ hid, tx }) => {
      const expected = nextIdentity();
      const plotId = nextPlotOwnerId();
      await emailStore.add(
        {
          hid: hid,
          email: expected.email,
          emailVerified: expected.emailVerified,
          updatedAt: undefined,
        },
        tx,
      );
      await profileStore.add({ hid: hid, ...expected }, tx);
      await plotStore.add({ hid: hid, plotOwnerId: plotId }, tx);
      await passwordStore.add(
        {
          hid: hid,
          hash: 'xxxx',
          strength: PasswordStrength.STRONG,
        },
        tx,
      );

      const actual = await store.search({
        email: expected.email.email,
      });

      expect(actual.length).toEqual(1);
      expect(actual[0]).toEqual({
        accounts: [{ id: plotId, type: 'owner_id' }],
        email: expected.email,
        emailVerified: expected.emailVerified,
        firstName: expected.firstName,
        hid: hid,
        lastName: expected.lastName,
        phoneNumber: expected.phoneNumber,
        title: expected.title,
        migrated: true,
      });
    });

    it<TestContext>('can search for unmigrated users', async ({ hid, tx }) => {
      const expected = nextIdentity();
      const seawareId = nextSeaWareClientId();

      await emailStore.add(
        {
          hid: hid,
          email: expected.email,
          emailVerified: expected.emailVerified,
          updatedAt: undefined,
        },
        tx,
      );

      await seawareStore.add(
        {
          hid: hid,
          seaWareClientId: seawareId,
        },
        tx,
      );

      const actual = await store.search({
        email: expected.email.email,
      });

      expect(actual.length).toEqual(1);
      expect(actual[0]).toEqual({
        accounts: [
          {
            id: seawareId,
            type: 'seaware_client_id',
          },
        ],
        email: expected.email,
        emailVerified: expected.emailVerified,
        firstName: null,
        hid: hid,
        lastName: null,
        phoneNumber: null,
        title: null,
        migrated: false,
      });
    });
  });

  describe('address handling', () => {
    const mockAddress: HIDAddress = {
      address_line1: '1',
      address_line2: 'Park Ln',
      address_city: 'Hemel Hempstead',
      address_county: 'Hertfordshire',
      address_postcode: 'HP2 4YJ',
      address_country: 'GB',
    };

    it<TestContext>('find includes address when address exists', async ({ hid, tx }) => {
      const expected = nextIdentity({
        accounts: [],
      });

      await emailStore.add(
        {
          hid: hid,
          email: expected.email,
          updatedAt: undefined,
        },
        tx,
      );

      await passwordStore.add(
        {
          hid: hid,
          hash: expected.passwordHash as string,
          strength: expected.passwordStrength as PasswordStrength,
        },
        tx,
      );

      await profileStore.add({ hid: hid, ...expected }, tx);

      await addressStore.add(
        {
          hid: hid,
          ...mockAddress,
        },
        tx,
      );

      const identity = await store.findById(hid);

      expect(identity).toEqual({
        hid: hid,
        ...expected,
        emailVerified: false,
        address: {
          ...mockAddress,
        },
      });
    });

    it<TestContext>('find excludes address when no address exists', async ({ hid, tx }) => {
      const expected = nextIdentity({
        accounts: [],
      });

      await emailStore.add(
        {
          hid: hid,
          email: expected.email,
          updatedAt: undefined,
        },
        tx,
      );

      await passwordStore.add(
        {
          hid: hid,
          hash: expected.passwordHash as string,
          strength: expected.passwordStrength as PasswordStrength,
        },
        tx,
      );

      await profileStore.add({ hid: hid, ...expected }, tx);

      const identity = await store.findById(hid);

      expect(identity).toEqual({
        hid: hid,
        ...expected,
        emailVerified: false,
      });

      expect(identity).not.toHaveProperty('address');
    });

    it<TestContext>('search includes address when address exists', async ({ hid, tx }) => {
      const expected = nextIdentity();
      const plotId = nextPlotOwnerId();

      await emailStore.add(
        {
          hid: hid,
          email: expected.email,
          emailVerified: expected.emailVerified,
          updatedAt: undefined,
        },
        tx,
      );

      await profileStore.add({ hid: hid, ...expected }, tx);
      await plotStore.add({ hid: hid, plotOwnerId: plotId }, tx);
      await passwordStore.add(
        {
          hid: hid,
          hash: 'xxxx',
          strength: PasswordStrength.STRONG,
        },
        tx,
      );

      await addressStore.add(
        {
          hid: hid,
          ...mockAddress,
        },
        tx,
      );

      const actual = await store.search({
        email: expected.email.email,
      });

      expect(actual.length).toEqual(1);
      expect(actual[0]).toEqual({
        accounts: [{ id: plotId, type: 'owner_id' }],
        email: expected.email,
        emailVerified: expected.emailVerified,
        firstName: expected.firstName,
        hid: hid,
        lastName: expected.lastName,
        phoneNumber: expected.phoneNumber,
        title: expected.title,
        address: mockAddress,
        migrated: true,
      });
    });

    it<TestContext>('search excludes address when no address exists', async ({ hid, tx }) => {
      const expected = nextIdentity();
      const seawareId = nextSeaWareClientId();

      await emailStore.add(
        {
          hid: hid,
          email: expected.email,
          emailVerified: expected.emailVerified,
          updatedAt: undefined,
        },
        tx,
      );

      await seawareStore.add(
        {
          hid: hid,
          seaWareClientId: seawareId,
        },
        tx,
      );

      const actual = await store.search({
        email: expected.email.email,
      });

      expect(actual.length).toEqual(1);
      expect(actual[0]).toEqual({
        accounts: [
          {
            id: seawareId,
            type: 'seaware_client_id',
          },
        ],
        email: expected.email,
        emailVerified: expected.emailVerified,
        firstName: null,
        hid: hid,
        lastName: null,
        phoneNumber: null,
        title: null,
        migrated: false,
      });

      expect(actual[0]).not.toHaveProperty('address');
    });
  });

  it<TestContext>('identity can be verified', async ({ hid, tx }) => {
    const expected = nextIdentity({
      accounts: [],
      emailVerified: true,
    });

    await emailStore.add(
      {
        hid: hid,
        email: expected.email,
        emailVerified: expected.emailVerified,
        updatedAt: undefined,
      },
      tx,
    );
    await passwordStore.add(
      {
        hid: hid,
        hash: expected.passwordHash as string,
        strength: expected.passwordStrength as PasswordStrength,
      },
      tx,
    );
    await profileStore.add({ hid: hid, ...expected }, tx);

    const identity = await store.findByEmail(expected.email);

    expect(identity).toEqual({
      hid: hid,
      ...expected,
    });
  });
});
