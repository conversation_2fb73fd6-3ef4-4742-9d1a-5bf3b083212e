import { HID, Transaction, RTBF_TRANSACTION_SOURCE_SUFFIX } from 'haven-hid-domain';
// @ts-ignore
import { HIDToSeaWareClient, SeaWareClientId } from 'haven-hid-domain';
// @ts-ignore
import { nextHIDToSeaWareClient, nextTx } from 'haven-hid-domain/mock.js';
import * as pgsql from 'pg';
import { beforeEach, describe, expect, it } from 'vitest';
import { PgHIDStore } from '../../src/hid/hid-store.js';
import { PgHIDToSeaWareClientStore } from '../../src/hid/hid-to-seaware-client-store.js';
import { getDatabase } from './db.js';

interface HIDStoreContext {
  store: PgHIDToSeaWareClientStore;
  client: pgsql.Pool;
  hid: HID;
  hidStore: PgHIDStore;
  tx: Transaction;
}

describe('hid-to-seaware-client store scenarios', () => {
  beforeEach<HIDStoreContext>(async (context) => {
    context.client = await getDatabase();
    context.store = new PgHIDToSeaWareClientStore();
    context.hidStore = new PgHIDStore();
    context.hid = await context.hidStore.next();
    context.tx = nextTx('hid-to-seaware-client-store');
  });

  describe('hid-to-seaware-client add and find scenarios', () => {
    it<HIDStoreContext>('store a seaware client against a hid', async ({
      store,
      client,
      hid,
      tx,
    }) => {
      const input = nextHIDToSeaWareClient({
        hid: hid,
      });

      await store.add(input, tx);

      const actual = await directGetByHID(client, input.hid);
      expect(actual).toMatchObject(input);
    });

    it<HIDStoreContext>('get hid for seaware client', async ({ store, client, hid }) => {
      const input = nextHIDToSeaWareClient({
        hid: hid,
      });

      await client.query(
        `INSERT INTO HID_TO_SEAWARE_CLIENT (hid, seaware_client_id) VALUES ('${input.hid}', '${input.seaWareClientId}');`,
      );

      const result = await store.findBySeaWareClientId(input.seaWareClientId);
      expect(result).toStrictEqual(input);
    });

    it<HIDStoreContext>('add fails if hid does not exist', async ({ store, tx }) => {
      const input = nextHIDToSeaWareClient({
        hid: 'DOESNOTEXIST',
      });

      try {
        await store.add(input, tx);
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
  });

  describe('hid-to-seaware-client update scenarios', () => {
    it<HIDStoreContext>('update hid for seaware_client_id', async ({ store, client, hid, tx }) => {
      const original = nextHIDToSeaWareClient({
        hid: hid,
      });
      await client.query(
        `INSERT INTO HID_TO_SEAWARE_CLIENT (hid, seaware_client_id) VALUES ('${original.hid}', '${original.seaWareClientId}');`,
      );

      const update = nextHIDToSeaWareClient({
        hid: original.hid,
      });
      await store.update(update, tx);

      const actual = await directGetByHID(client, update.hid);
      expect(actual).toStrictEqual(update);
    });

    it<HIDStoreContext>('update seaware_client_id for hid', async ({
      store,
      client,
      hid,
      hidStore,
      tx,
    }) => {
      const original = nextHIDToSeaWareClient({
        hid: hid,
      });
      await client.query(
        `INSERT INTO HID_TO_SEAWARE_CLIENT (hid, seaware_client_id) VALUES ('${original.hid}', ${original.seaWareClientId});`,
      );

      const otherHid = await hidStore.next();
      const update = nextHIDToSeaWareClient({
        hid: otherHid,
        seaWareClientId: original.seaWareClientId,
      });
      await store.update(update, tx);

      const actual = await directGetByHID(client, update.hid);
      expect(actual).toStrictEqual(update);
    });

    it<HIDStoreContext>('update fails if hid and seaware_client_id are the same', async ({
      store,
      client,
      hid,
      tx,
    }) => {
      const original = nextHIDToSeaWareClient({
        hid: hid,
      });
      await client.query(
        `INSERT INTO HID_TO_SEAWARE_CLIENT (hid, seaware_client_id) VALUES ('${original.hid}', ${original.seaWareClientId});`,
      );

      try {
        await store.update(original, tx);
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });

    it<HIDStoreContext>('update fails if neither hid nor seaware_client_id are found', async ({
      store,
      hid,
      tx,
    }) => {
      const original = nextHIDToSeaWareClient({
        hid: hid,
      });

      try {
        await store.update(original, tx);
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });

    it<HIDStoreContext>('update fails if hid does not exist', async ({
      store,
      client,
      hid,
      tx,
    }) => {
      const update = nextHIDToSeaWareClient({
        hid: 'DOESNOTEXIST',
      });
      try {
        await store.update(update, tx);
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
  });

  describe('hid-to-seaware-client delete scenarios', () => {
    it<HIDStoreContext>('delete seaware client id', async ({ store, client, hid, tx }) => {
      const input = nextHIDToSeaWareClient({
        hid: hid,
      });
      await client.query(
        `INSERT INTO HID_TO_SEAWARE_CLIENT (hid, seaware_client_id) VALUES ('${input.hid}', '${input.seaWareClientId}');`,
      );

      expect(await store.delete(input, tx)).toBeTruthy();
      await expectHIDAndSeaWareClientIdToNotExist(client, input);
    });

    it<HIDStoreContext>('delete non-existent seaware client id', async ({ store, hid, tx }) => {
      const input = nextHIDToSeaWareClient({
        hid: hid,
      });

      expect(await store.delete(input, tx)).toBeFalsy();
    });
  });

  describe('auditing', () => {
    it<HIDStoreContext>('add is audited', async ({ store, hid, tx }) => {
      const input = nextHIDToSeaWareClient({
        hid: hid,
      });

      await store.add(input, tx);

      expect(await store.audit.findAllByTransaction(tx)).toMatchObject([
        {
          tx: tx.id,
          hid: input.hid,
          to: input.seaWareClientId,
          from: null,
          type: 'seaware_client',
        },
      ]);
    });

    it<HIDStoreContext>('change seaware client is audited', async ({
      hidStore,
      store,
      client,
      hid,
      tx,
    }) => {
      const original = nextHIDToSeaWareClient({
        hid: hid,
      });
      await client.query(
        `INSERT INTO HID_TO_SEAWARE_CLIENT (hid, seaware_client_id) VALUES ('${original.hid}', '${original.seaWareClientId}');`,
      );

      const update = nextHIDToSeaWareClient({
        hid: original.hid,
      });
      await store.update(update, tx);

      expect(await store.audit.findAllByTransaction(tx)).toMatchObject([
        {
          tx: tx.id,
          hid: original.hid,
          to: update.seaWareClientId,
          from: original.seaWareClientId,
          type: 'seaware_client',
        },
      ]);
    });

    it<HIDStoreContext>('change hid is audited', async ({ hidStore, store, client, hid, tx }) => {
      const original = nextHIDToSeaWareClient({
        hid: hid,
      });
      const update = nextHIDToSeaWareClient({
        seaWareClientId: original.seaWareClientId,
      });
      await client.query(
        `INSERT INTO HID_TO_SEAWARE_CLIENT (hid, seaware_client_id) VALUES ('${original.hid}', '${original.seaWareClientId}');`,
      );
      await client.query(`INSERT INTO HID (hid) VALUES ('${update.hid}');`);

      await store.update(update, tx);

      expect(await store.audit.findAllByTransaction(tx)).toMatchObject([
        {
          tx: tx.id,
          hid: original.hid,
          to: null,
          from: original.seaWareClientId,
          type: 'seaware_client',
        },
        {
          tx: tx.id,
          hid: update.hid,
          to: original.seaWareClientId,
          from: null,
          type: 'seaware_client',
        },
      ]);
    });

    it<HIDStoreContext>('delete is audited', async ({ store, client, hid, tx }) => {
      const input = nextHIDToSeaWareClient({
        hid: hid,
      });
      await client.query(
        `INSERT INTO HID_TO_SEAWARE_CLIENT (hid, seaware_client_id) VALUES ('${input.hid}', '${input.seaWareClientId}');`,
      );

      await store.delete(input, tx);

      expect(await store.audit.findAllByTransaction(tx)).toMatchObject([
        {
          tx: tx.id,
          source: tx.source,
          hid: hid,
          from: input.seaWareClientId,
          to: null,
          type: 'seaware_client',
        },
      ]);
    });

    it<HIDStoreContext>('delete is not audited if nothing happens', async ({ store, tx }) => {
      const input = nextHIDToSeaWareClient();
      await store.delete(input, tx);

      expect(await store.audit.findAllByTransaction(tx)).toMatchObject([]);
    });

    it<HIDStoreContext>('delete is not audited if transaction source is RTBF', async ({
      store,
      client,
      hid,
      tx,
    }) => {
      const input = nextHIDToSeaWareClient({
        hid: hid,
      });
      await client.query(
        `INSERT INTO HID_TO_SEAWARE_CLIENT (hid, seaware_client_id) VALUES ('${input.hid}', '${input.seaWareClientId}');`,
      );
      const rtbfTx = nextTx(RTBF_TRANSACTION_SOURCE_SUFFIX);
      await store.delete(input, rtbfTx);

      expect(await store.audit.findAllByTransaction(rtbfTx)).toMatchObject([]);
    });
  });

  describe('errors', () => {
    it('should transform db errors', async () => {
      const hids = new PgHIDStore();
      const seaware = new PgHIDToSeaWareClientStore();
      const hid = await hids.next();
      await seaware.add({ hid, seaWareClientId: 1 }, nextTx());
      try {
        await seaware.add({ hid, seaWareClientId: 1 }, nextTx());
        throw new Error('This test should fail');
      } catch (e: any) {
        expect(e.message).toEqual('Error adding clientid to HID');
        expect(e.cause).toEqual('duplicate key value violates unique constraint');
        expect(e.cause.query).toBeUndefined();
      }
    });
  });
});

const directGetByHID = async (client: pgsql.Pool, hid: HID): Promise<HIDToSeaWareClient> => {
  const actual = await client.query(
    `SELECT hid, seaware_client_id FROM HID_TO_SEAWARE_CLIENT WHERE hid = '${hid}';`,
  );
  expect(actual.rowCount).toBe(1);
  const row = actual.rows[0];
  return {
    hid: row.hid,
    seaWareClientId: row.seaware_client_id,
  };
};

const directHIDDoesNotExist = async (client: pgsql.Pool, hid: HID): Promise<any> => {
  const actual = await client.query(
    `SELECT hid, seaware_client_id FROM HID_TO_SEAWARE_CLIENT WHERE hid = '${hid}';`,
  );
  expect(actual.rowCount).toBe(0);
};

const directSeaWareClientIdDoesNotExist = async (
  client: pgsql.Pool,
  seaWareClientId: SeaWareClientId,
): Promise<any> => {
  const actual = await client.query(
    `SELECT hid, seaware_client_id FROM HID_TO_SEAWARE_CLIENT WHERE seaware_client_id = '${seaWareClientId}';`,
  );
  expect(actual.rowCount).toBe(0);
};

const expectHIDAndSeaWareClientIdToNotExist = async (
  client: pgsql.Pool,
  input: HIDToSeaWareClient,
): Promise<any> => {
  await directHIDDoesNotExist(client, input.hid);
  await directSeaWareClientIdDoesNotExist(client, input.seaWareClientId);
};
