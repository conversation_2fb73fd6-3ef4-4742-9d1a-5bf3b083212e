import { HID, HIDToPassword, Transaction, RTBF_TRANSACTION_SOURCE_SUFFIX } from 'haven-hid-domain';
// @ts-ignore
import { nextTx, nextHIDToPassword } from 'haven-hid-domain/mock.js';
import * as pgsql from 'pg';
import { beforeEach, describe, expect, it } from 'vitest';
import { PgHIDStore } from '../../src/hid/hid-store.js';
import { getDatabase } from './db.js';
import { PgHIDToPasswordStore } from '../../src/hid/hid-to-password-store.js';

interface HIDStoreContext {
  store: PgHIDToPasswordStore;
  hidStore: PgHIDStore;
  client: pgsql.Pool;
  hid: HID;
  tx: Transaction;
}

describe('hid-to-password store scenarios', () => {
  beforeEach<HIDStoreContext>(async (context) => {
    context.hidStore = new PgHIDStore();
    context.hid = await context.hidStore.next();
    context.store = new PgHIDToPasswordStore();
    context.client = await getDatabase();
    context.tx = nextTx('hid-to-password-store');
  });

  describe('hid-to-password add and find scenarios', () => {
    it<HIDStoreContext>('store a password against a hid', async ({ store, client, hid, tx }) => {
      const input = nextHIDToPassword({
        hid: hid,
      });

      await store.add(input, tx);

      const actual = await directGetByHID(client, input.hid);
      expect(actual).toMatchObject(input);
    });

    it<HIDStoreContext>('get password for hid', async ({ store, client, hid, tx }) => {
      const input = nextHIDToPassword({
        hid: hid,
      });
      await client.query(
        `INSERT INTO HID_TO_PASSWORD (hid, hash, strength) VALUES ('${input.hid}', '${input.hash}', '${input.strength}');`,
      );

      const result = await store.findByHID(input.hid);
      expect(result).toStrictEqual(input);
    });

    it<HIDStoreContext>('add fails if hid does not exist', async ({ store, tx }) => {
      const input = nextHIDToPassword({
        hid: 'DOESNOTEXIST',
      });

      try {
        await store.add(input, tx);
      } catch (error: any) {
        expect(error.message).toMatch('Error adding password to HID');
      }
    });
  });

  describe('hid-to-password update scenarios', () => {
    it<HIDStoreContext>('update password for hid', async ({ store, client, hid, tx }) => {
      const original = nextHIDToPassword({
        hid: hid,
      });
      await client.query(
        `INSERT INTO HID_TO_PASSWORD (hid, hash, strength) VALUES ('${original.hid}', '${original.hash}', '${original.strength}');`,
      );

      const update = nextHIDToPassword({
        hid: original.hid,
      });
      const result = await store.update(update, tx);
      expect(result).toEqual(true);

      const actual = await directGetByHID(client, original.hid);
      expect(actual).toStrictEqual(update);
    });

    it<HIDStoreContext>('update returns false if nothing is changed', async ({
      store,
      client,
      hid,
      tx,
    }) => {
      const original = nextHIDToPassword({
        hid: hid,
      });
      await client.query(
        `INSERT INTO HID_TO_PASSWORD (hid, hash, strength) VALUES ('${original.hid}', '${original.hash}', '${original.strength}');`,
      );

      const actual = await store.update(original, tx);
      expect(actual).toEqual(false);
    });

    it<HIDStoreContext>('update fails if hid is not found', async ({ store, hid, tx }) => {
      const original = nextHIDToPassword({
        hid: hid,
      });

      try {
        await store.update(original, tx);
      } catch (error: any) {
        expect(error.message).toMatch(/No record found to update for/);
      }
    });

    it<HIDStoreContext>('update fails if hash is null', async ({ store, client, hid, tx }) => {
      const original = nextHIDToPassword({
        hid: hid,
      });
      await client.query(
        `INSERT INTO HID_TO_PASSWORD (hid, hash, strength) VALUES ('${original.hid}', '${original.hash}', '${original.strength}');`,
      );
      const update = nextHIDToPassword({
        hid: original.hid,
      });

      try {
        await store.update({ ...update, hash: '' }, tx);
      } catch (error: any) {
        expect(error.message).toMatch('Error storing password against HID');
      }
    });

    it<HIDStoreContext>('update fails if hash is too long', async ({ store, client, hid, tx }) => {
      const original = nextHIDToPassword({
        hid: hid,
      });
      await client.query(
        `INSERT INTO HID_TO_PASSWORD (hid, hash, strength) VALUES ('${original.hid}', '${original.hash}', '${original.strength}');`,
      );
      const update = nextHIDToPassword({
        hid: original.hid,
      });
      const tooLongHash = 'a'.repeat(101);

      try {
        await store.update({ ...update, hash: tooLongHash }, tx);
      } catch (error: any) {
        expect(error.message).toMatch('Error storing password against HID');
      }
    });
  });

  describe('hid-to-password delete scenarios', () => {
    it<HIDStoreContext>('delete password', async ({ store, client, hid, tx }) => {
      const input = nextHIDToPassword({
        hid: hid,
      });
      await client.query(
        `INSERT INTO HID_TO_PASSWORD (hid, hash, strength) VALUES ('${input.hid}', '${input.hash}', '${input.strength}');`,
      );

      expect(await store.delete(input, tx)).toBeTruthy();
      await directHIDDoesNotExist(client, input.hid);
    });
  });

  describe('auditing', () => {
    it<HIDStoreContext>('add is audited', async ({ store, client, hid, tx }) => {
      const input = nextHIDToPassword({
        hid: hid,
      });

      await store.add(input, tx);

      expect(await store.audit.findAllByTransaction(tx)).toMatchObject([
        {
          tx: tx.id,
          hid: input.hid,
          to: { strength: input.strength },
          from: null,
          type: 'password',
        },
      ]);
    });

    it<HIDStoreContext>('change password is audited', async ({
      hidStore,
      store,
      client,
      hid,
      tx,
    }) => {
      const original = nextHIDToPassword({
        hid: hid,
      });
      await client.query(
        `INSERT INTO HID_TO_PASSWORD (hid, hash, strength) VALUES ('${original.hid}', '${original.hash}', '${original.strength}');`,
      );

      const update = nextHIDToPassword({
        hid: original.hid,
      });
      await store.update(update, tx);

      expect(await store.audit.findAllByTransaction(tx)).toMatchObject([
        {
          tx: tx.id,
          hid: original.hid,
          to: { strength: update.strength },
          from: { strength: original.strength },
          type: 'password',
        },
      ]);
    });

    it<HIDStoreContext>('delete is audited', async ({ store, client, hid, tx }) => {
      const original = nextHIDToPassword({
        hid: hid,
      });
      await client.query(
        `INSERT INTO HID_TO_PASSWORD (hid, hash, strength) VALUES ('${original.hid}', '${original.hash}', '${original.strength}');`,
      );

      await store.delete(original, tx);

      expect(await store.audit.findAllByTransaction(tx)).toMatchObject([
        {
          tx: tx.id,
          source: tx.source,
          hid: hid,
          from: { strength: original.strength },
          to: null,
          type: 'password',
        },
      ]);
    });

    it<HIDStoreContext>('delete is not audited if nothing happens', async ({
      store,
      client,
      hid,
      tx,
    }) => {
      const input = nextHIDToPassword();
      await store.delete(input, tx);

      expect(await store.audit.findAllByTransaction(tx)).toMatchObject([]);
    });

    it<HIDStoreContext>('delete is not audited if transaction source is RTBF', async ({
      store,
      client,
      hid,
      tx,
    }) => {
      const original = nextHIDToPassword({
        hid: hid,
      });
      await client.query(
        `INSERT INTO HID_TO_PASSWORD (hid, hash, strength) VALUES ('${original.hid}', '${original.hash}', '${original.strength}');`,
      );
      const rtbfTx = nextTx(RTBF_TRANSACTION_SOURCE_SUFFIX);
      await store.delete(original, rtbfTx);

      expect(await store.audit.findAllByTransaction(rtbfTx)).toMatchObject([]);
    });
  });
});

const directGetByHID = async (client: pgsql.Pool, hid: HID): Promise<HIDToPassword> => {
  const actual = await client.query(
    `SELECT hid, hash, strength FROM HID_TO_PASSWORD WHERE hid = '${hid}';`,
  );
  expect(actual.rowCount).toBe(1);
  const row = actual.rows[0];
  return {
    hid: row.hid,
    hash: row.hash,
    strength: row.strength,
  };
};

const directHIDDoesNotExist = async (client: pgsql.Pool, hid: HID): Promise<any> => {
  const actual = await client.query(`SELECT hid FROM HID_TO_PASSWORD WHERE hid = '${hid}';`);
  expect(actual.rowCount).toBe(0);
};
