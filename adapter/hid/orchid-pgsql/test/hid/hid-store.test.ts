import {
  HID,
  HIDToEmail,
  H<PERSON>To<PERSON>mailUpdate,
  HIDToPlotOwner,
  HIDToSeaWareClient,
  PlotOwnerId,
  SeaWareClientId,
  Transaction,
  ValidEmail,
  AllHIDData,
  RTBF_TRANSACTION_SOURCE_SUFFIX,
  HavenIdentityTransactionalStore,
  HavenIdentityRegistration,
  HavenServiceAccountIdType,
} from 'haven-hid-domain';
import {
  nextHID,
  nextHIDToEmail,
  nextHIDToPlotOwner,
  nextHIDToSeaWareClient,
  nextIdentity,
  nextPlotOwnerId,
  nextSeaWareClientId,
  nextTx,
  nextValidEmail,
} from 'haven-hid-domain/mock.js';
import { beforeEach, describe, expect, it } from 'vitest';
import { PgHIDStore } from '../../src/hid/hid-store.js';
import { PgHIDToEmailStore } from '../../src/hid/hid-to-email-store.js';
import { PgHIDToPlotOwnerStore } from '../../src/hid/hid-to-plot-owner-store.js';
import { PgHIDToSeaWareClientStore } from '../../src/hid/hid-to-seaware-client-store.js';
import { getDatabase } from './db.js';
import { AssertionError } from 'assert';
import { PgHavenIdentityTransactionalStore } from '../../src/hid/hid.out.pgsql.js';

interface HIDStoreContext {
  store: PgHIDStore;
  emailStore: PgHIDToEmailStore;
  plotStore: PgHIDToPlotOwnerStore;
  seawareStore: PgHIDToSeaWareClientStore;
  identityStore: HavenIdentityTransactionalStore;
  hid: HID;
  tx: Transaction;
}

describe('hid store scenarios', () => {
  it('generate a hid', async () => {
    const store = new PgHIDStore();
    const client = await getDatabase();
    const hid = await store.next();

    const actual = await client.query(`SELECT *
                                       FROM HID
                                       WHERE hid = '${hid}';`);
    expect(actual.rowCount).toBe(1);
  });
});

describe('hid find all scenarios', () => {
  beforeEach<HIDStoreContext>(async (context) => {
    context.store = new PgHIDStore();
    context.emailStore = new PgHIDToEmailStore();
    context.plotStore = new PgHIDToPlotOwnerStore();
    context.seawareStore = new PgHIDToSeaWareClientStore();
    context.identityStore = PgHavenIdentityTransactionalStore();
    context.hid = await context.store.next();
    context.tx = nextTx('hid-store-test');
  });

  describe('find by email', () => {
    it<HIDStoreContext>('find none', async ({ store }) => {
      const actual = await store.findAllForEmail(nextValidEmail());

      expect(actual.exists()).toBeFalsy();
    });

    it<HIDStoreContext>('find email', async (context) => {
      const hidToEmail = await setUpEmail(context);

      const actual = await context.store.findAllForEmail(hidToEmail.email);

      expectHID(actual, context.hid);
      expectEmail(actual, hidToEmail.email);
      expectEmailUpdatedAt(actual);
      expectServiceIds(actual);
      expectIdentityExists(actual, false);
    });

    it<HIDStoreContext>('find identity', async (context) => {
      const identity = await setUpIdentity(context);

      const actual = await context.store.findAllForEmail(identity.email);

      expectHID(actual, identity.hid);
      expectIdentityExists(actual, true);
    });

    it<HIDStoreContext>('find email and plot', async (context) => {
      const hidToEmail = await setUpEmail(context);
      const hidToPlot = await setUpPlot(context);

      const actual = await context.store.findAllForEmail(hidToEmail.email);

      expectHID(actual, context.hid);
      expectEmail(actual, hidToEmail.email);
      expectServiceIds(actual, hidToPlot.plotOwnerId);
      expectIdentityExists(actual, false);
    });

    it<HIDStoreContext>('find email and seaware', async (context) => {
      const hidToEmail = await setUpEmail(context);
      const hidToSeaWare = await setUpSeaware(context);

      const actual = await context.store.findAllForEmail(hidToEmail.email);

      expectHID(actual, context.hid);
      expectEmail(actual, hidToEmail.email);
      expectServiceIds(actual, undefined, hidToSeaWare.seaWareClientId);
      expectIdentityExists(actual, false);
    });

    it<HIDStoreContext>('find email, plot and seaware', async (context) => {
      const hidToEmail = await setUpEmail(context);
      const hidToPlot = await setUpPlot(context);
      const hidToSeaWare = await setUpSeaware(context);

      const actual = await context.store.findAllForEmail(hidToEmail.email);

      expectHID(actual, context.hid);
      expectEmail(actual, hidToEmail.email);
      expectServiceIds(actual, hidToPlot.plotOwnerId, hidToSeaWare.seaWareClientId);
      expectIdentityExists(actual, false);
    });
  });

  describe('find by hid', () => {
    it<HIDStoreContext>('find none', async ({ store }) => {
      const actual = await store.findAllForHID(nextHID());

      expect(actual.exists()).toBeFalsy();
    });

    it<HIDStoreContext>('find hid', async ({ hid, store }) => {
      const actual = await store.findAllForHID(hid);

      expectHID(actual, hid);
      expectEmail(actual);
      expectServiceIds(actual);
      expectIdentityExists(actual, false);
    });

    it<HIDStoreContext>('find email', async (context) => {
      const hidToEmail = await setUpEmail(context);

      const actual = await context.store.findAllForHID(context.hid);

      expectHID(actual, context.hid);
      expectEmail(actual, hidToEmail.email);
      expectServiceIds(actual);
      expectIdentityExists(actual, false);
    });

    it<HIDStoreContext>('find identity', async (context) => {
      const identity = await setUpIdentity(context);

      const actual = await context.store.findAllForHID(identity.hid);

      expectHID(actual, identity.hid);
      expectIdentityExists(actual, true);
    });

    it<HIDStoreContext>('find plot', async (context) => {
      const hidToPlot = await setUpPlot(context);

      const actual = await context.store.findAllForHID(context.hid);

      expectHID(actual, context.hid);
      expectEmail(actual);
      expectServiceIds(actual, hidToPlot.plotOwnerId);
      expectIdentityExists(actual, false);
    });

    it<HIDStoreContext>('find seaware', async (context) => {
      const hidToSeaWare = await setUpSeaware(context);

      const actual = await context.store.findAllForHID(context.hid);

      expectHID(actual, context.hid);
      expectEmail(actual);
      expectServiceIds(actual, undefined, hidToSeaWare.seaWareClientId);
      expectIdentityExists(actual, false);
    });

    it<HIDStoreContext>('find email and plot', async (context) => {
      const hidToEmail = await setUpEmail(context);
      const hidToPlot = await setUpPlot(context);

      const actual = await context.store.findAllForHID(context.hid);

      expectHID(actual, context.hid);
      expectEmail(actual, hidToEmail.email);
      expectServiceIds(actual, hidToPlot.plotOwnerId);
      expectIdentityExists(actual, false);
    });

    it<HIDStoreContext>('find email and seaware', async (context) => {
      const hidToEmail = await setUpEmail(context);
      const hidToSeaWare = await setUpSeaware(context);

      const actual = await context.store.findAllForHID(context.hid);

      expectHID(actual, context.hid);
      expectEmail(actual, hidToEmail.email);
      expectServiceIds(actual, undefined, hidToSeaWare.seaWareClientId);
      expectIdentityExists(actual, false);
    });

    it<HIDStoreContext>('find plot and seaware', async (context) => {
      const hidToPlot = await setUpPlot(context);
      const hidToSeaWare = await setUpSeaware(context);

      const actual = await context.store.findAllForHID(context.hid);

      expectHID(actual, context.hid);
      expectEmail(actual);
      expectServiceIds(actual, hidToPlot.plotOwnerId, hidToSeaWare.seaWareClientId);
      expectIdentityExists(actual, false);
    });

    it<HIDStoreContext>('find email, plot and seaware', async (context) => {
      const hidToEmail = await setUpEmail(context);
      const hidToPlot = await setUpPlot(context);
      const hidToSeaWare = await setUpSeaware(context);

      const actual = await context.store.findAllForHID(context.hid);

      expectHID(actual, context.hid);
      expectEmail(actual, hidToEmail.email);
      expectServiceIds(actual, hidToPlot.plotOwnerId, hidToSeaWare.seaWareClientId);
      expectIdentityExists(actual, false);
    });
  });

  describe('find by plot', () => {
    it<HIDStoreContext>('find none', async ({ store }) => {
      const actual = await store.findAllForPlotOwnerId(nextPlotOwnerId());

      expect(actual.exists()).toBeFalsy();
    });

    it<HIDStoreContext>('find plot', async (context) => {
      const hidToPlot = await setUpPlot(context);

      const actual = await context.store.findAllForPlotOwnerId(hidToPlot.plotOwnerId);

      expectHID(actual, context.hid);
      expectEmail(actual);
      expectServiceIds(actual, hidToPlot.plotOwnerId);
      expectIdentityExists(actual, false);
    });

    it<HIDStoreContext>('find identity', async (context) => {
      const ownerId = nextPlotOwnerId();
      const identity = await setUpIdentity(context, {
        accounts: [
          {
            id: ownerId,
            type: HavenServiceAccountIdType.OWNER_ID,
          },
        ],
      });

      const actual = await context.store.findAllForPlotOwnerId(ownerId);

      expectHID(actual, identity.hid);
      expectIdentityExists(actual, true);
    });

    it<HIDStoreContext>('find plot and email', async (context) => {
      const hidToPlot = await setUpPlot(context);
      const hidToEmail = await setUpEmail(context);

      const actual = await context.store.findAllForPlotOwnerId(hidToPlot.plotOwnerId);

      expectHID(actual, context.hid);
      expectEmail(actual, hidToEmail.email);
      expectServiceIds(actual, hidToPlot.plotOwnerId);
      expectIdentityExists(actual, false);
    });

    it<HIDStoreContext>('find plot and seaware', async (context) => {
      const hidToPlot = await setUpPlot(context);
      const hidToSeaWare = await setUpSeaware(context);

      const actual = await context.store.findAllForPlotOwnerId(hidToPlot.plotOwnerId);

      expectHID(actual, context.hid);
      expectEmail(actual);
      expectServiceIds(actual, hidToPlot.plotOwnerId, hidToSeaWare.seaWareClientId);
      expectIdentityExists(actual, false);
    });

    it<HIDStoreContext>('find plot, email and seaware', async (context) => {
      const hidToPlot = await setUpPlot(context);
      const hidToEmail = await setUpEmail(context);
      const hidToSeaWare = await setUpSeaware(context);

      const actual = await context.store.findAllForPlotOwnerId(hidToPlot.plotOwnerId);

      expectHID(actual, context.hid);
      expectEmail(actual, hidToEmail.email);
      expectServiceIds(actual, hidToPlot.plotOwnerId, hidToSeaWare.seaWareClientId);
      expectIdentityExists(actual, false);
    });
  });

  describe('find by seaware', () => {
    it<HIDStoreContext>('find none', async ({ store }) => {
      const actual = await store.findAllForSeaWareClientId(nextSeaWareClientId());

      expect(actual.exists()).toBeFalsy();
    });

    it<HIDStoreContext>('find seaware', async (context) => {
      const hidToSeaWare = await setUpSeaware(context);

      const actual = await context.store.findAllForSeaWareClientId(hidToSeaWare.seaWareClientId);

      expectHID(actual, context.hid);
      expectEmail(actual);
      expectServiceIds(actual, undefined, hidToSeaWare.seaWareClientId);
      expectIdentityExists(actual, false);
    });

    it<HIDStoreContext>('find identity', async (context) => {
      const clientId = nextSeaWareClientId();
      const identity = await setUpIdentity(context, {
        accounts: [
          {
            id: clientId,
            type: HavenServiceAccountIdType.SEAWARE_CLIENT_ID,
          },
        ],
      });

      const actual = await context.store.findAllForSeaWareClientId(clientId);

      expectHID(actual, identity.hid);
      expectIdentityExists(actual, true);
    });

    it<HIDStoreContext>('find seaware and email', async (context) => {
      const hidToSeaWare = await setUpSeaware(context);
      const hidToEmail = await setUpEmail(context);

      const actual = await context.store.findAllForSeaWareClientId(hidToSeaWare.seaWareClientId);

      expectHID(actual, context.hid);
      expectEmail(actual, hidToEmail.email);
      expectServiceIds(actual, undefined, hidToSeaWare.seaWareClientId);
      expectIdentityExists(actual, false);
    });

    it<HIDStoreContext>('find seaware and plot', async (context) => {
      const hidToSeaWare = await setUpSeaware(context);
      const hidToPlot = await setUpPlot(context);

      const actual = await context.store.findAllForSeaWareClientId(hidToSeaWare.seaWareClientId);

      expectHID(actual, context.hid);
      expectEmail(actual);
      expectServiceIds(actual, hidToPlot.plotOwnerId, hidToSeaWare.seaWareClientId);
      expectIdentityExists(actual, false);
    });

    it<HIDStoreContext>('find seaware, email and plot', async (context) => {
      const hidToSeaWare = await setUpSeaware(context);

      const hidToPlot = await setUpPlot(context);
      const hidToEmail = await setUpEmail(context);

      const actual = await context.store.findAllForSeaWareClientId(hidToSeaWare.seaWareClientId);

      expectHID(actual, context.hid);
      expectEmail(actual, hidToEmail.email);
      expectServiceIds(actual, hidToPlot.plotOwnerId, hidToSeaWare.seaWareClientId);
      expectIdentityExists(actual, false);
    });
  });

  describe('delete by hid', () => {
    it<HIDStoreContext>('should delete the hid', async ({ store, hid, tx }) => {
      expect(await store.delete(hid, tx)).toBeTruthy();
    });

    it<HIDStoreContext>('should return falsy when hid can not be found', async ({ store, tx }) => {
      expect(await store.delete('not found', tx)).toBeFalsy();
    });

    it<HIDStoreContext>('should create audit log', async ({ store, hid, tx }) => {
      await store.delete(hid, tx);
      const [audit] = await store.audit.findAllByTransaction(tx);

      expect(audit).toEqual(expect.objectContaining({ hid }));
    });

    it<HIDStoreContext>('should create audit log for RTBF', async ({ store, hid }) => {
      const rtbfTx = nextTx(RTBF_TRANSACTION_SOURCE_SUFFIX);
      await store.delete(hid, rtbfTx);
      const [audit] = await store.audit.findAllByTransaction(rtbfTx);

      expect(audit).toEqual(expect.objectContaining({ hid }));
    });
  });
});

const expectHID = (actual: AllHIDData, expected: HID) => {
  expect(actual.exists()).toBeTruthy();
  expect(actual.hid).toStrictEqual(expected);
};

const expectEmail = (actual: AllHIDData, expected: ValidEmail | undefined = undefined) => {
  if (actual.email) {
    if (expected) {
      expect(actual.email.email).toStrictEqual(expected.email);
    }
  } else {
    expect(actual.email).toStrictEqual(expected);
  }
};

const expectEmailUpdatedAt = (actual: AllHIDData) => {
  if (actual.emailUpdatedAt) {
    expect(actual.emailUpdatedAt instanceof Date).toBeTruthy();
  } else {
    throw new AssertionError({
      message: 'Should have had a Date for emailUpdatedAt',
    });
  }
};

const expectServiceIds = (
  actual: AllHIDData,
  plot: PlotOwnerId | undefined = undefined,
  seaware: SeaWareClientId | undefined = undefined,
) => {
  expect(actual.plotOwnerId).toStrictEqual(plot);
  expect(actual.seaWareClientId).toStrictEqual(seaware);
};

const expectIdentityExists = (actual: AllHIDData, exists: boolean) => {
  expect(actual.identityExists).toEqual(exists);
};

const setUpIdentity = async (
  context: HIDStoreContext,
  overrides: Partial<HavenIdentityRegistration> = {},
): Promise<HIDToEmail> => {
  const identity = nextIdentity(overrides);
  return await context.identityStore.createIdentity(identity, context.tx);
};

const setUpEmail = async (context: HIDStoreContext): Promise<HIDToEmail> => {
  const hidToEmail: HIDToEmailUpdate = nextHIDToEmail({ hid: context.hid });
  await context.emailStore.add(hidToEmail, nextTx());
  return hidToEmail;
};

const setUpPlot = async (context: HIDStoreContext): Promise<HIDToPlotOwner> => {
  const hidToPlot = nextHIDToPlotOwner({
    hid: context.hid,
  });
  await context.plotStore.add(hidToPlot, context.tx);
  return hidToPlot;
};

const setUpSeaware = async (context: HIDStoreContext): Promise<HIDToSeaWareClient> => {
  const hidToSeaWare = nextHIDToSeaWareClient({
    hid: context.hid,
  });
  await context.seawareStore.add(hidToSeaWare, context.tx);
  return hidToSeaWare;
};
