import { beforeEach, describe, expect, it, vi, assert } from 'vitest';
import { OrchidTransactionProvider } from '../../src/hid/transaction.js';
import { logger } from '@havenengineering/module-haven-logging';
import { DomainError, DomainErrorCode } from 'haven-hid-domain';
import { AssertionError } from 'assert';

describe('db transaction', () => {
  const orchidTransactionProvider = new OrchidTransactionProvider();
  const mockLogger = vi.spyOn(logger, 'warn');

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should retry on retryable failure', async () => {
    const error = new DomainError(DomainErrorCode.SERVICE_ERROR, 'Bang', true);
    const callback = vi.fn(() => Promise.reject(error));
    try {
      await orchidTransactionProvider.transaction(callback, { retry: true });
      assert(false, 'should not get here');
    } catch (e) {
      if (e instanceof AssertionError) {
        throw e;
      }
    }

    expect(mockLogger).toHaveBeenCalledWith('Retrying transaction', { cause: error });
    expect(callback).toHaveBeenCalledTimes(2);
  });

  it('should not retry on non retryable failure', async () => {
    const error = new DomainError(DomainErrorCode.SERVICE_ERROR, 'Bang', false);
    const callback = vi.fn(() => Promise.reject(error));
    try {
      await orchidTransactionProvider.transaction(callback, { retry: true });
      assert(false, 'should not get here');
    } catch (e) {
      if (e instanceof AssertionError) {
        throw e;
      }
    }
    expect(callback).toHaveBeenCalledTimes(1);
  });

  it('should not retry on simple failure', async () => {
    const error = new Error('oops');
    const callback = vi.fn(() => Promise.reject(error));
    try {
      await orchidTransactionProvider.transaction(callback, { retry: true });
      assert(false, 'should not get here');
    } catch (e) {
      if (e instanceof AssertionError) {
        throw e;
      }
    }
    expect(callback).toHaveBeenCalledTimes(1);
  });

  it('should not retry on retryable failure, if retry is false', async () => {
    const error = new DomainError(DomainErrorCode.SERVICE_ERROR, 'Bang', true);
    const callback = vi.fn(() => Promise.reject(error));
    try {
      await orchidTransactionProvider.transaction(callback, { retry: false });
      assert(false, 'should not get here');
    } catch (e) {
      if (e instanceof AssertionError) {
        throw e;
      }
    }
    expect(callback).toHaveBeenCalledTimes(1);
  });

  it('should not retry on success', async () => {
    const callback = vi.fn(() => Promise.resolve('some result'));
    const result = await orchidTransactionProvider.transaction(callback, { retry: true });

    expect(callback).toHaveBeenCalledTimes(1);
    expect(result).toEqual('some result');
  });
});
