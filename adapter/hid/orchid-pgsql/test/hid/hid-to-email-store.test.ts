import {
  <PERSON><PERSON>,
  HIDToEmail,
  HIDToEmailLookup,
  Transaction,
  ValidEmail,
  RTBF_TRANSACTION_SOURCE_SUFFIX,
} from 'haven-hid-domain';
// @ts-ignore
import { nextHIDToEmail, nextTx, nextValidEmail } from 'haven-hid-domain/mock.js';
import * as pgsql from 'pg';
import { afterEach, beforeEach, describe, expect, it } from 'vitest';
import { PgHIDStore } from '../../src/hid/hid-store.js';
import { PgHIDToEmailStore } from '../../src/hid/hid-to-email-store.js';
import { getDatabase } from './db.js';
import MockDate from 'mockdate';

interface HIDStoreContext {
  store: PgHIDToEmailStore;
  client: pgsql.Pool;
  hid: HID;
  hidStore: PgHIDStore;
  tx: Transaction;
}

const NOW = new Date();
const UPDATED_AT = new Date('2022-02-02T10:00:00.000Z');

describe('hid-to-email store scenarios', async () => {
  beforeEach<HIDStoreContext>(async (context) => {
    context.client = await getDatabase();
    context.store = new PgHIDToEmailStore();
    context.hidStore = new PgHIDStore();
    context.hid = await context.hidStore.next();
    context.tx = nextTx();
    MockDate.set(NOW);
  });
  afterEach(() => {
    MockDate.reset();
  });

  describe('hid-to-email add and find scenarios', () => {
    it<HIDStoreContext>('store an email against a hid', async ({ store, client, hid, tx }) => {
      const input = nextHIDToEmail({ hid });
      await store.add(input, tx);

      const actual = await directGetByHID(client, input.hid);
      expect(actual).toMatchObject({
        ...input,
        updatedAt: NOW,
      });
    });

    it<HIDStoreContext>('store an email against a hid with updated-at', async ({
      store,
      client,
      hid,
      tx,
    }) => {
      const input = nextHIDToEmail({
        hid,
        updatedAt: UPDATED_AT,
      });

      await store.add(input, tx);

      const actual = await directGetByHID(client, input.hid);
      expect(actual).toMatchObject({
        ...input,
        updatedAt: UPDATED_AT,
      });
    });

    it<HIDStoreContext>('store a verified email', async ({ store, client, hid, tx }) => {
      const input = nextHIDToEmail({
        hid,
        emailVerified: true,
      });
      await store.add(input, tx);

      const actual = await directGetByHID(client, input.hid);
      expect(actual.emailVerified).toBeTruthy();
    });

    it<HIDStoreContext>('get a verified email', async ({ store, client, hid, tx }) => {
      const input = nextHIDToEmail({
        hid,
        emailVerified: true,
      });
      await store.add(input, tx);

      const found = await store.findByEmail(input.email);
      expect(found?.emailVerified).toBeTruthy();
    });

    it<HIDStoreContext>('get hid for email', async ({ store, client, hid }) => {
      const input = nextHIDToEmail({ hid });

      await client.query(
        `INSERT INTO HID_TO_EMAIL (hid, email, updated_at) VALUES ('${input.hid}', '${
          input.email.email
        }', '${UPDATED_AT.toISOString()}');`,
      );

      const result = await store.findByEmail(input.email);
      expect(result).toMatchObject({
        ...input,
        updatedAt: UPDATED_AT,
      });
    });

    it<HIDStoreContext>('add email fails if hid does not exist', async ({ store, tx }) => {
      const input = nextHIDToEmail({
        hid: 'DOESNOTEXIST',
      });
      try {
        await store.add(input, tx);
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
  });

  describe('hid-to-email update scenarios', () => {
    describe('without emailverified', () => {
      it<HIDStoreContext>('update email', async ({ store, client, hid, tx }) => {
        const original = nextHIDToEmail({ hid });
        await client.query(
          `INSERT INTO HID_TO_EMAIL (hid, email) VALUES ('${original.hid}', '${original.email.email}');`,
        );

        const update = nextHIDToEmail({
          hid: original.hid,
        });
        await store.update(update, tx);

        const actual = await directGetByHID(client, update.hid);
        expect(actual).toStrictEqual({
          ...update,
          emailVerified: false,
          updatedAt: NOW,
        });
      });

      it<HIDStoreContext>('update with updated-at', async ({ store, client, hid, tx }) => {
        const original = nextHIDToEmail({ hid });
        await client.query(
          `INSERT INTO HID_TO_EMAIL (hid, email) VALUES ('${original.hid}', '${original.email.email}');`,
        );

        const update = nextHIDToEmail({
          hid: original.hid,
          updatedAt: UPDATED_AT,
        });
        await store.update(update, tx);

        const actual = await directGetByHID(client, update.hid);
        expect(actual).toStrictEqual({
          ...update,
          emailVerified: false,
          updatedAt: UPDATED_AT,
        });
      });

      it<HIDStoreContext>('update hid for email', async ({ store, client, hid, hidStore, tx }) => {
        const original = nextHIDToEmail({ hid });
        await client.query(
          `INSERT INTO HID_TO_EMAIL (hid, email) VALUES ('${original.hid}', '${original.email.email}');`,
        );

        const otherHid = await hidStore.next();
        const update = nextHIDToEmail({
          hid: otherHid,
          email: original.email,
        });
        await store.update(update, tx);

        const actual = await directGetByHID(client, update.hid);
        expect(actual).toStrictEqual({
          ...update,
          emailVerified: false,
          updatedAt: NOW,
        });
      });

      it<HIDStoreContext>('update email for hid with updated-at', async ({
        store,
        client,
        hid,
        hidStore,
        tx,
      }) => {
        const original = nextHIDToEmail({ hid });
        await client.query(
          `INSERT INTO HID_TO_EMAIL (hid, email) VALUES ('${original.hid}', '${original.email.email}');`,
        );

        const otherHid = await hidStore.next();
        const update = nextHIDToEmail({
          hid: otherHid,
          email: original.email,
          updatedAt: UPDATED_AT,
        });
        await store.update(update, tx);

        const actual = await directGetByHID(client, update.hid);
        expect(actual).toStrictEqual({
          ...update,
          emailVerified: false,
          updatedAt: UPDATED_AT,
        });
      });

      it<HIDStoreContext>('update returns false if hid and email are the same', async ({
        store,
        client,
        hid,
        tx,
      }) => {
        const original = nextHIDToEmail({ hid });
        await client.query(
          `INSERT INTO HID_TO_EMAIL (hid, email) VALUES ('${original.hid}', '${original.email.email}');`,
        );

        const actual = await store.update(original, tx);
        expect(actual).toEqual(false);
      });

      it<HIDStoreContext>('update fails if neither hid nor email are found', async ({
        store,
        hid,
        tx,
      }) => {
        const original = nextHIDToEmail({ hid });
        try {
          await store.update(original, tx);
        } catch (error) {
          expect(error).toBeTruthy();
        }
      });

      it<HIDStoreContext>('update hid fails if hid does not exist', async ({ store, tx }) => {
        const update = nextHIDToEmail({
          hid: 'DOESNOTEXIST',
        });
        try {
          await store.update(update, tx);
        } catch (error) {
          expect(error).toBeTruthy();
        }
      });
    });

    describe('with emailverified', () => {
      it<HIDStoreContext>('update existing verified email with unverified one', async ({
        store,
        client,
        hid,
        tx,
      }) => {
        const original = nextHIDToEmail({ hid });
        await client.query(
          `INSERT INTO HID_TO_EMAIL (hid, email, email_verified) VALUES ('${original.hid}', '${original.email.email}', true);`,
        );

        const update = nextHIDToEmail({
          hid: original.hid,
        });
        await store.update(update, tx);

        const actual = await directGetByHID(client, update.hid);
        expect(actual).toStrictEqual({
          ...update,
          emailVerified: false,
          updatedAt: NOW,
        });
      });

      it<HIDStoreContext>('should reset verified state if email moves another hid', async ({
        store,
        client,
        hid,
        hidStore,
        tx,
      }) => {
        const original = nextHIDToEmail({ hid });
        await client.query(
          `INSERT INTO HID_TO_EMAIL (hid, email, email_verified) VALUES ('${original.hid}', '${original.email.email}', true);`,
        );

        const otherHid = await hidStore.next();
        const update = nextHIDToEmail({
          hid: otherHid,
          email: original.email,
        });
        await store.update(update, tx);

        const actual = await directGetByHID(client, update.hid);
        expect(actual).toStrictEqual({
          ...update,
          emailVerified: false,
          updatedAt: NOW,
        });
      });

      it<HIDStoreContext>('update emailVerified to true when provided', async ({
        store,
        client,
        hid,
        tx,
      }) => {
        const original = nextHIDToEmail({ hid });
        await client.query(
          `INSERT INTO HID_TO_EMAIL (hid, email) VALUES ('${original.hid}', '${original.email.email}');`,
        );

        const update = nextHIDToEmail({
          hid: original.hid,
          email: original.email,
          emailVerified: true,
        });
        await store.update(update, tx);

        const actual = await directGetByHID(client, update.hid);
        expect(actual).toStrictEqual({
          ...update,
          updatedAt: NOW,
        });
      });

      it<HIDStoreContext>('should unset verified for changed email', async ({
        store,
        client,
        hid,
        tx,
      }) => {
        const original = nextHIDToEmail({ hid });
        await client.query(
          `INSERT INTO HID_TO_EMAIL (hid, email, email_verified) VALUES ('${original.hid}', '${original.email}', true);`,
        );
        const newEmail = nextValidEmail();
        const update = nextHIDToEmail({
          hid: original.hid,
          email: newEmail,
        });
        await store.update(update, tx);

        const actual = await directGetByHID(client, update.hid);
        expect(actual).toStrictEqual({
          ...update,
          emailVerified: false,
          updatedAt: NOW,
        });
      });

      it<HIDStoreContext>('should change verified to false for new unverified email', async ({
        store,
        client,
        hid,
        tx,
      }) => {
        const original = nextHIDToEmail({ hid });
        await client.query(
          `INSERT INTO HID_TO_EMAIL (hid, email, email_verified) VALUES ('${original.hid}', '${original.email.email}', true);`,
        );
        const update = nextHIDToEmail({
          hid: original.hid,
        });
        await store.update(update, tx);

        const actual = await directGetByHID(client, update.hid);
        expect(actual).toStrictEqual({
          ...update,
          emailVerified: false,
          updatedAt: NOW,
        });
      });

      it<HIDStoreContext>('update existing verified email with explicitly unverified one', async ({
        store,
        client,
        hid,
        tx,
      }) => {
        const original = nextHIDToEmail({ hid });
        await client.query(
          `INSERT INTO HID_TO_EMAIL (hid, email, email_verified) VALUES ('${original.hid}', '${original.email.email}', true);`,
        );

        const update = nextHIDToEmail({
          hid: original.hid,
          emailVerified: false,
        });
        await store.update(update, tx);

        const actual = await directGetByHID(client, update.hid);
        expect(actual).toStrictEqual({
          ...update,
          emailVerified: false,
          updatedAt: NOW,
        });
      });

      it<HIDStoreContext>('update existing verified email with new verified email', async ({
        store,
        client,
        hid,
        tx,
      }) => {
        const original = nextHIDToEmail({ hid });
        await client.query(
          `INSERT INTO HID_TO_EMAIL (hid, email, email_verified) VALUES ('${original.hid}', '${original.email.email}', true);`,
        );

        const update = nextHIDToEmail({
          hid: original.hid,
          emailVerified: true,
        });
        await store.update(update, tx);

        const actual = await directGetByHID(client, update.hid);
        expect(actual).toStrictEqual({
          ...update,
          emailVerified: true,
          updatedAt: NOW,
        });
      });

      it<HIDStoreContext>('leave existing verified hid/email unchanged', async ({
        store,
        client,
        hid,
        tx,
      }) => {
        const original = nextHIDToEmail({ hid });
        await client.query(
          `INSERT INTO HID_TO_EMAIL (hid, email, email_verified, updated_at) VALUES ('${
            original.hid
          }', '${original.email.email}', true, '${UPDATED_AT.toISOString()}');`,
        );

        const update = nextHIDToEmail({
          hid: original.hid,
          email: original.email,
        });
        await store.update(update, tx);

        const actual = await directGetByHID(client, update.hid);
        expect(actual).toStrictEqual({
          ...update,
          emailVerified: true,
          updatedAt: UPDATED_AT,
        });
      });

      it<HIDStoreContext>('should set verified when changing hid with verified state', async ({
        store,
        client,
        hidStore,
        hid,
        tx,
      }) => {
        const original = nextHIDToEmail({ hid });
        await client.query(
          `INSERT INTO HID_TO_EMAIL (hid, email, email_verified) VALUES ('${original.hid}', '${original.email.email}', false);`,
        );

        const otherHid = await hidStore.next();
        const update = nextHIDToEmail({
          hid: otherHid,
          email: original.email,
          emailVerified: true,
        });
        await store.update(update, tx);

        const actual = await directGetByHID(client, update.hid);
        expect(actual).toStrictEqual({
          ...update,
          emailVerified: true,
          updatedAt: NOW,
        });
      });

      it<HIDStoreContext>('should unset verified when changing hid', async ({
        store,
        client,
        hidStore,
        hid,
        tx,
      }) => {
        const original = nextHIDToEmail({ hid });
        await client.query(
          `INSERT INTO HID_TO_EMAIL (hid, email, email_verified) VALUES ('${original.hid}', '${original.email.email}', true);`,
        );

        const otherHid = await hidStore.next();
        const update = nextHIDToEmail({
          hid: otherHid,
          email: original.email,
        });
        await store.update(update, tx);

        const actual = await directGetByHID(client, update.hid);
        expect(actual).toStrictEqual({
          ...update,
          emailVerified: false,
          updatedAt: NOW,
        });
      });
    });
  });

  describe('hid-to-email delete scenarios', () => {
    it<HIDStoreContext>('delete email', async ({ store, client, hid, tx }) => {
      const input = nextHIDToEmail({ hid });
      await client.query(
        `INSERT INTO HID_TO_EMAIL (hid, email) VALUES ('${input.hid}', '${input.email.email}');`,
      );

      const hidToEmailLookup = {
        hid: input.hid,
        email: input.email,
        emailVerified: false,
        updatedAt: NOW,
      };
      expect(await store.delete(hidToEmailLookup, tx)).toBeTruthy();
      await expectHIDAndEmailToNotExist(client, input);
    });

    it<HIDStoreContext>('delete non-existent email', async ({ store, hid, tx }) => {
      const input = nextHIDToEmail({ hid });
      const hidToEmailLookup = {
        hid: input.hid,
        email: input.email,
        emailVerified: false,
        updatedAt: NOW,
      };
      expect(await store.delete(hidToEmailLookup, tx)).toBeFalsy();
    });
  });

  describe('auditing', () => {
    it<HIDStoreContext>('add is audited', async ({ store, client, hid, tx }) => {
      const input = nextHIDToEmail({ hid });

      await store.add(input, tx);

      await directGetByHID(client, input.hid);

      expect(await store.audit.findAllByTransaction(tx)).toMatchObject([
        {
          hid: input.hid,
          from: null,
          to: input.email,
          tx: tx.id,
          type: 'email',
        },
      ]);
      expect(await store.verificationAudit.findAllByTransaction(tx)).toMatchObject([]);
    });

    it<HIDStoreContext>('change email is audited', async ({ hidStore, store, client, hid, tx }) => {
      const original = nextHIDToEmail({ hid });
      await client.query(
        `INSERT INTO HID_TO_EMAIL (hid, email) VALUES ('${original.hid}', '${original.email.email}');`,
      );

      const otherEmail = nextValidEmail();
      const update = nextHIDToEmail({
        hid,
        email: otherEmail,
      });
      await store.update(update, tx);

      expect(await store.audit.findAllByTransaction(tx)).toMatchObject([
        {
          hid: original.hid,
          from: original.email,
          to: otherEmail,
          tx: tx.id,
          type: 'email',
        },
      ]);
      expect(await store.verificationAudit.findAllByTransaction(tx)).toMatchObject([]);
    });

    it<HIDStoreContext>('change verified state is audited', async ({ store, client, hid, tx }) => {
      const original = nextHIDToEmail({ hid });
      await client.query(
        `INSERT INTO HID_TO_EMAIL (hid, email) VALUES ('${original.hid}', '${original.email.email}');`,
      );

      const update = nextHIDToEmail({
        hid,
        email: original.email,
        emailVerified: true,
      });
      await store.update(update, tx);

      expect(await store.audit.findAllByTransaction(tx)).toMatchObject([]);
      expect(await store.verificationAudit.findAllByTransaction(tx)).toMatchObject([
        {
          hid: original.hid,
          from: {
            email: original.email.email,
            verified: false,
          },
          to: {
            email: original.email.email,
            verified: true,
          },
          tx: tx.id,
        },
      ]);
    });

    it<HIDStoreContext>('change hid is audited', async ({ hidStore, store, client, hid, tx }) => {
      const original = nextHIDToEmail({ hid });
      await client.query(
        `INSERT INTO HID_TO_EMAIL (hid, email) VALUES ('${original.hid}', '${original.email.email}');`,
      );

      const otherHid = await hidStore.next();
      const update = nextHIDToEmail({
        hid: otherHid,
        email: original.email,
      });
      await store.update(update, tx);

      expect(await store.audit.findAllByTransaction(tx)).toMatchObject([
        {
          hid: original.hid,
          from: original.email,
          to: null,
          tx: tx.id,
          type: 'email',
        },
        {
          hid: otherHid,
          from: null,
          to: original.email,
          tx: tx.id,
          type: 'email',
        },
      ]);
      expect(await store.verificationAudit.findAllByTransaction(tx)).toMatchObject([]);
    });

    it<HIDStoreContext>('change hid for existing verified email', async ({
      hidStore,
      store,
      client,
      hid,
      tx,
    }) => {
      const original = nextHIDToEmail({
        hid,
        emailVerified: true,
      });
      await client.query(
        `INSERT INTO HID_TO_EMAIL (hid, email, email_verified) VALUES ('${original.hid}', '${original.email.email}', ${original.emailVerified});`,
      );

      const otherHid = await hidStore.next();
      const update = nextHIDToEmail({
        hid: otherHid,
        email: original.email,
      });

      await store.update(update, tx);

      expect(await store.audit.findAllByTransaction(tx)).toMatchObject([
        {
          hid: original.hid,
          from: original.email,
          to: null,
          tx: tx.id,
          type: 'email',
        },
        {
          hid: otherHid,
          from: null,
          to: original.email,
          tx: tx.id,
          type: 'email',
        },
      ]);
      expect(await store.verificationAudit.findAllByTransaction(tx)).toMatchObject([
        {
          hid: otherHid,
          from: null,
          to: {
            email: original.email.email,
            verified: false,
          },
          tx: tx.id,
          type: 'email_verified',
        },
      ]);
    });

    it<HIDStoreContext>('change hid and verified is audited', async ({
      hidStore,
      store,
      client,
      hid,
      tx,
    }) => {
      const original = nextHIDToEmail({ hid });
      await client.query(
        `INSERT INTO HID_TO_EMAIL (hid, email) VALUES ('${original.hid}', '${original.email.email}');`,
      );

      const otherHid = await hidStore.next();
      const update = nextHIDToEmail({
        hid: otherHid,
        email: original.email,
        emailVerified: true,
      });
      await store.update(update, tx);

      expect(await store.audit.findAllByTransaction(tx)).toMatchObject([
        {
          hid: original.hid,
          from: original.email,
          to: null,
          tx: tx.id,
          type: 'email',
        },
        {
          hid: otherHid,
          from: null,
          to: original.email,
          tx: tx.id,
          type: 'email',
        },
      ]);
      expect(await store.verificationAudit.findAllByTransaction(tx)).toMatchObject([
        {
          hid: otherHid,
          from: null,
          to: {
            email: original.email.email,
            verified: true,
          },
          tx: tx.id,
          type: 'email_verified',
        },
      ]);
    });

    it<HIDStoreContext>('nothing is audited if nothing changes', async ({
      store,
      client,
      hid,
      tx,
    }) => {
      const original = nextHIDToEmail({ hid });
      await client.query(
        `INSERT INTO HID_TO_EMAIL (hid, email) VALUES ('${original.hid}', '${original.email}');`,
      );

      await store.update(original, tx);
      expect(await store.audit.findAllByTransaction(tx)).toMatchObject([]);
      expect(await store.verificationAudit.findAllByTransaction(tx)).toMatchObject([]);
    });

    it<HIDStoreContext>('delete is audited', async ({ store, client, hid, tx }) => {
      const input = nextHIDToEmail({ hid });
      await client.query(
        `INSERT INTO HID_TO_EMAIL (hid, email) VALUES ('${input.hid}', '${input.email.email}');`,
      );

      const hidToEmailLookup = {
        hid: input.hid,
        email: input.email,
        emailVerified: false,
        updatedAt: NOW,
      };
      await store.delete(hidToEmailLookup, tx);

      expect(await store.audit.findAllByTransaction(tx)).toMatchObject([
        {
          tx: tx.id,
          source: tx.source,
          hid: hid,
          from: input.email,
          to: null,
          type: 'email',
        },
      ]);
      expect(await store.verificationAudit.findAllByTransaction(tx)).toMatchObject([]);
    });

    it<HIDStoreContext>('delete is NOT audited if nothing deleted', async ({ store, tx }) => {
      const input = nextHIDToEmail();
      const hidToEmailLookup = {
        hid: input.hid,
        email: input.email,
        emailVerified: false,
        updatedAt: NOW,
      };
      await store.delete(hidToEmailLookup, tx);

      expect(await store.audit.findAllByTransaction(tx)).toMatchObject([]);
      expect(await store.verificationAudit.findAllByTransaction(tx)).toMatchObject([]);
    });

    it<HIDStoreContext>('delete is not audited if transaction source is RTBF', async ({
      store,
      client,
      hid,
      tx,
    }) => {
      const input = nextHIDToEmail({ hid });
      await client.query(
        `INSERT INTO HID_TO_EMAIL (hid, email) VALUES ('${input.hid}', '${input.email.email}');`,
      );
      const hidToEmailLookup = {
        hid: input.hid,
        email: input.email,
        emailVerified: false,
        updatedAt: NOW,
      };
      const rtbfTx = nextTx(RTBF_TRANSACTION_SOURCE_SUFFIX);
      await store.delete(hidToEmailLookup, rtbfTx);

      expect(await store.audit.findAllByTransaction(rtbfTx)).toMatchObject([]);
      expect(await store.verificationAudit.findAllByTransaction(rtbfTx)).toMatchObject([]);
    });
  });
});

const directGetByHID = async (client: pgsql.Pool, hid: HID): Promise<HIDToEmailLookup> => {
  const actual = await client.query(
    `SELECT hid, email, email_verified, updated_at FROM HID_TO_EMAIL WHERE hid = '${hid}';`,
  );
  expect(actual.rowCount).toBe(1);
  return {
    hid: actual.rows[0].hid,
    email: ValidEmail.newFromSafeInput(actual.rows[0].email),
    emailVerified: actual.rows[0].email_verified,
    updatedAt: new Date(actual.rows[0].updated_at),
  };
};

const directHIDDoesNotExist = async (client: pgsql.Pool, hid: HID): Promise<any> => {
  const actual = await client.query(`SELECT hid, email FROM HID_TO_EMAIL WHERE hid = '${hid}';`);
  expect(actual.rowCount).toBe(0);
};

const directEmailDoesNotExist = async (client: pgsql.Pool, email: ValidEmail): Promise<any> => {
  const actual = await client.query(
    `SELECT hid, email FROM HID_TO_EMAIL WHERE email = '${email.email}';`,
  );
  expect(actual.rowCount).toBe(0);
};

const expectHIDAndEmailToNotExist = async (client: pgsql.Pool, input: HIDToEmail): Promise<any> => {
  await directHIDDoesNotExist(client, input.hid);
  await directEmailDoesNotExist(client, input.email);
};
