import { HID, HIDToProfile, RTBF_TRANSACTION_SOURCE_SUFFIX, Transaction } from 'haven-hid-domain';
// @ts-ignore
import { nextHIDToProfile, nextTx } from 'haven-hid-domain/mock.js';
import * as pgsql from 'pg';
import { beforeEach, describe, expect, it } from 'vitest';
import { PgHIDStore } from '../../src/hid/hid-store.js';
import { getDatabase } from './db.js';
import { PgHIDToProfileStore } from '../../src/hid/hid-to-profile-store.js';

interface HIDStoreContext {
  store: PgHIDToProfileStore;
  hidStore: PgHIDStore;
  client: pgsql.Pool;
  hid: HID;
  tx: Transaction;
}

describe('hid-to-profile store scenarios', () => {
  beforeEach<HIDStoreContext>(async (context) => {
    context.hidStore = new PgHIDStore();
    context.hid = await context.hidStore.next();
    context.store = new PgHIDToProfileStore();
    context.client = await getDatabase();
    context.tx = nextTx('hid-to-profile-store');
  });

  describe('hid-to-profile add and find scenarios', () => {
    it<HIDStoreContext>('store a profile against a hid', async ({ store, client, hid, tx }) => {
      const input = nextHIDToProfile({
        hid: hid,
      });

      await store.add(input, tx);

      const actual = await directGetByHID(client, input.hid);
      expect(actual).toMatchObject(input);
    });

    it<HIDStoreContext>('get profile for hid', async ({ store, client, hid, tx }) => {
      const input = nextHIDToProfile({
        hid: hid,
      });
      await insertDirectly(client, input);

      const result = await store.findByHID(input.hid);
      expect(result).toStrictEqual(input);
    });

    it<HIDStoreContext>('add fails if hid does not exist', async ({ store, tx }) => {
      const input = nextHIDToProfile({
        hid: 'DOESNOTEXIST',
      });

      try {
        await store.add(input, tx);
      } catch (error: any) {
        expect(error.message).toMatch('Error adding profile to HID');
      }
    });
  });

  describe('hid-to-profile update scenarios', () => {
    it<HIDStoreContext>('update profile for hid', async ({ store, client, hid, tx }) => {
      const original = nextHIDToProfile({
        hid: hid,
      });
      await insertDirectly(client, original);

      const update = nextHIDToProfile({
        hid: original.hid,
      });
      const result = await store.update(update, tx);
      expect(result).toEqual(true);

      const actual = await directGetByHID(client, original.hid);
      expect(actual).toStrictEqual(update);
    });

    it<HIDStoreContext>('update returns false if nothing is changed', async ({
      store,
      client,
      hid,
      tx,
    }) => {
      const original = nextHIDToProfile({
        hid: hid,
      });
      await insertDirectly(client, original);

      const actual = await store.update(original, tx);
      expect(actual).toEqual(false);
    });

    it<HIDStoreContext>('update fails if hid is not found', async ({ store, hid, tx }) => {
      const original = nextHIDToProfile({
        hid: hid,
      });

      try {
        await store.update(original, tx);
      } catch (error: any) {
        expect(error.message).toMatch(/No record found to update for/);
      }
    });
  });

  describe('hid-to-profile delete scenarios', () => {
    it<HIDStoreContext>('delete profile', async ({ store, client, hid, tx }) => {
      const input = nextHIDToProfile({
        hid: hid,
      });
      await insertDirectly(client, input);

      expect(await store.delete(input, tx)).toBeTruthy();
      await directHIDDoesNotExist(client, input.hid);
    });
  });

  describe('auditing', () => {
    it<HIDStoreContext>('add is audited', async ({ store, client, hid, tx }) => {
      const input = nextHIDToProfile({
        hid: hid,
      });

      await store.add(input, tx);

      expect(await store.audit.findAllByTransaction(tx)).toEqual([
        {
          hid_audit_id: expect.anything(),
          source: expect.anything(),
          timestamp: expect.anything(),
          tx: tx.id,
          hid: input.hid,
          to: {
            name: input.name,
            title: input.title,
            firstName: input.firstName,
            lastName: input.lastName,
            phoneNumber: input.phoneNumber,
          },
          from: null,
          type: 'profile',
        },
      ]);
    });

    it<HIDStoreContext>('change profile is audited', async ({
      hidStore,
      store,
      client,
      hid,
      tx,
    }) => {
      const original = nextHIDToProfile({
        hid: hid,
      });
      await insertDirectly(client, original);

      const update = nextHIDToProfile({
        hid: original.hid,
      });
      await store.update(update, tx);

      expect(await store.audit.findAllByTransaction(tx)).toMatchObject([
        {
          tx: tx.id,
          source: tx.source,
          hid: original.hid,
          to: {
            name: update.name,
            title: update.title,
            firstName: update.firstName,
            lastName: update.lastName,
            phoneNumber: update.phoneNumber,
          },
          from: {
            name: original.name,
            title: original.title,
            firstName: original.firstName,
            lastName: original.lastName,
            phoneNumber: original.phoneNumber,
          },
          type: 'profile',
        },
      ]);
    });

    it<HIDStoreContext>('change profile audit contains changes only', async ({
      hidStore,
      store,
      client,
      hid,
      tx,
    }) => {
      const original = nextHIDToProfile({
        hid: hid,
      });
      await insertDirectly(client, original);

      const { phoneNumber } = nextHIDToProfile();

      const input = {
        hid: original.hid,
        phoneNumber: phoneNumber as string,
      };
      await store.update(input, tx);

      expect(await store.audit.findAllByTransaction(tx)).toMatchObject([
        {
          tx: tx.id,
          source: tx.source,
          hid: original.hid,
          to: {
            phoneNumber,
          },
          from: {
            phoneNumber: original.phoneNumber,
          },
          type: 'profile',
          hid_audit_id: expect.any(String),
          timestamp: expect.any(Date),
        },
      ]);
    });

    it<HIDStoreContext>('delete is audited', async ({ store, client, hid, tx }) => {
      const original = nextHIDToProfile({
        hid: hid,
      });
      await insertDirectly(client, original);

      await store.delete(original, tx);

      expect(await store.audit.findAllByTransaction(tx)).toMatchObject([
        {
          tx: tx.id,
          source: tx.source,
          hid: hid,
          from: {
            name: original.name,
            title: original.title,
            firstName: original.firstName,
            lastName: original.lastName,
            phoneNumber: original.phoneNumber,
          },
          to: null,
          type: 'profile',
        },
      ]);
    });

    it<HIDStoreContext>('delete is not audited if nothing happens', async ({
      store,
      client,
      hid,
      tx,
    }) => {
      const input = nextHIDToProfile();
      await store.delete(input, tx);

      expect(await store.audit.findAllByTransaction(tx)).toMatchObject([]);
    });

    it<HIDStoreContext>('delete is not audited if transaction source is RTBF', async ({
      store,
      client,
      hid,
      tx,
    }) => {
      const original = nextHIDToProfile({
        hid: hid,
      });
      await insertDirectly(client, original);
      const rtbfTx = nextTx(RTBF_TRANSACTION_SOURCE_SUFFIX);
      await store.delete(original, rtbfTx);

      expect(await store.audit.findAllByTransaction(rtbfTx)).toMatchObject([]);
    });
  });
});

const directGetByHID = async (client: pgsql.Pool, hid: HID): Promise<HIDToProfile> => {
  const actual = await client.query(`SELECT * FROM HID_TO_PROFILE WHERE hid = '${hid}';`);
  expect(actual.rowCount).toBe(1);
  const row = actual.rows[0];
  return {
    hid: row.hid,
    name: row.name,
    title: row.title,
    firstName: row.first_name,
    lastName: row.last_name,
    phoneNumber: row.phone_number,
  };
};

const directHIDDoesNotExist = async (client: pgsql.Pool, hid: HID): Promise<any> => {
  const actual = await client.query(`SELECT hid FROM HID_TO_PROFILE WHERE hid = '${hid}';`);
  expect(actual.rowCount).toBe(0);
};

const insertDirectly = async (client: pgsql.Pool, original: HIDToProfile) => {
  await client.query(`INSERT INTO HID_TO_PROFILE (hid, name, title, first_name, last_name, phone_number)
                      VALUES ('${original.hid}', '${original.name}', '${original.title}', '${original.firstName}',
                              '${original.lastName}', '${original.phoneNumber}');`);
};
