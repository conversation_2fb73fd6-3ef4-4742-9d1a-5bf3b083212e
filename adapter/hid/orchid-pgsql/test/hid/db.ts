import * as dotenv from 'dotenv';
import findConfig from 'find-config';
import * as pgsql from 'pg';

export class Database {
  private static _instance: Database;

  private hasBeenReset: boolean = false;
  public readonly pool: pgsql.Pool;

  private constructor() {
    const path: string = findConfig('.env') || '.env';
    dotenv.config({ path });

    const { Pool } = require('pg');

    this.pool = new Pool({
      connectionString: process.env.DATABASE_TEST_URL,
      ssl: false,
      max: 20,
      idleTimeoutMillis: 1000,
      connectionTimeoutMillis: 1000,
    });
  }

  public async reset() {
    if (!this.hasBeenReset) {
      this.hasBeenReset = true;

      await this.pool.query('DELETE FROM HID_TO_PROFILE;');
      await this.pool.query('DELETE FROM HID_TO_PASSWORD;');
      await this.pool.query('DELETE FROM HID_TO_SEAWARE_CLIENT;');
      await this.pool.query('DELETE FROM HID_TO_PLOT_OWNER;');
      await this.pool.query('DELETE FROM HID_TO_EMAIL;');
      await this.pool.query('DELETE FROM HID_TO_ADDRESS;');
      await this.pool.query('DELETE FROM HID_AUDIT;');
      await this.pool.query('DELETE FROM HID;');
      await this.pool.query('DELETE FROM AUTHENTICATION_AUDIT;');
      await this.pool.query('DELETE FROM SECURITY_AUDIT;');
    }
  }

  public static get Instance() {
    return this._instance || (this._instance = new this());
  }
}

export const getDatabase = async (): Promise<pgsql.Pool> => {
  return Database.Instance.pool;
};

export const resetDatabase = async (): Promise<void> => {
  await Database.Instance.reset();
};
