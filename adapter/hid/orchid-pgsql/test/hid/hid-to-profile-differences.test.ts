// @ts-ignore
import { nextHIDToProfile } from 'haven-hid-domain/mock.js';
import { describe, expect, it } from 'vitest';
import { getChangedProperties, isChanged } from '../../src/hid/hid-to-profile-differences.js';

describe('hid-to-profile differences', () => {
  describe('getChangedProperties', () => {
    it('should return empty partial when nothing changed', () => {
      const input = nextHIDToProfile();
      expect(getChangedProperties(input, input)).toEqual({});
    });

    it('returns name when only name has changed', () => {
      const original = nextHIDToProfile();
      const { name } = nextHIDToProfile() as { name: string };
      expect(getChangedProperties(original, { name })).toEqual({ name: original.name });
    });

    it('returns phone when only phone has changed', () => {
      const original = nextHIDToProfile();
      const { phoneNumber } = nextHIDToProfile() as { phoneNumber: string };
      expect(
        getChangedProperties(original, {
          phoneNumber,
        }),
      ).toEqual({
        phoneNumber: original.phoneNumber,
      });
    });

    it('returns multiple properties when several have changed', () => {
      const original = nextHIDToProfile();
      const { phoneNumber, lastName, firstName } = nextHIDToProfile() as {
        phoneNumber: string;
        lastName: string;
        firstName: string;
      };
      expect(
        getChangedProperties(original, {
          lastName,
          firstName,
          phoneNumber,
        }),
      ).toEqual({
        phoneNumber: original.phoneNumber,
        firstName: original.firstName,
        lastName: original.lastName,
      });
    });

    it('returns all properties when all have changed', () => {
      const original = nextHIDToProfile();
      const { title, name, lastName, firstName, phoneNumber } = nextHIDToProfile() as {
        title: string;
        name: string;
        lastName: string;
        firstName: string;
        phoneNumber: string;
      };
      expect(
        getChangedProperties(original, {
          title,
          name,
          lastName,
          firstName,
          phoneNumber,
        }),
      ).toEqual({
        title: original.title,
        name: original.name,
        phoneNumber: original.phoneNumber,
        firstName: original.firstName,
        lastName: original.lastName,
      });
    });
  });

  describe('isChanged checks if anything has changed', () => {
    it('returns false for empty object', () => {
      expect(isChanged({})).toBeFalsy();
    });
    it('returns true for object with properties', () => {
      expect(isChanged({ name: 'xxx' })).toBeTruthy();
      expect(isChanged({ name: '' })).toBeTruthy();
    });
  });
});
