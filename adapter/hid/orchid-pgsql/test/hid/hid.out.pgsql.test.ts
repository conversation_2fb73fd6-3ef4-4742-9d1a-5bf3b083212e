import { describe, expect, it, vi } from 'vitest';
import { PgHIDForEmailPlotAndSeaWare } from '../../src/hid/hid.out.pgsql.js';
import {
  nextPlotOwnerWithEmail,
  nextSeaWareClientWithEmail,
  nextTx,
  nextValidEmail,
} from 'haven-hid-domain/mock.js';
import { getDatabase } from './db.js';
import { errAsync, okAsync } from 'neverthrow';

describe('hid store scenarios', async () => {
  vi.mock('../../src/hid/hid-to-email-store.js', () => {
    const PgHIDToEmailStore = vi.fn(() => ({
      add: vi.fn().mockImplementation(() => {
        return errAsync(new Error('oops'));
      }),
      findByEmail: vi.fn(() => okAsync(undefined)),
    }));
    return { PgHIDToEmailStore };
  });

  const client = await getDatabase();

  it('failed transaction rolls back creation of HID', async () => {
    const before = await countHids();
    const store = PgHIDForEmailPlotAndSeaWare();
    const email = nextValidEmail();
    const result = await store.getHIDForEmail(email, undefined, nextTx('transaction-test'));
    const after = await countHids();
    expect(before).toEqual(after);
    expect(result).toBeFalsy();
  });

  it('failed transaction rolls back creation of HID for email and plot', async () => {
    const before = await countHids();
    const store = PgHIDForEmailPlotAndSeaWare();
    const emailAndOwner = nextPlotOwnerWithEmail();
    try {
      await store.getHIDForEmailAndPlot(
        emailAndOwner.email,
        undefined,
        emailAndOwner.plotOwnerId,
        nextTx('transaction-test'),
      );
    } catch (error) {
      expect(error).toBeTruthy();
    }
    const after = await countHids();
    expect(before).toEqual(after);
  });

  it('failed transaction rolls back creation of HID for email and seaware', async () => {
    const before = await countHids();
    const store = PgHIDForEmailPlotAndSeaWare();
    const emailAndSeaware = nextSeaWareClientWithEmail();
    try {
      await store.getHIDForEmailAndSeaWare(
        emailAndSeaware.email,
        undefined,
        emailAndSeaware.seaWareClientId,
        nextTx('transaction-test'),
      );
    } catch (error) {
      expect(error).toBeTruthy();
    }

    const after = await countHids();
    expect(before).toEqual(after);
  });

  const countHids = async () => {
    const result = await client.query(`SELECT count(1) FROM HID;`);
    return result.rows[0].count;
  };
});
