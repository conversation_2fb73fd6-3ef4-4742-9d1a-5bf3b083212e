import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  nextValidEmail,
  nextHID,
  // @ts-ignore
} from 'haven-hid-domain/mock.js';
import { PgAuthenticationAuditStore } from '../../src/hid/authenticate.audit.store.js';
import {
  AuthenticationAudit,
  AuthenticationMethodType,
  AuthenticationOutcome,
} from 'haven-hid-domain';
import { getDatabase } from './db.js';
import { logger } from '@havenengineering/module-haven-logging';

const info = vi.spyOn(logger, 'info');
const error = vi.spyOn(logger, 'error');

describe('Authentication Audit tests', async () => {
  const authenticationAudit = new PgAuthenticationAuditStore();
  const client = await getDatabase();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('stores the audit record', async () => {
    const hid = nextHID();
    const email = nextValidEmail().email;
    const record: AuthenticationAudit = {
      hid,
      email,
      outcome: AuthenticationOutcome.SUCCESS,
      ipAddress: '127.0.0.1',
      clientId: 'client-id',
      userAgent: 'Chrome',
      method: AuthenticationMethodType.IDENTITY,
    };
    await authenticationAudit.add(record);

    const stored = await lookupRecord(record.email);
    expect(stored.length).toBe(1);
    expect(stored[0]).toEqual({
      email: record.email,
      hid: record.hid,
      method: record.method,
      outcome: record.outcome,
      user_agent: record.userAgent,
      ip_address: record.ipAddress,
      timestamp: expect.anything(),
      client_id: record.clientId,
      id: expect.anything(),
    });
    expect(stored[0].timestamp).not.toBeUndefined();
    expect(error).not.toHaveBeenCalled();
    expect(info).toHaveBeenCalledWith('Authentication SUCCESS', {
      outcome: 'SUCCESS',
      client: record.clientId,
      ip: record.ipAddress,
      ua: record.userAgent,
    });
  });

  it('stores the audit record without a HID', async () => {
    const email = nextValidEmail().email;
    const record: AuthenticationAudit = {
      email,
      outcome: AuthenticationOutcome.FAILED,
      ipAddress: '127.0.0.1',
      clientId: 'client-id',
      userAgent: 'Chrome',
      method: AuthenticationMethodType.IDENTITY,
    };
    await authenticationAudit.add(record);

    const stored = await lookupRecord(record.email);
    expect(stored.length).toBe(1);
    expect(stored[0].hid).toBeNull();
  });

  it('trims excessively long userAgents', async () => {
    const email = nextValidEmail().email;
    const record: AuthenticationAudit = {
      email,
      outcome: AuthenticationOutcome.FAILED,
      ipAddress: '127.0.0.1',
      clientId: 'client-id',
      userAgent: 'x'.repeat(256),
      method: AuthenticationMethodType.IDENTITY,
    };
    await authenticationAudit.add(record);

    const stored = await lookupRecord(record.email);
    expect(stored.length).toBe(1);
    expect(stored[0].user_agent.length).toEqual(255);
    expect(stored[0].user_agent).toEqual(record.userAgent.substring(0, 255));
  });

  it('exception recording audit is logged but ignored', async () => {
    const email = nextValidEmail().email;
    await authenticationAudit.add({
      email,
      outcome: 'SUCCESS',
    } as AuthenticationAudit);
    expect(info).toHaveBeenCalledWith('Authentication SUCCESS', {
      outcome: 'SUCCESS',
      client: undefined,
      ip: undefined,
      ua: undefined,
    });
    expect(error).toHaveBeenCalledWith(
      'Could not audit authentication',
      new TypeError("Cannot read properties of undefined (reading 'substring')"),
    );
  });

  it('obfuscate the audit record', async () => {
    const hid = nextHID();
    const email = nextValidEmail().email;
    const record: AuthenticationAudit = {
      hid,
      email,
      outcome: AuthenticationOutcome.SUCCESS,
      ipAddress: '127.0.0.1',
      clientId: 'client-id',
      userAgent: 'Chrome',
      method: AuthenticationMethodType.IDENTITY,
    };
    await authenticationAudit.add(record);
    await authenticationAudit.obfuscate({
      email,
      hid,
    });

    const stored = await lookupRecordByHid(hid);
    expect(stored.length).toBe(1);
    expect(stored[0]).toEqual({
      email: 'FORGOTTEN',
      hid: record.hid,
      method: record.method,
      outcome: record.outcome,
      user_agent: record.userAgent,
      ip_address: 'FORGOTTEN',
      timestamp: expect.anything(),
      client_id: record.clientId,
      id: expect.anything(),
    });
  });

  it('obfuscate the audit record without a HID', async () => {
    const email = nextValidEmail().email;
    const record: AuthenticationAudit = {
      email,
      outcome: AuthenticationOutcome.SUCCESS,
      ipAddress: '127.0.0.1',
      clientId: 'client-id',
      userAgent: 'Chrome',
      method: AuthenticationMethodType.IDENTITY,
    };
    await authenticationAudit.add(record);
    await authenticationAudit.obfuscate({
      email,
    });

    const stored = await lookupRecord(email);
    expect(stored.length).toBe(0);
  });

  const lookupRecord = async (email: string) => {
    const actual = await client.query(`SELECT *
                                       FROM AUTHENTICATION_AUDIT
                                       WHERE email = '${email}';`);
    return actual.rows;
  };

  const lookupRecordByHid = async (hid: string) => {
    const actual = await client.query(`SELECT *
                                       FROM AUTHENTICATION_AUDIT
                                       WHERE hid = '${hid}';`);
    return actual.rows;
  };
});
