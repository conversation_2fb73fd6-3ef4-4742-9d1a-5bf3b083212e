import {
  <PERSON>t,
  <PERSON><PERSON>,
  HIDToEmailLookup,
  HIDToEmailStore,
  HIDToEmailUpdate,
  isRTBFTransaction,
  SystemError,
  Transaction,
  ValidEmail,
} from 'haven-hid-domain';
import { Db } from '../../db/db.js';
import { HidToEmailAuditStore } from './hid-to-email-audit.js';
import { toSystemError } from './error.js';
import { HidToEmailVerifiedAuditStore } from './hid-to-emailverified-audit.js';

type OptionalEmailHID = {
  email?: string;
  hid?: HID;
};
type EmailHIDUpdate = {
  email?: string;
  hid?: HID;
  emailVerified?: boolean;
};

export class PgHIDToEmailStore implements HIDToEmailStore {
  audit = new HidToEmailAuditStore();
  verificationAudit = new HidToEmailVerifiedAuditStore();

  async add(input: HIDToEmailUpdate, tx: Transaction): Promise<void> {
    try {
      await this.write(input);
      await this.audit.add({
        tx: tx.id,
        source: tx.source,
        hid: input.hid,
        from: null,
        to: input.email,
      });
      if (input.emailVerified) {
        // No need to audit an email has been added unverified
        await this.verificationAudit.add({
          tx: tx.id,
          source: tx.source,
          hid: input.hid,
          from: null,
          to: {
            email: input.email.email,
            verified: input.emailVerified,
          },
        });
      }
    } catch (error) {
      throw toSystemError('Error adding email to HID')(error);
    }
  }

  async delete(input: HIDToEmailLookup, tx: Transaction): Promise<boolean> {
    try {
      const deleted =
        (await this.deleteRow({
          email: input.email.email,
          hid: input.hid,
        })) > 0;
      if (deleted && !isRTBFTransaction(tx))
        await this.audit.add({
          tx: tx.id,
          source: tx.source,
          hid: input.hid,
          from: input.email,
          to: null,
        });
      return deleted;
    } catch (error) {
      throw toSystemError('Error deleting email')(error);
    }
  }

  update(input: HIDToEmailUpdate, tx: Transaction): Promise<boolean> {
    return this.updateHIDOrEmail(input, tx);
  }

  async findByEmail(input: ValidEmail): Promise<HIDToEmailLookup | undefined> {
    try {
      const data = await this.find({ email: input.email });
      return data;
    } catch (error) {
      throw toSystemError('Error finding email')(error);
    }
  }

  async findByHID(input: HID): Promise<HIDToEmailLookup | undefined> {
    try {
      const data = await this.find({ hid: input });
      return data;
    } catch (error) {
      throw toSystemError('Error finding email by HID')(error);
    }
  }

  async auditByTransaction(tx: Transaction): Promise<Audit<ValidEmail>[]> {
    try {
      const data = await this.audit.findAllByTransaction(tx);
      return data;
    } catch (error) {
      throw toSystemError('Error fetching audit')(error);
    }
  }

  private async find(condition: OptionalEmailHID): Promise<HIDToEmailLookup | undefined> {
    const result = await this.select().where(condition);
    return this.toResult(result);
  }

  private select() {
    return Db.instance.hidToEmail.select('hid', 'email', 'email_verified', 'updated_at');
  }

  private toResult(result: { [key: string]: unknown }[]): HIDToEmailLookup | undefined {
    if (result.length === 0) {
      return undefined;
    }

    const values = Array.from(result.values());
    const hidToEmail: HIDToEmailLookup = {
      hid: values[0].hid as string,
      email: ValidEmail.newFromSafeInput(values[0].email as string),
      updatedAt: new Date(values[0].updated_at as number),
      emailVerified: values[0].email_verified as boolean,
    };
    return hidToEmail;
  }

  private async write(input: HIDToEmailUpdate): Promise<void> {
    const updatedAt = input.updatedAt || new Date();
    const data = {
      hid: input.hid,
      email: input.email.email,
      emailVerified: input.emailVerified || false,
      createdAt: updatedAt,
      updatedAt: updatedAt.toISOString(),
    };
    await Db.instance.hidToEmail.create(data);
  }

  private async updateHIDOrEmail(input: HIDToEmailUpdate, tx: Transaction): Promise<boolean> {
    const emailUpdated = await this.changeEmail(input, tx);
    if (emailUpdated !== undefined) {
      return emailUpdated;
    }

    const hidUpdated = await this.changeHid(input, tx);
    if (hidUpdated !== undefined) {
      return hidUpdated;
    }

    throw new SystemError(`No record found to update for [{hid: ${input.hid}]`);
  }

  private async changeEmail(
    input: HIDToEmailUpdate,
    tx: Transaction,
  ): Promise<boolean | undefined> {
    const data = await this.findByHID(input.hid);
    if (!data) {
      return undefined;
    }

    try {
      if (this.isEmailOrVerifiedStateUnchanged(data, input)) {
        return false;
      }

      const emailVerified =
        input.emailVerified || (data.emailVerified && data.email.equals(input.email));
      const emailVerifiedChanged = emailVerified !== data.emailVerified;

      await this.updateRow(
        { hid: input.hid },
        {
          email: input.email.email,
          emailVerified: emailVerified,
        },
        input.updatedAt,
      );
      if (!data.email.equals(input.email)) {
        await this.audit.add({
          tx: tx.id,
          source: tx.source,
          hid: input.hid,
          from: data.email,
          to: input.email,
        });
      }
      if (emailVerifiedChanged) {
        await this.verificationAudit.add({
          tx: tx.id,
          source: tx.source,
          hid: input.hid,
          from: {
            email: data.email.email,
            verified: data.emailVerified,
          },
          to: {
            email: data.email.email,
            verified: emailVerified,
          },
        });
      }
      return true;
    } catch (error) {
      throw toSystemError('Error storing email against HID')(error);
    }
  }

  private isEmailOrVerifiedStateUnchanged(
    existing: {
      email: ValidEmail;
      emailVerified: boolean;
    },
    change: {
      email: ValidEmail;
      emailVerified?: boolean;
    },
  ): boolean {
    return (
      existing.email.equals(change.email) &&
      (change.emailVerified === undefined || change.emailVerified === existing.emailVerified)
    );
  }

  private async changeHid(input: HIDToEmailUpdate, tx: Transaction): Promise<boolean | undefined> {
    const data = await this.findByEmail(input.email);
    if (!data) {
      return undefined;
    }

    try {
      const emailVerified = input.emailVerified || false;
      const emailVerifiedChanged = data.emailVerified !== emailVerified;

      await this.updateRow(
        { email: input.email.email },
        {
          hid: input.hid,
          emailVerified: emailVerified,
        },
        input.updatedAt,
      );
      await this.audit.add({
        tx: tx.id,
        source: tx.source,
        hid: data.hid,
        from: input.email,
        to: null,
      });
      await this.audit.add({
        tx: tx.id,
        source: tx.source,
        hid: input.hid,
        from: null,
        to: input.email,
      });
      if (emailVerifiedChanged) {
        await this.verificationAudit.add({
          tx: tx.id,
          source: tx.source,
          hid: input.hid,
          from: null,
          to: {
            email: data.email.email,
            verified: emailVerified,
          },
        });
      }
      return true;
    } catch (error) {
      throw toSystemError('Error storing email against HID')(error);
    }
  }

  private async deleteRow(where: OptionalEmailHID): Promise<number> {
    return Db.instance.hidToEmail.where(where).delete();
  }

  private async updateRow(
    where: OptionalEmailHID,
    data: EmailHIDUpdate,
    updatedAt: Date | undefined,
  ): Promise<number> {
    const timestamp = updatedAt || new Date();
    const update = {
      updatedAt: timestamp,
      ...data,
    };
    return Db.instance.hidToEmail.where(where).update(update);
  }
}
