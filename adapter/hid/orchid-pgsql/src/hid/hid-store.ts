import {
  AllHIDData,
  HID,
  HIDGenerator,
  HIDStore,
  NanoidHIDGenerator,
  PlotOwnerId,
  SeaWareClientId,
  Transaction,
  ValidEmail,
} from 'haven-hid-domain';
import { Db } from '../../db/db.js';
import { toSystemError } from './error.js';
import { HidAuditStore } from './hid-audit.js';

export class PgHIDStore implements HIDGenerator, HIDStore {
  audit = new HidAuditStore();

  async findAllForEmail(input: ValidEmail): Promise<AllHIDData> {
    try {
      const data = await this.find({
        'hid_to_email.email': input.email,
      });
      return data;
    } catch (error) {
      throw toSystemError('Error finding all for Email')(error);
    }
  }

  async findAllForHID(input: HID): Promise<AllHIDData> {
    try {
      const data = await this.find({ 'hid.hid': input });
      return data;
    } catch (error) {
      throw toSystemError('Error finding all for HID')(error);
    }
  }

  async findAllForPlotOwnerId(input: PlotOwnerId): Promise<AllHIDData> {
    try {
      const data = await this.find({
        'hid_to_plot_owner.plot_owner_id': input,
      });
      return data;
    } catch (error) {
      throw toSystemError('Error finding all for plot owner id')(error);
    }
  }

  async findAllForSeaWareClientId(input: SeaWareClientId): Promise<AllHIDData> {
    try {
      const data = await this.find({
        'hid_to_seaware_client.seaware_client_id': input,
      });
      return data;
    } catch (error) {
      throw toSystemError('Error finding all for seaware client id')(error);
    }
  }

  async delete(hid: HID, tx: Transaction): Promise<boolean> {
    try {
      const isDeleted = (await this.deleteRow({ hid })) > 0;
      if (isDeleted)
        await this.audit.add({
          tx: tx.id,
          source: tx.source,
          hid,
          from: hid,
          to: null,
        });
      return isDeleted;
    } catch (error) {
      throw toSystemError('Error deleting hid')(error);
    }
  }

  async next(): Promise<HID> {
    const hid = await new NanoidHIDGenerator().next();
    try {
      await this.write(hid);
      return hid;
    } catch (error) {
      throw toSystemError('Error generating HID')(error);
    }
  }

  private async write(hid: HID): Promise<void> {
    const table = Db.instance.hid;
    await table.create({ hid: hid });
  }

  private async deleteRow(where: object): Promise<number> {
    return Db.instance.hid.where(where).delete();
  }

  private async find(condition: object): Promise<AllHIDData> {
    const tables = Db.instance;

    const query = tables.hid
      .leftJoin(tables.hidToEmail, 'hid.hid', 'hid_to_email.hid')
      .leftJoin(tables.hidToPlotOwner, 'hid.hid', 'hid_to_plot_owner.hid')
      .leftJoin(tables.hidToSeaWareClient, 'hid.hid', 'hid_to_seaware_client.hid')
      .leftJoin(tables.hidToProfile, 'hid.hid', 'hid_to_profile.hid')
      .leftJoin(tables.hidToPassword, 'hid.hid', 'hid_to_password.hid')
      .where(condition)
      .select({
        hid: 'hid.hid',
        email: 'hid_to_email.email',
        updated_at: 'hid_to_email.updated_at',
        plot_owner_id: 'hid_to_plot_owner.plot_owner_id',
        seaware_client_id: 'hid_to_seaware_client.seaware_client_id',
        profile: 'hid_to_profile.hid',
        password: 'hid_to_password.hid',
      });

    const result = await query;
    return this.toResult(result);
  }

  private toResult(data: unknown[]): AllHIDData {
    const result = new AllHIDData();
    if (data.length > 0) {
      const row = Array.from(data.values())[0] as { [key: string]: string };
      result.hid = row.hid || undefined;
      result.email = this.toValidEmail(row.email);
      result.emailUpdatedAt = row.updated_at ? new Date(row.updated_at) : undefined;
      result.plotOwnerId = row.plot_owner_id ? +row.plot_owner_id : undefined;
      result.seaWareClientId = row.seaware_client_id ? +row.seaware_client_id : undefined;
      result.identityExists = row.profile !== null || row.password !== null;
    }

    return result;
  }

  private toValidEmail(input: null | string): ValidEmail | undefined {
    return input === null ? undefined : ValidEmail.newFromSafeInput(input);
  }
}
