import { AuditStore } from './audit.store.js';
import { HIDToAddressAudit } from 'haven-hid-domain';
import { Db } from '../../db/db.js';
import { DbTable } from 'orchid-orm';
import { HidAuditTable } from '../tables/hid-audit.table.js';

export class HIDToAddressAuditStore extends AuditStore<HIDToAddressAudit> {
  getTable = (): DbTable<typeof HidAuditTable> => Db.instance.hidAudit;
  getType = () => 'address';
  fromValue = (value: string): HIDToAddressAudit => JSON.parse(value) as HIDToAddressAudit;
  toValue = ({
    address_line1,
    address_line2,
    address_city,
    address_county,
    address_postcode,
    address_country,
  }: HIDToAddressAudit): string =>
    JSON.stringify({
      address_line1,
      address_line2,
      address_city,
      address_county,
      address_postcode,
      address_country,
    });
}
