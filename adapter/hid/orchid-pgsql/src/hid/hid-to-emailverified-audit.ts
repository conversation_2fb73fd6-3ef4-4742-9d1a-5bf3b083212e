import { DbTable } from 'orchid-orm';
import { Db } from '../../db/db.js';
import { HidAuditTable } from '../tables/hid-audit.table.js';
import { AuditStore } from './audit.store.js';

export type EmailVerification = {
  email: string;
  verified: boolean;
};

export class HidToEmailVerifiedAuditStore extends AuditStore<EmailVerification> {
  getTable = (): DbTable<typeof HidAuditTable> => Db.instance.hidAudit;
  getType = () => 'email_verified';
  fromValue = (value: string): EmailVerification => JSON.parse(value) as EmailVerification;
  toValue = (value: EmailVerification): string => JSON.stringify(value);
}
