import { logger } from '@havenengineering/module-haven-logging';
import {
  AuthenticationAudit,
  AuthenticationAuditStore,
  ObfuscateAuthenticationAuditParams,
} from 'haven-hid-domain';
import { Db, HidTables } from '../../db/db.js';
import { DbTable } from 'orchid-orm';
import { toSystemError } from './error.js';

export class PgAuthenticationAuditStore implements AuthenticationAuditStore {
  constructor() {}

  private getTable(): DbTable<HidTables['authenticationAudit']> {
    return Db.instance.authenticationAudit;
  }

  async add(record: AuthenticationAudit): Promise<void> {
    try {
      const message = `Authentication ${record.outcome}`;
      const context = {
        client: record.clientId,
        outcome: record.outcome,
        ip: record.ipAddress,
        ua: record.userAgent,
      };
      logger.info(message, context);
      await this.getTable().create({
        ...record,
        userAgent: record.userAgent.substring(0, 255),
      });
    } catch (e: unknown) {
      toSystemError('Could not audit authentication')(e);
    }
  }

  async obfuscate({ email, hid }: ObfuscateAuthenticationAuditParams): Promise<void> {
    try {
      await this.getTable()
        .where((q) => (hid ? q.where({ email }).orWhere({ hid }) : q.where({ email })))
        .update({
          email: 'FORGOTTEN',
          ipAddress: 'FORGOTTEN',
        });
    } catch (e: unknown) {
      throw toSystemError('Could not obfuscate authentication audit')(e);
    }
  }
}
