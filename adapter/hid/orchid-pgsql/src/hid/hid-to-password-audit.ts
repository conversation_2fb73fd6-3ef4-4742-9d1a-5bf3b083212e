import { AuditStore } from './audit.store.js';
import { HIDToPasswordAudit } from 'haven-hid-domain';
import { Db } from '../../db/db.js';
import { DbTable } from 'orchid-orm';
import { HidAuditTable } from '../tables/hid-audit.table.js';
import { PasswordStrength } from '@havenengineering/module-shared-password-strength';

export class HIDToPasswordAuditStore extends AuditStore<HIDToPasswordAudit> {
  getTable = (): DbTable<typeof HidAuditTable> => Db.instance.hidAudit;
  getType = () => 'password';
  fromValue = (value: PasswordStrength): HIDToPasswordAudit => ({ strength: value });
  toValue = (audit: HIDToPasswordAudit): PasswordStrength => audit.strength;
}
