import { HID } from 'haven-hid-domain';
import { DbTable } from 'orchid-orm';
import { Db } from '../../db/db.js';
import { HidAuditTable } from '../tables/hid-audit.table.js';
import { AuditStore } from './audit.store.js';

export class HidAuditStore extends AuditStore<HID> {
  getTable = (): DbTable<typeof HidAuditTable> => Db.instance.hidAudit;
  getType = () => 'hid';
  fromValue = (value: string): HID => value;
  toValue = (value: HID): string => value;
}
