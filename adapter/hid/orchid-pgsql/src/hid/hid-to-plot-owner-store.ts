import {
  <PERSON><PERSON>,
  HIDToPlotOwner,
  HIDToPlotOwnerStore,
  PlotOwnerId,
  SystemError,
  Transaction,
  isRTBFTransaction,
} from 'haven-hid-domain';
import { isDeepStrictEqual } from 'util';
import { Db } from '../../db/db.js';
import { HidToPlotOwnerAudit } from './hid-to-plot-audit.js';
import { toSystemError } from './error.js';

export class PgHIDToPlotOwnerStore implements HIDToPlotOwnerStore {
  audit = new HidToPlotOwnerAudit();

  async add(input: HIDToPlotOwner, tx: Transaction): Promise<void> {
    try {
      await this.write(input);
      await this.audit.add({
        tx: tx.id,
        source: tx.source,
        hid: input.hid,
        from: null,
        to: input.plotOwnerId,
      });
    } catch (error) {
      throw toSystemError('Error storing Plot Owner Id against HID')(error);
    }
  }

  async delete(input: HIDToPlotOwner, tx: Transaction): Promise<boolean> {
    try {
      const count = await this.deleteRow({
        plotOwnerId: input.plotOwnerId,
      });
      if (count > 0 && !isRTBFTransaction(tx))
        await this.audit.add({
          tx: tx.id,
          source: tx.source,
          hid: input.hid,
          from: input.plotOwnerId,
          to: null,
        });
      return count > 0;
    } catch (error) {
      throw toSystemError('Error deleting Plot Owner Id from HID')(error);
    }
  }

  async update(input: HIDToPlotOwner, tx: Transaction): Promise<boolean> {
    return this.updateHIDOrPlotOwner(input, tx);
  }

  async findByPlotOwnerId(input: PlotOwnerId): Promise<HIDToPlotOwner | undefined> {
    try {
      const data = await this.find({ plotOwnerId: input });
      return data;
    } catch (error) {
      throw toSystemError('Error finding HID by Plot Owner Id')(error);
    }
  }

  async findByHID(input: HID): Promise<HIDToPlotOwner | undefined> {
    try {
      const data = await this.find({ hid: input });
      return data;
    } catch (error) {
      toSystemError('Error finding Plot Owner Id by HID')(error);
    }
  }

  private async find(condition: object): Promise<HIDToPlotOwner | undefined> {
    const result = await this.select().where(condition);
    return this.toResult(result as HIDToPlotOwner[]);
  }

  private select() {
    const table = Db.instance.hidToPlotOwner;
    return table.select('hid', 'plotOwnerId');
  }

  private toResult(result: HIDToPlotOwner[]) {
    if (result.length === 0) {
      return undefined;
    } else {
      const values = Array.from(result.values());
      return {
        hid: values[0].hid,
        plotOwnerId: values[0].plotOwnerId,
      };
    }
  }

  private async write(input: HIDToPlotOwner): Promise<void> {
    const table = Db.instance.hidToPlotOwner;
    await table.create({
      hid: input.hid,
      plotOwnerId: input.plotOwnerId,
    });
  }

  private async updateHIDOrPlotOwner(input: HIDToPlotOwner, tx: Transaction): Promise<boolean> {
    const updatedByHid = await this.updateByHID(input, tx);
    if (updatedByHid) {
      return updatedByHid;
    }

    const updatedByPlotOwnerId = await this.updateByPlotOwnerId(input, tx);
    if (updatedByPlotOwnerId) {
      return updatedByPlotOwnerId;
    }

    throw new SystemError(`No record found to update for [${JSON.stringify(input)}]`);
  }

  private async updateByHID(input: HIDToPlotOwner, tx: Transaction): Promise<boolean> {
    const data = await this.findByHID(input.hid);
    if (!data) {
      return false;
    }
    if (isDeepStrictEqual(data, input)) {
      throw new SystemError(`Update already exists [${JSON.stringify(input)}]`);
    }

    try {
      await this.updateRow(
        { hid: input.hid },
        {
          plotOwnerId: input.plotOwnerId,
        },
      );
      await this.audit.add({
        tx: tx.id,
        source: tx.source,
        hid: input.hid,
        from: data.plotOwnerId,
        to: input.plotOwnerId,
      });
      return true;
    } catch (error) {
      throw toSystemError('Error storing Plot Owner Id against HID')(error);
    }
  }

  private async updateByPlotOwnerId(input: HIDToPlotOwner, tx: Transaction): Promise<boolean> {
    const data = await this.findByPlotOwnerId(input.plotOwnerId);
    if (!data) {
      return false;
    }

    try {
      await this.updateRow({ plotOwnerId: input.plotOwnerId }, { hid: input.hid });
      await this.audit.add({
        tx: tx.id,
        source: tx.source,
        hid: data.hid,
        from: data.plotOwnerId,
        to: null,
      });
      await this.audit.add({
        tx: tx.id,
        source: tx.source,
        hid: input.hid,
        from: null,
        to: input.plotOwnerId,
      });
      return true;
    } catch (error) {
      throw toSystemError('Error updating HID against Plot Owner Id')(error);
    }
  }

  private async deleteRow(where: object): Promise<number> {
    return Db.instance.hidToPlotOwner.where(where).delete();
  }

  private async updateRow(where: object, update: object): Promise<number> {
    return Db.instance.hidToPlotOwner.where(where).update(update);
  }
}
