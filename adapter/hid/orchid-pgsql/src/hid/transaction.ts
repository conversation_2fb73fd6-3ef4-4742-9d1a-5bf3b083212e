import { logger } from '@havenengineering/module-haven-logging';
import { Db } from '../../db/db.js';
import { DbTransactionProvider, DbTransactionProviderOptions, DomainError } from 'haven-hid-domain';

export const orchidTransaction = async <R>(callback: () => Promise<R>): Promise<R> =>
  await Db.instance.$transaction<typeof Db.instance, R>(async () => await callback());

export class OrchidTransactionProvider implements DbTransactionProvider {
  transaction: <T>(callback: () => Promise<T>, option: DbTransactionProviderOptions) => Promise<T>;

  constructor() {
    this.transaction = async (callback, { retry = false }) => {
      try {
        return await orchidTransaction(callback);
      } catch (e: unknown) {
        if (retry && (e as DomainError).canRetry) {
          logger.warn('Retrying transaction', {
            cause: e,
          });
          return orchidTransaction(callback);
        }
        throw e;
      }
    };
  }
}
