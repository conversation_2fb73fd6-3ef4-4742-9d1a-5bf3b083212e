import {
  HID,
  HIDToSeaWareClient,
  HIDToSeaWareClientStore,
  SeaWareClientId,
  SystemError,
  Transaction,
  isRTBFTransaction,
} from 'haven-hid-domain';
import { isDeepStrictEqual } from 'util';
import { Db } from '../../db/db.js';
import { HIDToSeawareClientAudit } from './hid-to-seaware-client-audit.js';
import { toSystemError } from './error.js';

export class PgHIDToSeaWareClientStore implements HIDToSeaWareClientStore {
  audit = new HIDToSeawareClientAudit();

  async add(input: HIDToSeaWareClient, tx: Transaction): Promise<void> {
    try {
      await this.write(input);
      await this.audit.add({
        tx: tx.id,
        source: tx.source,
        hid: input.hid,
        from: null,
        to: input.seaWareClientId,
      });
    } catch (error) {
      throw toSystemError('Error adding clientid to HID')(error);
    }
  }

  async delete(input: HIDToSeaWareClient, tx: Transaction): Promise<boolean> {
    try {
      const deleted =
        (await this.deleteRow({
          seawareClientId: input.seaWareClientId,
        })) > 0;
      if (deleted && !isRTBFTransaction(tx)) {
        await this.audit.add({
          tx: tx.id,
          source: tx.source,
          hid: input.hid,
          from: input.seaWareClientId,
          to: null,
        });
      }
      return deleted;
    } catch (error) {
      throw toSystemError('Error deleting clientid from HID')(error);
    }
  }

  async update(input: HIDToSeaWareClient, tx: Transaction): Promise<boolean> {
    return this.updateHIDOrSeaWareClient(input, tx);
  }

  async findBySeaWareClientId(input: SeaWareClientId): Promise<HIDToSeaWareClient | undefined> {
    try {
      const data = await this.find({ seawareClientId: input });
      return data;
    } catch (error) {
      throw toSystemError('Error fetching HID by client-id')(error);
    }
  }

  async findByHID(input: HID): Promise<HIDToSeaWareClient | undefined> {
    try {
      const data = await this.find({ hid: input });
      return data;
    } catch (error) {
      toSystemError('Error fetching Seaware Client Id by HID')(error);
    }
  }

  private async find(condition: object): Promise<HIDToSeaWareClient | undefined> {
    const result = await this.select().where(condition);
    return this.toResult(result as { hid: HID; seawareClientId: number }[]);
  }

  private select() {
    const table = Db.instance.hidToSeaWareClient;
    return table.select('hid', 'seawareClientId');
  }

  private toResult(result: { hid: string; seawareClientId: number }[]) {
    if (result.length === 0) {
      return undefined;
    } else {
      const values = Array.from(result.values());
      return {
        hid: values[0].hid,
        seaWareClientId: values[0].seawareClientId,
      };
    }
  }

  private async write(input: HIDToSeaWareClient): Promise<void> {
    const table = Db.instance.hidToSeaWareClient;
    await table.create({
      hid: input.hid,
      seawareClientId: input.seaWareClientId,
    });
  }

  private async updateHIDOrSeaWareClient(
    input: HIDToSeaWareClient,
    tx: Transaction,
  ): Promise<boolean> {
    const updatedByHid = await this.updateByHID(input, tx);
    if (updatedByHid) {
      return updatedByHid;
    }

    const updatedBySeaWareClientId = await this.updateBySeaWareClientId(input, tx);
    if (updatedBySeaWareClientId) {
      return updatedBySeaWareClientId;
    }

    throw new SystemError(`No record found to update for [${JSON.stringify(input)}]`);
  }

  private async updateByHID(input: HIDToSeaWareClient, tx: Transaction): Promise<boolean> {
    const data = await this.findByHID(input.hid);
    if (!data) {
      return false;
    }
    if (isDeepStrictEqual(data, input)) {
      throw new SystemError(`Update already exists [${JSON.stringify(input)}]`);
    }

    try {
      await this.updateRow(
        { hid: input.hid },
        {
          seawareClientId: input.seaWareClientId,
        },
      );
      await this.audit.add({
        tx: tx.id,
        source: tx.source,
        hid: input.hid,
        from: data.seaWareClientId,
        to: input.seaWareClientId,
      });
      return true;
    } catch (error) {
      throw toSystemError('Error updating clientid for HID')(error);
    }
  }

  private async updateBySeaWareClientId(
    input: HIDToSeaWareClient,
    tx: Transaction,
  ): Promise<boolean> {
    const data = await this.findBySeaWareClientId(input.seaWareClientId);
    if (!data) {
      return false;
    }

    try {
      await this.updateRow(
        {
          seawareClientId: input.seaWareClientId,
        },
        { hid: input.hid },
      );
      await this.audit.add({
        tx: tx.id,
        source: tx.source,
        hid: data.hid,
        from: input.seaWareClientId,
        to: null,
      });
      await this.audit.add({
        tx: tx.id,
        source: tx.source,
        hid: input.hid,
        from: null,
        to: input.seaWareClientId,
      });
      return true;
    } catch (error) {
      throw toSystemError('Error updating HID for clientid')(error);
    }
  }

  private async deleteRow(where: object): Promise<number> {
    return Db.instance.hidToSeaWareClient.where(where).delete();
  }

  private async updateRow(where: object, update: object): Promise<number> {
    return Db.instance.hidToSeaWareClient.where(where).update(update);
  }
}
