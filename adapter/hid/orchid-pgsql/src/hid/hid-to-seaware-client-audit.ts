import { SeaWareClientId } from 'haven-hid-domain';
import { DbTable } from 'orchid-orm';
import { Db } from '../../db/db.js';
import { HidAuditTable } from '../tables/hid-audit.table.js';
import { AuditStore } from './audit.store.js';

export class HIDToSeawareClientAudit extends AuditStore<SeaWareClientId> {
  getTable = (): DbTable<typeof HidAuditTable> => Db.instance.hidAudit;
  getType = () => 'seaware_client';
  fromValue = (value: string): SeaWareClientId => Number(value);
  toValue = (value: SeaWareClientId): string => value.toString();
}
