import { logger } from '@havenengineering/module-haven-logging';
import { SecurityAudit, SecurityAuditStore } from 'haven-hid-domain';
import { Db, HidTables } from '../../db/db.js';
import { DbTable } from 'orchid-orm';
import { toSystemError } from './error.js';

export class PgSecurityAuditStore implements SecurityAuditStore {
  constructor() {}

  private getTable(): DbTable<HidTables['securityAudit']> {
    return Db.instance.securityAudit;
  }

  async add(record: SecurityAudit): Promise<void> {
    try {
      const message = `source: ${record.source} | reason: ${record.reason}`;
      const context = {
        client: record.clientId,
        source: record.source,
        ip: record.ipAddress,
        ua: record.userAgent,
      };
      logger.warn(message, context);
      await this.getTable().create({
        ...record,
        userAgent: record.userAgent.substring(0, 255),
      });
    } catch (e: unknown) {
      toSystemError('Could not save security audit')(e);
    }
  }
}
