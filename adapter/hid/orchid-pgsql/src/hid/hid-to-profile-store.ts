import {
  Audit,
  HID,
  HIDToProfile,
  HIDToProfileAudit,
  HIDToProfileStore,
  HIDToProfileUpdate,
  isRTBFTransaction,
  Profile,
  SystemError,
  Transaction,
} from 'haven-hid-domain';
import { Db } from '../../db/db.js';
import { toSystemError } from './error.js';
import { HIDToProfileAuditStore } from './hid-to-profile-audit.js';
import { getChangedProperties, isChanged } from './hid-to-profile-differences.js';

export class PgHIDToProfileStore implements HIDToProfileStore {
  audit = new HIDToProfileAuditStore();

  async add(input: HIDToProfile, tx: Transaction): Promise<void> {
    try {
      await this.write(input);
      await this.audit.add({
        tx: tx.id,
        source: tx.source,
        hid: input.hid,
        from: null,
        to: input,
      });
      return;
    } catch (error) {
      throw toSystemError('Error adding profile to HID')(error);
    }
  }

  async delete(input: HIDToProfile, tx: Transaction): Promise<boolean> {
    try {
      const deleteResult = await this.deleteRow({
        hid: input.hid,
      });
      const deleted = deleteResult > 0;

      if (deleted && !isRTBFTransaction(tx)) {
        await this.audit.add({
          tx: tx.id,
          source: tx.source,
          hid: input.hid,
          from: input,
          to: null,
        });
      }
      return deleted;
    } catch (error) {
      throw toSystemError('Error deleting profile')(error);
    }
  }

  async update(input: HIDToProfileUpdate, tx: Transaction): Promise<boolean> {
    try {
      const record = await this.findByHID(input.hid);

      if (!record) {
        throw new SystemError(`No record found to update for [{hid: ${input.hid}]`);
      }

      const properties = getChangedProperties(record, input as Profile);
      if (!isChanged(properties)) return false;

      await this.updateRow({ hid: input.hid }, input);
      await this.audit.add({
        tx: tx.id,
        source: tx.source,
        hid: input.hid,
        from: properties,
        to: input,
      });
      return true;
    } catch (error) {
      throw toSystemError('Error storing profile against HID')(error);
    }
  }

  async findByHID(input: HID): Promise<HIDToProfile | undefined> {
    return this.find({ hid: input });
  }

  async auditByTransaction(tx: Transaction): Promise<Audit<HIDToProfileAudit>[]> {
    try {
      return this.audit.findAllByTransaction(tx);
    } catch (error) {
      throw toSystemError('Error fetching audit')(error);
    }
  }

  private async find(condition: { hid: HID }): Promise<HIDToProfile | undefined> {
    const result = await this.select().where(condition);
    return this.toResult(result);
  }

  private select() {
    return Db.instance.hidToProfile.select(
      'hid',
      'name',
      'title',
      'firstName',
      'lastName',
      'phoneNumber',
    );
  }

  private toResult(result: { [key: string]: unknown }[]): HIDToProfile | undefined {
    if (result.length === 0) {
      return undefined;
    }

    const values = Array.from(result.values()) as unknown as HIDToProfile[];
    return values[0];
  }

  private async write(input: HIDToProfile): Promise<void> {
    await Db.instance.hidToProfile.create({
      hid: input.hid,
      name: input.name,
      title: input.title,
      firstName: input.firstName,
      lastName: input.lastName,
      phoneNumber: input.phoneNumber,
    });
  }

  private async deleteRow(where: { hid: HID }): Promise<number> {
    return Db.instance.hidToProfile.where(where).delete();
  }

  private async updateRow(where: { hid: HID }, data: Partial<Profile>): Promise<number> {
    return Db.instance.hidToProfile.where(where).update(data);
  }
}
