import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  HIDToAddressStore,
  isRTBFTransaction,
  SystemError,
  Transaction,
} from 'haven-hid-domain';
import { Db } from '../../db/db.js';
import { HIDToAddressAuditStore } from './hid-to-address-audit.js';

const DELETE_ALL_SAFETY_KEY = 'I hate all addresses'; // see deleteAll(), below

type Row = Awaited<ReturnType<typeof Db.instance.hidToAddress.take>>;
type Table = Awaited<ReturnType<typeof Db.instance.hidToAddress.select>>;

const rowToHidAddress = (row: Row): HIDToAddress => {
  return {
    id: row.id as number,
    hid: row.hid as string,
    address_line1: row.address_line1 as string,
    address_line2: row.address_line2 as string,
    address_city: row.address_city as string,
    address_county: row.address_county as string,
    address_postcode: row.address_postcode as string,
    address_country: row.address_country as string,
    address_effective_from: row.address_effective_from as Date,
  };
};

export class PgHIDToAddressStore implements HIDToAddressStore {
  audit = new HIDToAddressAuditStore();

  async add(address: HIDToAddress, tx: Transaction): Promise<void> {
    const previousAddress = await this.findCurrentAddressForHID(address.hid);

    await Db.instance.hidToAddress.create({
      hid: address.hid,
      address_line1: address.address_line1,
      address_line2: address.address_line2,
      address_city: address.address_city,
      address_county: address.address_county,
      address_postcode: address.address_postcode,
      address_country: address.address_country,
      address_effective_from: address.address_effective_from || new Date(),
    });

    await this.audit.add({
      tx: tx.id,
      source: tx.source,
      hid: address.hid,
      from: previousAddress,
      to: address,
    });
  }

  async deleteAddress(address: HIDToAddress, tx: Transaction): Promise<boolean> {
    if (!address.id) {
      throw new SystemError(`Attempt to delete address with undefined id`);
    }

    try {
      const deleteResult = (await Db.instance.hidToAddress
        .findBy({ id: address.id })
        .get('id')
        .delete()) as number;

      const deleted = deleteResult > 0;

      if (deleted && !isRTBFTransaction(tx)) {
        await this.audit.add({
          tx: tx.id,
          source: tx.source,
          hid: address.hid,
          from: address,
          to: null,
        });
      }

      return deleted;
    } catch (error) {
      throw new SystemError(`Error deleting address with id ${address.id}`);
    }
  }

  async deleteAllAddressesForHID(hid: HID, tx: Transaction): Promise<boolean> {
    if (!hid) {
      throw new SystemError(`Attempt to delete addresses for undefined hid`);
    }
    try {
      const addresses = await this.findAllAddressesForHID(hid);

      await Db.instance.hidToAddress.findBy({ hid }).get('id').delete();

      if (!isRTBFTransaction(tx)) {
        for (const address of addresses) {
          await this.audit.add({
            tx: tx.id,
            source: tx.source,
            hid: hid,
            from: address,
            to: null,
          });
        }
      }

      return true;
    } catch (error) {
      throw new SystemError(`Error deleting addresses for hid ${hid}`);
    }
  }

  // the safety key is to prevent accidental calling
  // deleteAll should never really be used in production, but autocomplete makes it
  // dangerously easy to select deleteAll instead of deleteAllAddressesForHID or even deleteAddress.
  // So you are forced to _choose_ to call deleteAll by requiring a non-obvious string parameter
  async deleteAll(safetyKey: string): Promise<boolean> {
    if (safetyKey != DELETE_ALL_SAFETY_KEY) {
      throw new SystemError(
        `Attempt to delete all addresses with incorrect safety key: ${safetyKey}`,
      );
    }
    try {
      await Db.instance.hidToAddress.all().delete();

      return true;
    } catch (error) {
      throw new SystemError(`Error deleting all addresses`);
    }
  }

  async findCurrentAddressForHID(hid: HID): Promise<HIDToAddress | null> {
    if (!hid) {
      throw new SystemError(`Attempt to find current address for undefined hid`);
    }
    try {
      return await this.findLatest({ hid: hid });
    } catch (error) {
      throw new SystemError(`Error finding current address for HID ${hid}`);
    }
  }

  async findAllAddressesForHID(hid: HID): Promise<HIDToAddress[]> {
    if (!hid) {
      throw new SystemError(`Attempt to find all addresses for undefined hid`);
    }
    try {
      return await this.findAll({ hid });
    } catch (error) {
      throw new SystemError(`Error finding all addresses for HID ${hid}`);
    }
  }

  private async findLatest(condition: object): Promise<HIDToAddress | null> {
    try {
      const row: Row = await Db.instance.hidToAddress
        .where(condition)
        .order({ address_effective_from: 'DESC' })
        .selectAll()
        .take();

      return rowToHidAddress(row);
    } catch (e) {
      if (e?.constructor.name === 'NotFoundError') {
        return null;
      }

      throw e;
    }
  }

  private async findAll(condition: object): Promise<HIDToAddress[]> {
    const rows: Table = await Db.instance.hidToAddress
      .where(condition)
      .order({ address_effective_from: 'DESC' })
      .selectAll();

    const addresses: HIDToAddress[] = rows.map((row): HIDToAddress => rowToHidAddress(row));
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    return addresses;
  }
}
