import { HIDToProfile, Profile } from 'haven-hid-domain';

export const getChangedProperties = (
  existing: HIDToProfile,
  update: Partial<Profile>,
): Partial<Profile> => {
  const properties = Object.entries(update) as [keyof Profile, unknown][];
  return properties.reduce(
    (initialProfileData: Partial<Profile>, [key, value]) =>
      value === existing[key]
        ? initialProfileData
        : {
            ...initialProfileData,
            [key]: existing[key],
          },
    {},
  );
};

export const isChanged = (from: Partial<Profile>) => Object.keys(from).length !== 0;
