import { Audit, AuditData, Transaction } from 'haven-hid-domain';
import { DbTable } from 'orchid-orm';
import { HidAuditTable } from '../tables/hid-audit.table.js';

export abstract class AuditStore<U> {
  async findAllByTransaction(tx: Transaction): Promise<Audit<U>[]> {
    const results = await this.getTable().where({ tx: tx.id, type: this.getType() }).order({
      hid_audit_id: 'ASC',
      type: 'ASC',
    });

    return results.map((row) => {
      const from = typeof row.from === 'string' ? this.fromValue(row.from) : null;
      const to = typeof row.to === 'string' ? this.fromValue(row.to) : null;
      return { ...row, from, to } as Audit<U>;
    });
  }

  async add(record: AuditData<U>): Promise<void> {
    await this.getTable().create({
      ...record,
      type: this.getType(),
      from: record.from != undefined ? this.toValue(record.from) : null,
      to: record.to != undefined ? this.toValue(record.to) : null,
    });
  }

  abstract getTable(): DbTable<typeof HidAuditTable>;
  abstract fromValue(value: string): U;
  abstract toValue(value: U): string;
  abstract getType(): string;
}
