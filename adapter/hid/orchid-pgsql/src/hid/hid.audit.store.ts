import { DbTable } from 'orchid-orm';
import { Db } from '../../db/db.js';
import { HidAuditTable } from '../tables/hid-audit.table.js';
import { HidAuditStore, ObfuscateAuditParams } from 'haven-hid-domain';
import { toSystemError } from './error.js';

export class PgHidAuditStore implements HidAuditStore {
  constructor() {}

  private getTable = (): DbTable<typeof HidAuditTable> => Db.instance.hidAudit;

  async obfuscate({ email, hid }: ObfuscateAuditParams): Promise<void> {
    try {
      await this.getTable()
        .where((q) =>
          hid
            ? q
                .where({
                  hid,
                  type: {
                    in: ['email', 'profile', 'email_verified'],
                  },
                })
                .orWhere({
                  from: { contains: email },
                })
                .orWhere({
                  to: { contains: email },
                })
            : q
                .where({
                  from: { contains: email },
                })
                .orWhere({
                  to: { contains: email },
                }),
        )
        .update({
          from: 'FORGOTTEN',
          to: 'FORGOTTEN',
        });
    } catch (e: unknown) {
      throw toSystemError('Could not obfuscate hid audit')(e);
    }
  }
}
