import { AuditStore } from './audit.store.js';
import { PlotOwnerId } from 'haven-hid-domain';
import { Db } from '../../db/db.js';
import { DbTable } from 'orchid-orm';
import { HidAuditTable } from '../tables/hid-audit.table.js';

export class HidToPlotOwnerAudit extends AuditStore<PlotOwnerId> {
  getTable = (): DbTable<typeof HidAuditTable> => Db.instance.hidAudit;
  getType = () => 'plot_owner';
  fromValue = (value: string): PlotOwnerId => Number(value);
  toValue = (value: PlotOwnerId): string => value.toString();
}
