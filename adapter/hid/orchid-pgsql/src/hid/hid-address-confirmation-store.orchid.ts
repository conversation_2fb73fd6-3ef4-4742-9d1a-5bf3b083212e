import { HID, HidAddressRequiresConfirmationStore } from 'haven-hid-domain';
import { Db } from '../../db/db.js';

export class OrchidHidAddressRequiresConfirmationStore
  implements HidAddressRequiresConfirmationStore
{
  async add(hid: HID): Promise<boolean> {
    await Db.instance.hidAddressRequiresConfirmation.create({ hid: hid });
    return true;
  }

  async remove(hid: HID): Promise<boolean> {
    await Db.instance.hidAddressRequiresConfirmation.where({ hid: hid }).delete();
    return true;
  }

  async needsConfirmation(hid: HID): Promise<boolean> {
    const present = await Db.instance.hidAddressRequiresConfirmation
      .select('hid')
      .where({ hid: hid });
    return present && present.length > 0;
  }
}
