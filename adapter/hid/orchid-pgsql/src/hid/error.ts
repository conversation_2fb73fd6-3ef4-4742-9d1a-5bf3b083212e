import { SystemError, DbConflictError } from 'haven-hid-domain';
import { logger } from '@havenengineering/module-haven-logging';

const DUPLICATE_KEY_VIOLATES_UNIQUE_CONSTRAINT_CODE = '23505';

interface DbError extends Error {
  code: string;
  query: unknown;
  severity: 'string';
}
export const toSystemError =
  (message: string) =>
  (e: unknown): SystemError | DbConflictError => {
    // Handle conversion to system error in one place
    if (e instanceof SystemError) return e;

    const dbError = e as DbError;
    if (dbError.code === DUPLICATE_KEY_VIOLATES_UNIQUE_CONSTRAINT_CODE) {
      logger.error(message, {
        cause: dbError.message,
        canRetry: true,
      });
      return new DbConflictError(message, {
        cause: 'duplicate key value violates unique constraint',
      });
    }

    if (dbError.query) {
      logger.error(`${message}: ${dbError.message}`, {
        cause: { message: dbError.message },
      });
      return new SystemError(message, {
        cause: dbError.message,
      });
    }

    logger.error(message, e);
    return new SystemError(message, { cause: e });
  };
