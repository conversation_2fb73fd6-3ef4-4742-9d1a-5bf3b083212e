import { AuditStore } from './audit.store.js';
import { HIDToProfileAudit } from 'haven-hid-domain';
import { Db } from '../../db/db.js';
import { DbTable } from 'orchid-orm';
import { HidAuditTable } from '../tables/hid-audit.table.js';

export class HIDToProfileAuditStore extends AuditStore<HIDToProfileAudit> {
  getTable = (): DbTable<typeof HidAuditTable> => Db.instance.hidAudit;
  getType = () => 'profile';
  fromValue = (value: string): HIDToProfileAudit => JSON.parse(value) as HIDToProfileAudit;
  toValue = ({ name, title, firstName, lastName, phoneNumber }: HIDToProfileAudit): string =>
    JSON.stringify({
      name,
      title,
      firstName,
      lastName,
      phoneNumber,
    });
}
