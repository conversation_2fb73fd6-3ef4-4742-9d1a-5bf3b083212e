import {
  <PERSON>t,
  <PERSON><PERSON>,
  <PERSON>Error,
  Transaction,
  HIDToPassword,
  HIDToPasswordAudit,
  HIDToPasswordStore,
  isRTBFTransaction,
} from 'haven-hid-domain';
import { Db } from '../../db/db.js';
import { toSystemError } from './error.js';
import { HIDToPasswordAuditStore } from './hid-to-password-audit.js';
import { PasswordStrength } from '@havenengineering/module-shared-password-strength';

type PasswordUpdate = {
  hash: string;
  strength: PasswordStrength;
};

export class PgHIDToPasswordStore implements HIDToPasswordStore {
  audit = new HIDToPasswordAuditStore();

  async add(input: HIDToPassword, tx: Transaction): Promise<void> {
    try {
      await this.write(input);
      await this.audit.add({
        tx: tx.id,
        source: tx.source,
        hid: input.hid,
        from: null,
        to: input,
      });
    } catch (error) {
      throw toSystemError('Error adding password to HID')(error);
    }
  }

  async delete(input: HIDToPassword, tx: Transaction): Promise<boolean> {
    try {
      const deleted =
        (await this.deleteRow({
          hid: input.hid,
        })) > 0;
      if (deleted && !isRTBFTransaction(tx)) {
        await this.audit.add({
          tx: tx.id,
          source: tx.source,
          hid: input.hid,
          from: input,
          to: null,
        });
      }
      return deleted;
    } catch (error) {
      throw toSystemError('Error deleting password')(error);
    }
  }

  async update({ hid, hash, strength }: HIDToPassword, tx: Transaction): Promise<boolean> {
    try {
      const record = await this.findByHID(hid);

      if (!record) {
        throw new SystemError(`No record found to update for [{hid: ${hid}]`);
      }

      if (record.hid === hid && record.hash === hash) return false;

      await this.updateRow({ hid }, { hash, strength });
      await this.audit.add({
        tx: tx.id,
        source: tx.source,
        hid: hid,
        from: { strength: record.strength },
        to: { strength },
      });
      return true;
    } catch (error) {
      throw toSystemError('Error storing password against HID')(error);
    }
  }

  async findByHID(hid: HID): Promise<HIDToPassword | null> {
    return this.find({ hid });
  }

  async auditByTransaction(tx: Transaction): Promise<Audit<HIDToPasswordAudit>[]> {
    try {
      return this.audit.findAllByTransaction(tx);
    } catch (error) {
      throw toSystemError('Error fetching audit')(error);
    }
  }

  private async find(condition: { hid: HID }): Promise<HIDToPassword | null> {
    const result = await this.select().where(condition);
    return this.toResult(result);
  }

  private toResult(result: { [key: string]: unknown }[]): HIDToPassword | null {
    if (result.length === 0) return null;
    const values = Array.from(result.values());
    const hidToPassword: HIDToPassword = {
      hid: values[0].hid as HID,
      hash: values[0].hash as string,
      strength: values[0].strength as PasswordStrength,
    };
    return hidToPassword;
  }

  private select() {
    return Db.instance.hidToPassword.select('hid', 'hash', 'strength');
  }

  private async write(input: HIDToPassword): Promise<void> {
    await Db.instance.hidToPassword.create({
      hid: input.hid,
      hash: input.hash,
      strength: input.strength,
    });
  }

  private async deleteRow(where: { hid: HID }): Promise<number> {
    return Db.instance.hidToPassword.where(where).delete();
  }

  private async updateRow(where: { hid: HID }, data: PasswordUpdate): Promise<number> {
    return Db.instance.hidToPassword.where(where).update(data);
  }
}
