import {
  HavenIdentityQuery,
  HID,
  OptionalHavenIdentity,
  ValidEmail,
  HavenServiceAccountIdType as IdType,
  HavenServiceAccount,
  HIDAddress,
} from 'haven-hid-domain';
import { Db } from '../../db/db.js';
import { IdentitySearchQuery, IdentitySearchResult } from 'haven-hid-domain';
import { PasswordStrength } from '@havenengineering/module-shared-password-strength';

type FindCriteria = { 'hid_to_email.hid': HID } | { 'hid_to_email.email': string };

export class PgIdentityQuery implements HavenIdentityQuery {
  constructor() {}

  findByEmail = async (search: ValidEmail): Promise<OptionalHavenIdentity> => {
    return this.find({
      'hid_to_email.email': search.email,
    });
  };

  findById = async (hid: HID): Promise<OptionalHavenIdentity> => {
    return this.find({ 'hid_to_email.hid': hid });
  };

  async search(params: IdentitySearchQuery, limit: number = 10): Promise<IdentitySearchResult[]> {
    const tables = Db.instance;

    const query = tables.hid
      .join(tables.hidToEmail, 'hid.hid', 'hid_to_email.hid')
      .leftJoin(tables.hidToProfile, 'hid.hid', 'hid_to_profile.hid')
      .leftJoin(this.buildAddressSubQuery(), 'hid.hid', 'latest_address.hid')
      .leftJoin(tables.hidToPassword, 'hid.hid', 'hid_to_password.hid')
      .leftJoin(tables.hidToSeaWareClient, 'hid.hid', 'hid_to_seaware_client.hid')
      .leftJoin(tables.hidToPlotOwner, 'hid.hid', 'hid_to_plot_owner.hid')
      .where({ email: params.email })
      .select(
        'hid.hid',
        'hid_to_email.email',
        'hid_to_email.emailVerified',
        'hid_to_email.updated_at',
        'hid_to_profile.name',
        'hid_to_profile.title',
        'hid_to_profile.firstName',
        'hid_to_profile.lastName',
        'hid_to_profile.phoneNumber',
        'latest_address.address_line1',
        'latest_address.address_line2',
        'latest_address.address_city',
        'latest_address.address_county',
        'latest_address.address_postcode',
        'latest_address.address_country',
        'hid_to_password.strength',
        'hid_to_plot_owner.plot_owner_id',
        'hid_to_seaware_client.seaware_client_id',
      )
      .limit(limit);

    const result = await query;
    return this.toSearchResult(result);
  }

  private async find(condition: FindCriteria): Promise<OptionalHavenIdentity> {
    const tables = Db.instance;

    const query = tables.hid
      .join(tables.hidToEmail, 'hid.hid', 'hid_to_email.hid')
      .join(tables.hidToProfile, 'hid.hid', 'hid_to_profile.hid')
      .leftJoin(this.buildAddressSubQuery(), 'hid.hid', 'latest_address.hid')
      .leftJoin(tables.hidToPassword, 'hid.hid', 'hid_to_password.hid')
      .leftJoin(tables.hidToSeaWareClient, 'hid.hid', 'hid_to_seaware_client.hid')
      .leftJoin(tables.hidToPlotOwner, 'hid.hid', 'hid_to_plot_owner.hid')
      .where(condition)
      .select(
        'hid.hid',
        'hid_to_email.email',
        'hid_to_email.emailVerified',
        'hid_to_email.updated_at',
        'hid_to_profile.name',
        'hid_to_profile.title',
        'hid_to_profile.firstName',
        'hid_to_profile.lastName',
        'hid_to_profile.phoneNumber',
        'latest_address.address_line1',
        'latest_address.address_line2',
        'latest_address.address_city',
        'latest_address.address_county',
        'latest_address.address_postcode',
        'latest_address.address_country',
        'hid_to_password.hash',
        'hid_to_password.strength',
        'hid_to_plot_owner.plot_owner_id',
        'hid_to_seaware_client.seaware_client_id',
      );

    const result = await query;
    return this.toResult(result);
  }

  private toResult(data: { [key: string]: unknown }[]): OptionalHavenIdentity {
    if (data.length > 0) {
      const accounts: HavenServiceAccount[] = [];
      if (data[0].seaware_client_id)
        accounts.push({
          id: data[0].seaware_client_id as number,
          type: IdType.SEAWARE_CLIENT_ID,
        });
      if (data[0].plot_owner_id)
        accounts.push({
          id: data[0].plot_owner_id as number,
          type: IdType.OWNER_ID,
        });

      const { hid, email, title, name, firstName, lastName, phoneNumber, emailVerified } = data[0];

      return {
        hid: hid as HID,
        email: ValidEmail.newFromSafeInput(email as string),
        name: name as string,
        emailVerified: emailVerified as boolean,
        title: title as string,
        firstName: firstName as string,
        lastName: lastName as string,
        phoneNumber: phoneNumber as string,
        ...(this.addressExists(data[0])
          ? {
              address: this.buildAddressObject(data[0]),
            }
          : {}),
        ...(data[0].hash
          ? {
              passwordHash: data[0].hash as string,
            }
          : {}),
        ...(data[0].strength
          ? {
              passwordStrength: data[0].strength as PasswordStrength,
            }
          : {}),
        accounts,
      };
    }

    return undefined;
  }

  private toSearchResult(data: { [key: string]: unknown }[]): IdentitySearchResult[] {
    return data.map((result) => {
      const {
        hid,
        email,
        emailVerified,
        seaware_client_id,
        plot_owner_id,
        title,
        firstName,
        lastName,
        phoneNumber,
        strength,
      } = result;
      const accounts: HavenServiceAccount[] = [];
      if (seaware_client_id) {
        accounts.push({
          id: seaware_client_id as number,
          type: IdType.SEAWARE_CLIENT_ID,
        });
      }
      if (plot_owner_id) {
        accounts.push({
          id: plot_owner_id as number,
          type: IdType.OWNER_ID,
        });
      }

      return {
        hid: hid,
        email: ValidEmail.newFromSafeInput(email as string),
        title,
        firstName,
        lastName,
        emailVerified: emailVerified as boolean,
        phoneNumber,
        ...(this.addressExists(result)
          ? {
              address: this.buildAddressObject(result),
            }
          : {}),
        accounts,
        migrated: !!strength,
      } as IdentitySearchResult;
    });
  }

  private addressExists(data: { [key: string]: unknown }): boolean {
    return data.address_line1 !== null;
  }

  private buildAddressObject(data: { [key: string]: unknown }): HIDAddress {
    return {
      address_line1: data.address_line1 as string,
      address_line2: data.address_line2 as string,
      address_city: data.address_city as string,
      address_county: data.address_county as string,
      address_postcode: data.address_postcode as string,
      address_country: data.address_country as string,
    };
  }

  private buildAddressSubQuery() {
    return Db.instance.hidToAddress
      .select('*')
      .order({ address_effective_from: 'DESC' })
      .limit(1)
      .as('latest_address');
  }
}
