import { ValidEmail } from 'haven-hid-domain';
import { DbTable } from 'orchid-orm';
import { Db } from '../../db/db.js';
import { HidAuditTable } from '../tables/hid-audit.table.js';
import { AuditStore } from './audit.store.js';

export class HidToEmailAuditStore extends AuditStore<ValidEmail> {
  getTable = (): DbTable<typeof HidAuditTable> => Db.instance.hidAudit;
  getType = () => 'email';
  fromValue = (value: string): ValidEmail => ValidEmail.newFromSafeInput(value);
  toValue = (value: ValidEmail): string => value.email;
}
