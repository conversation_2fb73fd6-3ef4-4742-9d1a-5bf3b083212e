import {
  EmailVerificationToken,
  EmailVerificationTokenType,
  HID,
  HIDEmailVerificationLookup,
  HIDEmailVerificationRequest,
  HIDEmailVerificationRequestStore,
} from 'haven-hid-domain';
import { toSystemError } from './error.js';
import { Db } from '../../db/db.js';

export class PgHIDEmailVerificationRequestStore implements HIDEmailVerificationRequestStore {
  async add(input: HIDEmailVerificationRequest): Promise<void> {
    try {
      await this.write(input);
    } catch (error) {
      throw toSystemError('Error creating email verification request')(error);
    }
  }

  async deleteAll(hid: HID, type: EmailVerificationTokenType): Promise<number> {
    return await Db.instance.hidToEmailVerificationRequest.where({ hid, type }).delete();
  }

  async find(token: EmailVerificationToken) {
    if (!token) return null;

    const result = await Db.instance.hidToEmailVerificationRequest
      .select('hid', 'token', 'type', 'created_at', 'return_url')
      .where({ token });
    return this.toResult(result);
  }

  private toResult(results: { [key: string]: unknown }[]): HIDEmailVerificationLookup | null {
    if (results.length === 0) return null;
    const values = Array.from(results.values());
    return {
      token: values[0].token as string,
      hid: values[0].hid as HID,
      returnUrl: values[0].return_url as string,
      type: values[0].type as EmailVerificationTokenType,
      createdAt: new Date(values[0].created_at as number),
    };
  }

  private async write(input: HIDEmailVerificationRequest) {
    await Db.instance.hidToEmailVerificationRequest.create({
      token: input.token,
      hid: input.hid,
      returnUrl: input.returnUrl,
      type: input.type,
    });
  }
}
