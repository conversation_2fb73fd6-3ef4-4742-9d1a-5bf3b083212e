import { PgHIDStore } from './hid-store.js';
import { PgHIDToEmailStore } from './hid-to-email-store.js';
import { PgHIDToPlotOwnerStore } from './hid-to-plot-owner-store.js';
import { PgHIDToSeaWareClientStore } from './hid-to-seaware-client-store.js';
import { PgHIDToPasswordStore } from './hid-to-password-store.js';
import { PgHIDToProfileStore } from './hid-to-profile-store.js';
import { PgIdentityQuery } from './identity-query.js';
import { OrchidTransactionProvider } from './transaction.js';
import { PgAuthenticationAuditStore } from './authenticate.audit.store.js';
import { PgHIDEmailVerificationRequestStore } from './hid-email-verification-request-store.js';
import { PgHidAuditStore } from './hid.audit.store.js';
import { OrchidHidAddressRequiresConfirmationStore } from './hid-address-confirmation-store.orchid.js';
import {
  AuthenticationAuditStore,
  HavenIdentityTransactionalStore,
  HIDForEmail,
  HIDForEmailPlotAndSeaWare,
  HIDForEmailPlotAndSeaWareTransactional,
  HIDToEmailStore,
  HIDToPlot,
  HIDToSeaWare,
  RightToBeForgotten,
  HIDEmailVerificationRequestStore,
  HIDStore,
} from 'haven-hid-domain';
import { PgSecurityAuditStore } from './security.audit.store.js';
import { PgHIDToAddressStore } from './hid-to-address-store.js';

export const PgHidStore = (): HIDStore => {
  return new PgHIDStore();
};

export const PgHIDForEmailPlotAndSeaWare = (): HIDForEmailPlotAndSeaWare => {
  const dbTransactionProvider = new OrchidTransactionProvider();
  const hidStore = new PgHIDStore();
  const emailStore = new PgHIDToEmailStore();
  const plotStore = new PgHIDToPlotOwnerStore();
  const seawareStore = new PgHIDToSeaWareClientStore();

  const hidToEmail = new HIDForEmail(hidStore, emailStore);
  const hidToPlot = new HIDToPlot(hidStore, emailStore, plotStore);
  const hidToSeaware = new HIDToSeaWare(seawareStore);
  const identityQuery = new PgIdentityQuery();

  return new HIDForEmailPlotAndSeaWareTransactional(
    dbTransactionProvider,
    hidStore,
    hidStore,
    hidToEmail,
    hidToPlot,
    hidToSeaware,
    identityQuery,
  );
};

export const PgHavenIdentityTransactionalStore = (): HavenIdentityTransactionalStore => {
  const dbTransactionProvider = new OrchidTransactionProvider();
  const hidStore = new PgHIDStore();
  const emailStore = new PgHIDToEmailStore();
  const passwordStore = new PgHIDToPasswordStore();
  const profileStore = new PgHIDToProfileStore();
  const addressStore = new PgHIDToAddressStore();
  const identityQuery = new PgIdentityQuery();
  const hidForEmail = PgHIDForEmailPlotAndSeaWare();
  const hidAddressRequiresAuthenticationStore = new OrchidHidAddressRequiresConfirmationStore();

  return new HavenIdentityTransactionalStore(
    hidStore,
    hidForEmail,
    emailStore,
    passwordStore,
    profileStore,
    addressStore,
    identityQuery,
    dbTransactionProvider,
    hidAddressRequiresAuthenticationStore,
  );
};

export const PgHavenAuthenticationAuditStore = (): AuthenticationAuditStore => {
  return new PgAuthenticationAuditStore();
};

export const PgRightToBeForgotten = (): RightToBeForgotten => {
  const dbTransactionProvider = new OrchidTransactionProvider();
  const emailStore = new PgHIDToEmailStore();
  const plotStore = new PgHIDToPlotOwnerStore();
  const seawareStore = new PgHIDToSeaWareClientStore();
  const profileStore = new PgHIDToProfileStore();
  const passwordStore = new PgHIDToPasswordStore();
  const hidStore = new PgHIDStore();
  const authenticationAuditStore = new PgAuthenticationAuditStore();
  const hidAuditStore = new PgHidAuditStore();

  return new RightToBeForgotten(
    dbTransactionProvider,
    emailStore,
    plotStore,
    seawareStore,
    profileStore,
    passwordStore,
    hidStore,
    authenticationAuditStore,
    hidAuditStore,
  );
};

export const PgEmailStore = (): HIDToEmailStore => {
  return new PgHIDToEmailStore();
};

export const PgEmailVerificationRequestStore = (): HIDEmailVerificationRequestStore => {
  return new PgHIDEmailVerificationRequestStore();
};

export const PgHavenSecurityAuditStore = (): PgSecurityAuditStore => {
  return new PgSecurityAuditStore();
};
