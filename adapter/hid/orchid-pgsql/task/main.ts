import { logger } from '@havenengineering/module-haven-logging';
import { start, stop } from 'haven-identity-database';
import humanizeDuration from 'humanize-duration';
import { ResultAsync, okAsync } from 'neverthrow';
import { runMigrations, runSeedForE2e } from '../db/db-migrate.js';
import { Config, Mode, getConfig } from './config.js';
import { generate } from './generator.js';
import { load } from './load.js';

export type NoResult = void;

export const toError = (e: unknown): Error => {
  logger.error(e as Error);
  return new Error('Unexpected Error', {
    cause: e,
  });
};

const handleError = (error: Error) => {
  logger.error(error.message);
  process.exit(1);
};

const generateTask = (config: Config): ResultAsync<unknown, Error> => {
  return generate(config.generator);
};

const loadTask = (config: Config): ResultAsync<unknown, Error> => {
  return ResultAsync.fromPromise(load(config.load), toError);
};

const noopTask = (): ResultAsync<undefined, Error> => {
  return okAsync(undefined);
};

const stopTask = (): ResultAsync<void, Error> => {
  return stop();
};

const startTask = (config: Config): ResultAsync<unknown, Error> => {
  return start([config.database]);
};

const migrateTask = (config: Config): ResultAsync<void, Error> => {
  return startTask(config).andThen(() => {
    return runMigrations();
  });
};

const e2eSeedTask = (config: Config): ResultAsync<void, Error> => {
  return startTask(config).andThen(() => {
    return runSeedForE2e();
  });
};

const runTask = (config: Config): ResultAsync<unknown, Error> => {
  switch (config.mode) {
    case Mode.GENERATE:
      return generateTask(config);
    case Mode.LOAD:
      return loadTask(config);
    case Mode.STOP:
      return stopTask();
    case Mode.START:
      return startTask(config);
    case Mode.MIGRATE:
      return migrateTask(config);
    case Mode.E2E_SEED:
      return e2eSeedTask(config);
    case Mode.RESTART:
      return noopTask();
  }
};

export const main = async (): Promise<void> => {
  const start = new Date().getTime();
  logger.info('HID ADAPTER | Load configuration');
  const config = getConfig();

  await runTask(config)
    .map(() => {
      const end = new Date().getTime();
      const duration = end - start;
      logger.info(`HID ADAPTER | ${config.mode} Complete in ${humanizeDuration(duration)}`);
      process.exit(0);
    })
    .mapErr(handleError)
    .unwrapOr(undefined);
};

await main();
