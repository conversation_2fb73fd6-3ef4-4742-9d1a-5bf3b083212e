import { logger } from '@havenengineering/module-haven-logging';
import envvar from 'env-var';
import fs from 'node:fs';
import { pipeline } from 'node:stream/promises';
import pg, { PoolClient } from 'pg';
import { from as copyFrom } from 'pg-copy-streams';
import { TABLES, Table, getTableByName } from './data.js';

export class LoadConfig {
  public readonly databaseUrl: string;
  public readonly deleteBeforeLoad: boolean;

  constructor(env: NodeJS.Dict<string>) {
    const input = envvar.from(env);

    this.databaseUrl = input
      .get('DATABASE_URL')
      .default('postgresql://localhost:5432/identity')
      .asString();
    this.deleteBeforeLoad = input.get('DELETE_BEFORE_LOAD').default(0).asBool();
  }
}

export const load = async (config: LoadConfig): Promise<any> => {
  logger.info('LOAD | Connecting to database');
  const connectionString = config.databaseUrl;
  const pool = new pg.Pool({ connectionString });
  const client = await pool.connect();

  if (config.deleteBeforeLoad) {
    logger.info('LOAD | Deleting data before load');
    await truncateDatabase(getTableByName('HID'), client);
  }

  try {
    for (const table of TABLES) {
      await loadTable(table, client);
    }
  } finally {
    logger.info(`LOAD | Load complete`);
    client.release();
  }
  await pool.end();
};

const truncateDatabase = async (table: Table, client: PoolClient): Promise<any> => {
  logger.info(`LOAD | Truncate ${table.name}`);
  return client.query(`TRUNCATE ${table.name} CASCADE`);
};

const loadTable = async (table: Table, client: PoolClient): Promise<any> => {
  logger.info(`LOAD | Loading ${table.name}`);
  const ingestStream = client.query(
    copyFrom(`COPY ${table.name} (${columnsAsString(table)}) FROM STDIN WITH CSV HEADER`),
  );
  const sourceStream = fs.createReadStream(`${table.file}`);
  await pipeline(sourceStream, ingestStream);
};

const columnsAsString = (table: Table): string => {
  return table.columns.join(',');
};
