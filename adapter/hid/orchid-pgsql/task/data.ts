export type Table = {
  name: string;
  columns: string[];
  file: string;
};

export const TABLES: Table[] = [
  {
    name: 'HID',
    columns: ['HID'],
    file: 'dist/hid.csv',
  },
  {
    name: 'HID_TO_EMAIL',
    columns: ['HID', 'EMAIL'],
    file: 'dist/hid_to_email.csv',
  },
  {
    name: 'HID_AUDIT',
    columns: ['TX', 'SOURCE', 'HID', '"from"', '"to"', 'TYPE', 'TIMESTAMP'],
    file: 'dist/hid_audit.csv',
  },
];

export const getTableByName = (name: string): Table => {
  const match = TABLES.find((table) => {
    return (
      table.name.localeCompare(name, 'en', {
        sensitivity: 'base',
      }) == 0
    );
  });
  if (match === undefined) {
    throw new Error(`Table [$name] not found`);
  }
  return match;
};
