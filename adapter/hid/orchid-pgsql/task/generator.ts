import envvar from 'env-var';
import { nextHID, nextValidEmail, nextTx } from 'haven-hid-domain/mock.js';
import { ResultAsync, fromPromise, okAsync } from 'neverthrow';

import { logger } from '@havenengineering/module-haven-logging';
import { existsSync } from 'fs';
import { writeFile } from 'fs/promises';
import { Buffer } from 'node:buffer';
import { Table, getTableByName } from './data.js';

const DEFAULT_SIZE = 1000000;

export class GeneratorConfig {
  public readonly size: number;
  public readonly skipIfExisting: boolean;

  constructor(env: NodeJS.Dict<string>) {
    const input = envvar.from(env);

    this.size = input.get('SIZE').default(DEFAULT_SIZE).asInt();
    this.skipIfExisting = input.get('SKIP_IF_EXISTING').default(0).asBool();
  }
}

export const generate = (config: GeneratorConfig): ResultAsync<unknown, Error> => {
  if (config.skipIfExisting) {
    if (existsSync(getTableByName('HID').file)) {
      logger.info(`GENERATE | Skipping dataset/csv file generation: data files already exist`);
      return okAsync(undefined);
    }
  }

  logger.info(`GENERATE | Generating in-memory dataset with [${config.size}] entries`);
  const stepSize = Math.floor(config.size / 50);

  const hids = generateDataset(config.size, stepSize, 'HID', nextHID);
  const emails = generateDataset(config.size, stepSize, 'Email', () => {
    return nextValidEmail().toString();
  });

  const writeHidTable = writeTableToCSV(getTableByName('HID'), hids.values());
  const writeHidToEmailTable = writeTableToCSV(
    getTableByName('HID_TO_EMAIL'),
    hidToEmailGenerator(config.size, hids, emails),
  );
  const writeAudit = writeTableToCSV(
    getTableByName('HID_AUDIT'),
    hidAuditGenerator(config.size, hids, emails),
  );

  return ResultAsync.combine([writeHidTable, writeHidToEmailTable, writeAudit]);
};

const generateDataset = (
  total: number,
  stepSize: number,
  name: string,
  fn: () => string,
): Set<string> => {
  process.stdout.write(`${name}s:`.padEnd(10));
  const dataset = new Set<string>();
  while (dataset.size < total) {
    dataset.add(fn());
    if (dataset.size % stepSize == 0) {
      process.stdout.write('.');
    }
  }
  process.stdout.write(' [DONE]\n');

  return dataset;
};

function* hidToEmailGenerator(size: number, hids: Set<string>, emails: Set<string>) {
  const hidValues = hids.values();
  const emailValues = emails.values();
  let index = 0;

  while (index < size) {
    index++;
    yield `${hidValues.next().value},${emailValues.next().value}`;
  }
}

function* hidAuditGenerator(size: number, hids: Set<string>, emails: Set<string>) {
  const hidValues = hids.values();
  const emailValues = emails.values();
  let index = 0;

  while (index < size) {
    index++;
    const tx = nextTx('generator');
    const timestamp = new Date().toISOString();
    yield `${tx.id},generator,${hidValues.next().value},,${emailValues.next().value},email,${timestamp}`;
  }
}
const writeTableToCSV = (
  table: Table,
  generator: IterableIterator<string>,
): ResultAsync<void, Error> => {
  const data = Array.from(generator).join('\n');
  const headings = `${Array.from(table.columns).join(',')}\n`;
  const all = Buffer.concat([Buffer.from(headings), Buffer.from(data)]);
  const write = writeFile(table.file, all);
  return fromPromise(write, (e) => {
    return new Error(`Unable to generate ${table.name} data`, { cause: e });
  });
};
