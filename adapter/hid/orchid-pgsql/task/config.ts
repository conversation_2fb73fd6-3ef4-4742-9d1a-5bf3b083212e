import { logger } from '@havenengineering/module-haven-logging';
import dotenv from 'dotenv';
import { Database } from 'haven-identity-database';
import { GeneratorConfig } from './generator.js';
import { LoadConfig } from './load.js';
import findConfig from 'find-config';

export enum Mode {
  GENERATE = 'GENERATE',
  LOAD = 'LOAD',
  START = 'START',
  RESTART = 'RESTART',
  STOP = 'STOP',
  MIGRATE = 'MIGRATE',
  E2E_SEED = 'E2E_SEED',
}

export class Config {
  public database: Database = {
    user: 'hid',
    password: 'hid',
    name: 'identity',
  };
  public generator = new GeneratorConfig(process.env);
  public load = new LoadConfig(process.env);

  public ci: boolean = process.env.CI ? true : false;

  get mode(): Mode {
    let mode: keyof typeof Mode;
    for (mode in Mode) {
      if (process.argv.includes(`--${mode.toLocaleLowerCase()}`)) {
        return Mode[mode];
      }
    }
    return Mode.START;
  }
}

export const getConfig = (): Config => {
  const path: string = findConfig('.env') || '.env';
  dotenv.config({ path });

  const result = new Config();

  logger.info(`CONFIG | Mode is ${result.mode}`);

  return result;
};
