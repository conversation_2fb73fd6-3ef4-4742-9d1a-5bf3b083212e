import { HID_LENGTH } from 'haven-hid-domain';
import { rakeDb } from 'rake-db';
import { getDbConfig } from '../config.js';
import { rakeConfig } from '../db-migrate.js';
import { HidTable } from '../tables/hid.table.js';

rakeDb(getDbConfig().allDatabases, rakeConfig(), ['migrate'])(async (db) => {
  await db.createTable(
    'hid_address_requires_confirmation',
    { comment: 'indicates that the address for a hid needs confirmation' },
    // @ts-ignore
    (t) => ({
      hid: t.varchar(HID_LENGTH).primaryKey().foreignKey('hid', 'hid'),
    }),
  );
});
