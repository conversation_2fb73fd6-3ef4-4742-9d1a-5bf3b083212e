import { HID_LENGTH } from 'haven-hid-domain';
import { rakeDb } from 'rake-db';
import { getDbConfig } from '../config.js';
import { rakeConfig } from '../db-migrate.js';

rakeDb(getDbConfig().allDatabases, rakeConfig(), ['migrate'])(async (db) => {
  // @ts-ignore
  await db.createTable('hid_to_password', (t) => ({
    hid: t.text().unique().min(HID_LENGTH).max(HID_LENGTH).primaryKey(),
    hash: t.text().min(1).max(100),
    strength: t.text().min(1).max(20).nullable(),
    ...t.timestamps(),
  }));
  await db.addForeignKey('hid_to_password', ['hid'], 'hid', ['hid']);
});
