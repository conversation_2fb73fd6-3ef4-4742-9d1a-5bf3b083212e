import { HID_LENGTH } from 'haven-hid-domain';
import { rakeDb } from 'rake-db';
import { getDbConfig } from '../config.js';
import { rakeConfig } from '../db-migrate.js';

rakeDb(getDbConfig().allDatabases, rakeConfig(), ['migrate'])(async (db) => {
  await db.changeTable('hid_to_password', (t) => ({
    hid: t.change(t.text(), t.varchar(HID_LENGTH)),
  }));
  await db.changeTable('hid_to_profile', (t) => ({
    hid: t.change(t.text(), t.varchar(HID_LENGTH)),
  }));
});
