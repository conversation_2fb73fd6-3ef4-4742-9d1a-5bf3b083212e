import { HID_LENGTH } from 'haven-hid-domain';
import { rakeDb } from 'rake-db';
import { getDbConfig } from '../config.js';
import { rakeConfig } from '../db-migrate.js';

rakeDb(getDbConfig().allDatabases, rakeConfig(), ['migrate'])(async (db) => {
  await db.changeTable('hid', (t) => ({
    hid: t.change(t.text(), t.varchar(HID_LENGTH)),
  }));
  await db.changeTable('hid_audit', (t) => ({
    hid: t.change(t.text(), t.varchar(HID_LENGTH)),
    tx: t.change(t.text(), t.varchar(26)),
    source: t.change(t.text(), t.varchar(100)),
    type: t.change(t.text(), t.varchar(20)),
    from: t.change(t.text(), t.varchar(100)),
    to: t.change(t.text(), t.varchar(100)),
  }));
  await db.changeTable('hid_to_email', (t) => ({
    hid: t.change(t.text(), t.varchar(HID_LENGTH)),
    email: t.change(t.text(), t.varchar(100)),
  }));
  await db.changeTable('hid_to_plot_owner', (t) => ({
    hid: t.change(t.text(), t.varchar(HID_LENGTH)),
  }));
  await db.changeTable('hid_to_seaware_client', (t) => ({
    hid: t.change(t.text(), t.varchar(HID_LENGTH)),
  }));
});
