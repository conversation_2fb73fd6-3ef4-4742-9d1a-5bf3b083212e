import { HID_LENGTH } from 'haven-hid-domain';
import { rakeDb } from 'rake-db';
import { getDbConfig } from '../config.js';
import { rakeConfig } from '../db-migrate.js';

rakeDb(getDbConfig().allDatabases, rakeConfig(), ['migrate'])(async (db) => {
  // @ts-ignore
  await db.createTable('hid_to_profile', (t) => ({
    hid: t.text().unique().min(HID_LENGTH).max(HID_LENGTH).primaryKey(),
    name: t.text().min(1).max(100),
    title: t.text().min(1).max(20).nullable(),
    firstName: t.text().min(1).max(100).nullable(),
    lastName: t.text().min(1).max(100).nullable(),
    phoneNumber: t.text().min(1).max(30).nullable(),
    ...t.timestamps(),
  }));
  await db.addForeign<PERSON>ey('hid_to_profile', ['hid'], 'hid', ['hid']);
});
