import { HID_LENGTH } from 'haven-hid-domain';
import { rakeDb } from 'rake-db';
import { getDbConfig } from '../config.js';
import { rakeConfig } from '../db-migrate.js';

rakeDb(getDbConfig().allDatabases, rakeConfig(), ['migrate'])(async (db) => {
  // @ts-ignore
  await db.createTable('hid_audit', { noPrimaryKey: true }, (t) => ({
    tx: t.text().min(26).max(26).index(),
    source: t.text().min(1).max(100),
    type: t.text().min(1).max(30), // 'email' | 'plot_owner' | 'seaware_client
    from: t.text().min(6).max(100).nullable(),
    to: t.text().min(6).max(100).nullable(),
    hid: t.text().min(HID_LENGTH).max(HID_LENGTH),
    timestamp: t.timestamp().default(t.sql('now()')),
  }));
});
