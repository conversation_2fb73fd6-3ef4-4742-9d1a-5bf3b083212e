import { rakeDb } from 'rake-db';
import { getDbConfig } from '../config.js';
import { rakeConfig } from '../db-migrate.js';

rakeDb(getDbConfig().allDatabases, rakeConfig(), ['migrate'])(async (db) => {
  await db.addForeign<PERSON>ey('hid_to_email', ['hid'], 'hid', ['hid']);
  await db.addForeign<PERSON><PERSON>('hid_to_plot_owner', ['hid'], 'hid', ['hid']);
  await db.addForeign<PERSON>ey('hid_to_seaware_client', ['hid'], 'hid', ['hid']);
});
