import { rakeDb } from 'rake-db';
import { getDbConfig } from '../config.js';
import { rakeConfig } from '../db-migrate.js';
import {
  ADDRESS_LINE_LENGTH,
  ADDRESS_CITY_LENGTH,
  POSTCODE_LENGTH,
} from '../tables/hid-to-address.table.js';

rakeDb(getDbConfig().allDatabases, rakeConfig(), ['migrate'])(async (db) => {
  await db.changeTable('hid_to_address', (t) => ({
    address_line1: t.change(t.text(), t.varchar(ADDRESS_LINE_LENGTH)),
    address_city: t.change(t.text(), t.varchar(ADDRESS_CITY_LENGTH)),
    address_postcode: t.change(t.text(), t.varchar(POSTCODE_LENGTH)),
  }));
});
