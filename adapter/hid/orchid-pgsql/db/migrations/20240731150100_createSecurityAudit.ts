import { HID_LENGTH } from 'haven-hid-domain';
import { rakeDb } from 'rake-db';
import { getDbConfig } from '../config.js';
import { rakeConfig } from '../db-migrate.js';
import { EMAIL_LENGTH } from '../tables/hid-to-email.table.js';

rakeDb(getDbConfig().allDatabases, rakeConfig(), ['migrate'])(async (db) => {
  await db.createTable(
    'security_audit',
    { comment: 'audit table for security related logs' },
    // @ts-ignore
    (t) => ({
      id: t.bigSerial().primaryKey(),
      hid: t.varchar(HID_LENGTH).nullable(),
      email: t.varchar(EMAIL_LENGTH).index(),
      clientId: t.varchar(100),
      reason: t.varchar(255),
      source: t.varchar(80),
      userAgent: t.varchar(255),
      ipAddress: t.varchar(40),
      timestamp: t.timestamp().default(t.sql('now()')),
    }),
  );
});
