import { HID_LENGTH } from 'haven-hid-domain';
import { rakeDb } from 'rake-db';
import { getDbConfig } from '../config.js';
import { rakeConfig } from '../db-migrate.js';

rakeDb(getDbConfig().allDatabases, rakeConfig(), ['migrate'])(async (db) => {
  await db.adapter.query(
    `SET timezone = 'UTC';ALTER TABLE hid_to_email_verification_request alter column "created_at" type timestamptz, ALTER created_at SET DEFAULT now()`,
  );
});
