import { rakeDb } from 'rake-db';
import { getDbConfig } from '../config.js';
import { rakeConfig } from '../db-migrate.js';

rakeDb(getDbConfig().allDatabases, rakeConfig(), ['migrate'])(async (db) => {
  await db.query`CREATE EXTENSION IF NOT EXISTS pg_trgm;`;
  await db.addIndex('hid_audit', ['type'], { name: 'idx_hid_audit_type' });

  await db.query`CREATE INDEX idx_hid_audit_from_gin ON hid_audit USING gin("from" gin_trgm_ops);`;
  await db.query`CREATE INDEX idx_hid_audit_to_gin ON hid_audit USING gin("to" gin_trgm_ops);`;
});
