import { HID_LENGTH } from 'haven-hid-domain';
import { rakeDb } from 'rake-db';
import { getDbConfig } from '../config.js';
import { rakeConfig } from '../db-migrate.js';

rakeDb(getDbConfig().allDatabases, rakeConfig(), ['migrate'])(async (db) => {
  // @ts-ignore
  await db.createTable('hid_to_seaware_client', (t) => ({
    seaware_client_id: t.integer().primaryKey().unique(),
    hid: t.text().unique().min(HID_LENGTH).max(HID_LENGTH),
    ...t.timestamps(),
  }));
});
