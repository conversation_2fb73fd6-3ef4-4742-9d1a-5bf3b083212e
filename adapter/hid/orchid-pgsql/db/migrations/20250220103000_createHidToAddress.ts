import { HID_LENGTH } from 'haven-hid-domain';
import { rakeDb } from 'rake-db';
import { getDbConfig } from '../config.js';
import { rakeConfig } from '../db-migrate.js';
import {
  ADDRESS_LINE_LENGTH,
  POSTCODE_LENGTH,
  COUNTRY_CODE_LENGTH,
} from '../tables/hid-to-address.table.js';
import { HidTable } from '../tables/hid.table.js';

rakeDb(getDbConfig().allDatabases, rakeConfig(), ['migrate'])(async (db) => {
  await db.createTable(
    'hid_to_address',
    { comment: 'current and historical addresses' },
    // @ts-ignore
    (t) => ({
      id: t.bigSerial().primaryKey(),
      hid: t.varchar(HID_LENGTH).foreignKey(() => HidTable, 'hid'),
      address_line1: t.varchar(ADDRESS_LINE_LENGTH),
      address_line2: t.varchar(ADDRESS_LINE_LENGTH).nullable(),
      address_city: t.varchar(ADDRESS_LINE_LENGTH),
      address_county: t.varchar(ADDRESS_LINE_LENGTH),
      address_postcode: t.varchar(POSTCODE_LENGTH),
      address_country: t.varchar(COUNTRY_CODE_LENGTH).default('GB'),
      address_effective_from: t.timestamp().default(t.sql('now()')),
    }),
  );
});
