import { HID_LENGTH } from 'haven-hid-domain';
import { rakeDb } from 'rake-db';
import { getDbConfig } from '../config.js';
import { rakeConfig } from '../db-migrate.js';

/**
 * change the audit from/to to be text to allow larger objects to be serialised as JSON
 */
rakeDb(getDbConfig().allDatabases, rakeConfig(), ['migrate'])(async (db) => {
  await db.adapter.query(
    `ALTER TABLE hid_audit alter column "from" type TEXT using "from" :: TEXT`,
  );
  await db.adapter.query(`ALTER TABLE hid_audit alter column "to" type TEXT using "to" :: TEXT`);
});
