import { HID_LENGTH } from 'haven-hid-domain';
import { rakeConfig } from '../db-migrate.js';
import { rakeDb } from 'rake-db';
import { getDbConfig } from '../config.js';

rakeDb(getDbConfig().allDatabases, rakeConfig(), ['migrate'])(async (db) => {
  // @ts-ignore
  await db.createTable('hid_to_email', (t) => ({
    email: t.text().primaryKey().unique().min(6).max(100),
    hid: t.text().unique().min(HID_LENGTH).max(HID_LENGTH),
    ...t.timestamps(),
  }));
});
