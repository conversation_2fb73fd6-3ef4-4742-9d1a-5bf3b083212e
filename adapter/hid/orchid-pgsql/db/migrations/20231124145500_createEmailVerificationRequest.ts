import { HID_LENGTH } from 'haven-hid-domain';
import { rakeDb } from 'rake-db';
import { getDbConfig } from '../config.js';
import { rakeConfig } from '../db-migrate.js';

export const TOKEN_LENGTH = 26;
export const TOKEN_TYPE_LENGTH = 30;
rakeDb(getDbConfig().allDatabases, rakeConfig(), ['migrate'])(async (db) => {
  // @ts-ignore
  await db.createTable('hid_to_email_verification_request', (t) => ({
    token: t.varchar(TOKEN_LENGTH).unique().min(TOKEN_LENGTH).max(TOKEN_LENGTH).primaryKey(),
    hid: t.varchar(HID_LENGTH).min(HID_LENGTH).max(HID_LENGTH).index(),
    returnUrl: t.varchar().min(1),
    type: t.varchar(TOKEN_TYPE_LENGTH).min(1).max(TOKEN_TYPE_LENGTH),
    createdAt: t.timestampNoTZ().default(t.sql('now()')),
  }));
});
