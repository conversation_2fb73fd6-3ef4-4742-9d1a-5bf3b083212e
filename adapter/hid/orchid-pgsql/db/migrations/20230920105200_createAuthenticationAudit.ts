import { HID_LENGTH } from 'haven-hid-domain';
import { rakeDb } from 'rake-db';
import { getDbConfig } from '../config.js';
import { rakeConfig } from '../db-migrate.js';
import { EMAIL_LENGTH } from '../tables/hid-to-email.table.js';

rakeDb(getDbConfig().allDatabases, rakeConfig(), ['migrate'])(async (db) => {
  await db.createTable(
    'authentication_audit',
    { comment: 'audit table for login attempts' },
    // @ts-ignore
    (t) => ({
      id: t.bigSerial().primaryKey(),
      hid: t.varchar(HID_LENGTH).nullable(),
      email: t.varchar(EMAIL_LENGTH).index(),
      outcome: t.varchar(30),
      clientId: t.varchar(100),
      method: t.varchar(100),
      userAgent: t.varchar(255),
      ipAddress: t.varchar(40),
      timestamp: t.timestamp().default(t.sql('now()')),
    }),
  );
});
