import dotenv from 'dotenv';
import findConfig from 'find-config';
import { OrchidORM, orchidORM } from 'orchid-orm';
import { getDbConfig } from './config.js';
import { HidAuditTable } from './tables/hid-audit.table.js';
import { HidToEmailTable } from './tables/hid-to-email.table.js';
import { HidToPasswordTable } from './tables/hid-to-password.table.js';
import { HidToPlotOwnerTable } from './tables/hid-to-plot-owner.table.js';
import { HidToProfileTable } from './tables/hid-to-profile.table.js';
import { HidToSeaWareClientTable } from './tables/hid-to-seaware-client.table.js';
import { HidTable } from './tables/hid.table.js';
import { AuthenticationAuditTable } from './tables/authentication-audit.table.js';
import { HidToEmailVerificationRequestTable } from './tables/hid-to-email-verification-request.table.js';
import { SecurityAuditTable } from './tables/security-audit.table.js';
import { HidToAddressTable } from './tables/hid-to-address.table.js';
import { HidAddressRequiresConfirmationTable } from './tables/hid-address-requires-confirmation.table.js';

export type HidTables = {
  hid: typeof HidTable;
  hidAudit: typeof HidAuditTable;
  hidToEmail: typeof HidToEmailTable;
  hidToPlotOwner: typeof HidToPlotOwnerTable;
  hidToSeaWareClient: typeof HidToSeaWareClientTable;
  hidToPassword: typeof HidToPasswordTable;
  hidToProfile: typeof HidToProfileTable;
  authenticationAudit: typeof AuthenticationAuditTable;
  hidToEmailVerificationRequest: typeof HidToEmailVerificationRequestTable;
  securityAudit: typeof SecurityAuditTable;
  hidToAddress: typeof HidToAddressTable;
  hidAddressRequiresConfirmation: typeof HidAddressRequiresConfirmationTable;
};

export class Db {
  private static _instance: OrchidORM<HidTables>;

  public static get instance(): OrchidORM<HidTables> {
    if (!this._instance) {
      const path: string = findConfig('.env') || '.env';
      dotenv.config({ path });
      this._instance = orchidORM(getDbConfig().database, {
        hid: HidTable,
        hidAudit: HidAuditTable,
        hidToEmail: HidToEmailTable,
        hidToPlotOwner: HidToPlotOwnerTable,
        hidToSeaWareClient: HidToSeaWareClientTable,
        hidToPassword: HidToPasswordTable,
        hidToProfile: HidToProfileTable,
        authenticationAudit: AuthenticationAuditTable,
        hidToEmailVerificationRequest: HidToEmailVerificationRequestTable,
        securityAudit: SecurityAuditTable,
        hidToAddress: HidToAddressTable,
        hidAddressRequiresConfirmation: HidAddressRequiresConfirmationTable,
      });
    }
    return this._instance;
  }
}
