import { HID_LENGTH } from 'haven-hid-domain';
import { BaseTable, TableConfig } from '../baseTable.js';
import { HidTable } from './hid.table.js';

export type HidToSeaWareClient = HidToSeaWareClientTable['columns']['type'];
export class HidToSeaWareClientTable extends BaseTable {
  readonly table = 'hid_to_seaware_client';
  columns: TableConfig = this.setColumns((t) => ({
    seawareClientId: t.integer().primaryKey().unique(),
    hid: t
      .text(HID_LENGTH, HID_LENGTH)
      .unique()
      .foreignKey(() => HidTable, 'hid'),
    ...t.timestamps(),
  }));
}
