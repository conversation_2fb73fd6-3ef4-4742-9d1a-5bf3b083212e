import { BaseTable, TableConfig } from '../baseTable.js';
import { HID_LENGTH } from 'haven-hid-domain';
import { EMAIL_LENGTH } from './hid-to-email.table.js';

export class SecurityAuditTable extends BaseTable {
  readonly table = 'security_audit';
  columns: TableConfig = this.setColumns((t) => ({
    id: t.identity({ always: true }).primaryKey(),
    hid: t.text(HID_LENGTH, HID_LENGTH).nullable(),
    email: t.text(1, EMAIL_LENGTH),
    clientId: t.text(1, 100),
    reason: t.text(0, 255),
    source: t.text(0, 80),
    userAgent: t.text(0, 255),
    ipAddress: t.text(0, 40),
    timestamp: t.timestamp().default(t.sql('now()')),
  }));
}
