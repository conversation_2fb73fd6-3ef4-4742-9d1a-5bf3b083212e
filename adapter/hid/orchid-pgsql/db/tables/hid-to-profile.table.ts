import { HID_LENGTH } from 'haven-hid-domain';
import { BaseTable, TableConfig } from '../baseTable.js';
import { HidTable } from './hid.table.js';

export class HidToProfileTable extends BaseTable {
  readonly table = 'hid_to_profile';
  columns: TableConfig = this.setColumns((t) => ({
    hid: t
      .text(HID_LENGTH, HID_LENGTH)
      .unique()
      .foreignKey(() => HidTable, 'hid')
      .primaryKey(),
    name: t.text(1, 100),
    title: t.text(1, 20).nullable(),
    firstName: t.text(1, 100).nullable(),
    lastName: t.text(1, 100).nullable(),
    phoneNumber: t.text(1, 30).nullable(),
    ...t.timestamps(),
  }));
}
