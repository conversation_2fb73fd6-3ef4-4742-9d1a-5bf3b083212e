import { BaseTable, TableConfig } from '../baseTable.js';
import { HID_LENGTH } from 'haven-hid-domain';
import { HidTable } from './hid.table.js';

export const ADDRESS_LINE_LENGTH = 100;
export const ADDRESS_CITY_LENGTH = 50;
export const POSTCODE_LENGTH = 10;
export const COUNTRY_CODE_LENGTH = 2;

export class HidToAddressTable extends BaseTable {
  readonly table = 'hid_to_address';
  columns: TableConfig = this.setColumns((t) => ({
    id: t.identity({ always: true }).primaryKey(),
    hid: t.text(HID_LENGTH, HID_LENGTH).foreignKey(() => HidTable, 'hid'),
    address_line1: t.text(1, ADDRESS_LINE_LENGTH),
    address_line2: t.varchar(ADDRESS_LINE_LENGTH).nullable(),
    address_city: t.text(1, ADDRESS_CITY_LENGTH),
    address_county: t.varchar(ADDRESS_LINE_LENGTH).nullable(),
    address_postcode: t.text(1, POSTCODE_LENGTH),
    address_country: t.text(COUNTRY_CODE_LENGTH, COUNTRY_CODE_LENGTH).default('GB'),
    address_effective_from: t.timestamp().default(t.sql('now()')),
  }));
}
