import { HID_LENGTH } from 'haven-hid-domain';
import { BaseTable, TableConfig } from '../baseTable.js';
import { HidTable } from './hid.table.js';

export type HidToPlotOwner = HidToPlotOwnerTable['columns']['type'];
export class HidToPlotOwnerTable extends BaseTable {
  readonly table = 'hid_to_plot_owner';
  columns: TableConfig = this.setColumns((t) => ({
    plotOwnerId: t.integer().primaryKey().unique(),
    hid: t
      .text(HID_LENGTH, HID_LENGTH)
      .unique()
      .foreignKey(() => HidTable, 'hid'),
    ...t.timestamps(),
  }));
}
