import { HID_LENGTH } from 'haven-hid-domain';
import { BaseTable, TableConfig } from '../baseTable.js';

export type HidAudit = HidAuditTable['columns']['type'];
export class HidAuditTable extends BaseTable {
  readonly table = 'hid_audit';
  noPrimaryKey = true;
  columns: TableConfig = this.setColumns((t) => ({
    tx: t.text(26, 26).index(),
    source: t.text(1, 100),
    type: t.text(1, 20), // 'email' | 'plot_owner' | 'seaware_client
    from: t.varchar().nullable(),
    to: t.varchar().nullable(),
    hid: t.text(HID_LENGTH, HID_LENGTH),
    timestamp: t.timestamp().default(t.sql('now()')),
    hid_audit_id: t.bigint().identity({ always: true }).primaryKey(),
  }));
}
