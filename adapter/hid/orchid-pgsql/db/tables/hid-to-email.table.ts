import { HID_LENGTH } from 'haven-hid-domain';
import { BaseTable, TableConfig } from '../baseTable.js';
import { HidTable } from './hid.table.js';

export const EMAIL_LENGTH = 100;

export class HidToEmailTable extends BaseTable {
  readonly table = 'hid_to_email';
  columns: TableConfig = this.setColumns((t) => ({
    email: t.text(6, EMAIL_LENGTH).unique(),
    emailVerified: t.boolean(),
    hid: t
      .text(HID_LENGTH, HID_LENGTH)
      .primaryKey()
      .unique()
      .foreignKey(() => HidTable, 'hid'),
    createdAt: t.timestamp(),
    updatedAt: t.timestamp(),
  }));
}
