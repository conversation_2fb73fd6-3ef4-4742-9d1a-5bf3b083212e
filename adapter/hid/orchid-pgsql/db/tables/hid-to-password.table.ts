import { HID_LENGTH } from 'haven-hid-domain';
import { BaseTable, TableConfig } from '../baseTable.js';
import { HidTable } from './hid.table.js';

export class HidToPasswordTable extends BaseTable {
  readonly table = 'hid_to_password';
  columns: TableConfig = this.setColumns((t) => ({
    hid: t
      .text(HID_LENGTH, HID_LENGTH)
      .unique()
      .foreignKey(() => HidTable, 'hid')
      .primaryKey(),
    hash: t.text(1, 100),
    strength: t.text(1, 20).nullable(),
    ...t.timestamps(),
  }));
}
