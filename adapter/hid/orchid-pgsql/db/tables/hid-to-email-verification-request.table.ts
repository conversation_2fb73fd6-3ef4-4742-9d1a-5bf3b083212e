import { BaseTable, TableConfig } from '../baseTable.js';
import { HID_LENGTH } from 'haven-hid-domain';

export const TOKEN_LENGTH = 26;
export const TOKEN_TYPE_LENGTH = 30;
export class HidToEmailVerificationRequestTable extends BaseTable {
  readonly table = 'hid_to_email_verification_request';
  columns: TableConfig = this.setColumns((t) => ({
    token: t.varchar(TOKEN_LENGTH).unique().min(TOKEN_LENGTH).max(TOKEN_LENGTH).primaryKey(),
    hid: t.varchar(HID_LENGTH).min(HID_LENGTH).max(HID_LENGTH).index(),
    returnUrl: t.varchar().min(1),
    type: t.varchar(TOKEN_TYPE_LENGTH).min(1).max(TOKEN_TYPE_LENGTH),
    createdAt: t.timestamp().default(t.sql('now()')),
  }));
}
