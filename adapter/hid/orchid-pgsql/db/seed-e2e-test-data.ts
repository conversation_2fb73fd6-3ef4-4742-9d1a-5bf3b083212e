import { Db } from './db.js';

export const seed = async () => {
  const hidTable = Db.instance.hid;
  const emailTable = Db.instance.hidToEmail;
  const seawareClientTable = Db.instance.hidToSeaWareClient;
  const plotOwnerTable = Db.instance.hidToPlotOwner;
  const passwordTable = Db.instance.hidToPassword;
  const profileTable = Db.instance.hidToProfile;

  const passwordHash = '$2b$10$y08b3iqBzJjUrtBq11zU7.0rAqSZdX2bX3265BkC7Pg.Nd.x3H15S';
  const hids = ['E2E000000001', 'E2E000000002', 'E2E000000003', 'E2E000000004'];
  const deleteWhere = { hid: { in: hids } };

  await plotOwnerTable.where(deleteWhere).delete();
  await seawareClientTable.where(deleteWhere).delete();
  await emailTable.where(deleteWhere).delete();
  await passwordTable.where(deleteWhere).delete();
  await profileTable.where(deleteWhere).delete();
  await hidTable.where(deleteWhere).delete();

  await hidTable.createMany([
    { hid: 'E2E000000001' },
    { hid: 'E2E000000002' },
    { hid: 'E2E000000003' },
    { hid: 'E2E000000004' },
  ]);
  await emailTable.createMany([
    { hid: 'E2E000000001', email: '<EMAIL>' },
    { hid: 'E2E000000002', email: '<EMAIL>' },
    { hid: 'E2E000000003', email: '<EMAIL>' },
    { hid: 'E2E000000004', email: '<EMAIL>' },
  ]);
  await passwordTable.createMany([
    { hid: 'E2E000000001', hash: passwordHash },
    { hid: 'E2E000000002', hash: passwordHash },
    { hid: 'E2E000000003', hash: passwordHash },
    { hid: 'E2E000000004', hash: passwordHash },
  ]);
  await profileTable.createMany([
    {
      hid: 'E2E000000001',
      firstName: 'Etest',
      lastName: 'Lite',
      title: 'Mr',
      name: 'Mr Etest Lite',
    },
    {
      hid: 'E2E000000002',
      firstName: 'Etest',
      lastName: 'Seaware',
      title: 'Mr',
      name: 'Mr Etest Seaware',
    },
    {
      hid: 'E2E000000003',
      firstName: 'Etest',
      lastName: 'Plot',
      title: 'Mr',
      name: 'Mr Etest Plot',
    },
    {
      hid: 'E2E000000004',
      firstName: 'Etest',
      lastName: 'Seaware Plot',
      title: 'Mr',
      name: 'Mr Etest Seaware Plot',
    },
  ]);

  await seawareClientTable.createMany([
    { hid: 'E2E000000002', seawareClientId: 43395345 },
    { hid: 'E2E000000004', seawareClientId: 43395346 },
  ]);
  await plotOwnerTable.createMany([
    { hid: 'E2E000000003', plotOwnerId: 3000000 },
    { hid: 'E2E000000004', plotOwnerId: 4000000 },
  ]);
};
