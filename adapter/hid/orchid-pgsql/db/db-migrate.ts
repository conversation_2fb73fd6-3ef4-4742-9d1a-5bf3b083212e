import { logger } from '@havenengineering/module-haven-logging';
import dotenv from 'dotenv';
import findConfig from 'find-config';
import { fromPromise, ResultAsync } from 'neverthrow';
import { rakeDb } from 'rake-db';
import { getDbConfig, Mode } from './config.js';

export const rakeConfig = () => {
  const path: string = findConfig('.env') || '.env';
  dotenv.config({ path });

  return {
    snakeCase: true,
    migrationsPath: './migrations/ts',
    useCodeUpdater: false,
    commands: {
      async seed() {
        const { seed } = await import('./seed-e2e-test-data.js');
        await seed();
      },
    },
    import: (path: string): Promise<unknown> => {
      logger.debug(`Converting [${path}]`);
      const pathToJs = path.replace(`/ts`, '').replace('.ts', '.js');
      return import(pathToJs);
    },
  };
};

export const runMigrations = (path?: string): ResultAsync<void, Error> => {
  const migrate = async () => {
    logger.info('ADAPTER | Running database migrations');

    const config = rakeConfig();
    if (path != undefined) {
      config.migrationsPath = path;
    }

    for (const db of getDbConfig(Mode.MIGRATE).allDatabases) {
      await rakeDb(db, config, ['migrate']).promise;
    }

    logger.info('ADAPTER | Completed database migrations');
  };
  const toError = (e: unknown): Error => {
    logger.error('ADAPTER | Failed to migrate database', e);
    return new Error('Unable to migrate database', { cause: e });
  };

  return fromPromise(migrate(), toError);
};

export const runSeedForE2e = (): ResultAsync<void, Error> => {
  const seed = async () => {
    logger.info('ADAPTER | Running database E2E seed');

    const config = rakeConfig();
    const db = getDbConfig(Mode.MIGRATE).database;
    await rakeDb(db, config, ['seed']).promise;

    logger.info('ADAPTER | Completed database seeds');
  };
  const toError = (e: unknown): Error => {
    logger.error('ADAPTER | Failed to seed database', e);
    return new Error('Unable to seed database', { cause: e });
  };

  return fromPromise(seed(), toError);
};
