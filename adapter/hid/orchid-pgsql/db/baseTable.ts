import { createBaseTable, Table } from 'orchid-orm';
import {} from 'pqb'; // See https://github.com/microsoft/TypeScript/issues/47663#issuecomment-1519138189
export type TableConfig = Table['columns'];

export const BaseTable = createBaseTable({
  snakeCase: true,
  columnTypes: (t) => ({
    ...t,
    text: (min: number = 0, max: number = Infinity) => t.text(min, max),
    timestamp: <P extends number>(precision?: P) => t.timestamp<P>(precision).asDate(),
  }),
});
