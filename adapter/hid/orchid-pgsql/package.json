{"name": "haven-hid-pgsql-adapter", "version": "0.0.1", "description": "Haven HID Adapter for PostgreSQL using Orchid-ORM", "type": "module", "main": "./dist/src/exports.js", "types": "./dist/src/exports.d.ts", "exports": {".": "./dist/src/exports.js", "./adapter.js": "./dist/src/exports.js", "./migration.js": "./dist/db/exports.js"}, "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "copy:migrations": "rimraf dist/db/migrations/ts && pnpm copyfiles -u 2 db/migrations/* dist/db/migrations/ts", "build": "tsc && pnpm copy:migrations", "test": "vitest run --coverage --reporter verbose --reporter json --outputFile dist/vitest.json --pool forks --poolOptions.forks.singleFork", "test:unit": "pnpm test", "test:interactive": "vitest", "db": "node dist/db/main.js", "start": "pnpm build && TESTCONTAINERS_RYUK_DISABLED=true pnpm task --start", "migrate": "pnpm build && TESTCONTAINERS_RYUK_DISABLED=true pnpm task --migrate", "seed-e2e": "pnpm build && TESTCONTAINERS_RYUK_DISABLED=true pnpm task --e2e_seed", "populate": "pnpm build && SIZE=4000000 pnpm task --generate && DELETE_BEFORE_LOAD=true pnpm task --load", "stop": "pnpm task --stop", "task": "node dist/task/main.js", "restart": "TESTCONTAINERS_RYUK_DISABLED=true pnpm task --restart", "full": "pnpm restart && pnpm test"}, "author": "Haven Engineering", "license": "ISC", "dependencies": {"@havenengineering/module-haven-logging": "^0.5.1", "@havenengineering/module-shared-password-strength": "workspace:*", "dotenv": "^16.4.5", "env-var": "^7.5.0", "find-config": "^1.0.0", "haven-hid-domain": "workspace:*", "neverthrow": "^6.2.1", "orchid-orm": "^1.17.30", "pg": "^8.11.5", "pg-copy-streams": "^6.0.6", "pqb": "^0.18.29", "rake-db": "^2.10.65"}, "devDependencies": {"@tsconfig/node20": "^20.1.4", "@types/find-config": "^1.0.4", "@types/humanize-duration": "^3.27.4", "@types/pg": "^8.11.6", "@types/pg-copy-streams": "^1.2.5", "@vitest/coverage-v8": "^3.0.8", "casual": "^1.6.2", "copyfiles": "^2.4.1", "handlebars": "^4.7.8", "haven-identity-database": "workspace:*", "humanize-duration": "^3.32.1", "mkdirp": "^3.0.1", "mockdate": "^3.0.5", "orchid-orm-test-factory": "^0.3.173", "rimraf": "^5.0.7", "run-script-os": "^1.1.6", "ts-node": "^10.9.2", "typescript": "^5.4.5", "vitest": "^3.0.8"}, "engines": {"npm": "use-pnpm", "node": ">=20"}}