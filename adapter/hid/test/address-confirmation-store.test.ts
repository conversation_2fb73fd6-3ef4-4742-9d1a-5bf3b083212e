import { describe, it, expect, beforeAll } from 'vitest';
import { OrchidHidAddressRequiresConfirmationStore } from '../orchid-pgsql/src/hid/hid-address-confirmation-store.orchid.js';

describe.each([[new OrchidHidAddressRequiresConfirmationStore()]])(
  'Address Requires Confirmation Store Tests',
  (store) => {
    beforeAll(async () => {
      // delete first just in case a previous test did not complete
      await store.remove('R2D2C3P0');
    });

    describe('Store implementation', () => {
      it('Should return false for unknown hid', async () => {
        const absent = await store.needsConfirmation('unknown');
        expect(absent).toBeFalsy();
      });

      it('Should create, read, delete', async () => {
        await store.add('R2D2C3P0');

        const present = await store.needsConfirmation('R2D2C3P0');
        expect(present).toBeTruthy();

        await store.remove('R2D2C3P0');

        const absent = await store.needsConfirmation('R2D2C3P0');
        expect(absent).toBeFalsy();
      });
    });
  },
);
