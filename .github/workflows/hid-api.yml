name: HID API BUILD AND TEST

on:
  schedule:
  - cron: "30 3 * * *"
  pull_request:
    branches:
    - main
    paths:
    - api/hid/**
    - domain/hid/**
    - adapter/hid/**
    - lib/blapi/**
    - lib/plot/**
    - lib/haven-fastify/**
    - lib/names/**
    - lib/password-util/**
    - lib/password-strength/**
    - lib/email-validation/**
    - .github/workflows/hid-api.yml
    - pnpm-workspace.yaml
    - pnpm-lock.yaml
    - .npmrc
    - Dockerfile
  push:
    branches:
    - master
    - dev
    - feat/*
    - chore/*
    - build/*
    - fix/*
    paths:
    - api/hid/**
    - domain/hid/**
    - adapter/hid/**
    - lib/blapi/**
    - lib/plot/**
    - lib/haven-fastify/**
    - lib/names/**
    - lib/password-util/**
    - lib/password-strength/**
    - .github/workflows/hid-api.yml
    - pnpm-workspace.yaml
    - pnpm-lock.yaml
    - .npmrc
    - Dockerfile

env:
  NODE_VERSION: "20.x"
  APP_NAME: "service-haven-identity-hid"
  IDENTITY_SSO_API_KEY: ${{ secrets.IDENTITY_SSO_API_KEY }}
  ECR_REGISTRY: "745662293263.dkr.ecr.eu-west-1.amazonaws.com"
  BRAND: "haven"
  PRODUCT: "identity"
  TRIBE: "foundation"

jobs:
  build:
    name: Build for HID api
    runs-on: platform-runners-dev
    steps:
    - name: Checkout
      uses: actions/checkout@v3

    - uses: pnpm/action-setup@v2
      name: Install pnpm
      with:
        version: 8
        run_install: false

    - name: Use Node.js ${{ env.NODE_VERSION }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ env.NODE_VERSION }}
        registry-url: https://npm.pkg.github.com
        scope: '@havenengineering'
        cache: 'pnpm'

    - name: Install
      #      - name: Audit
      #        run: |
      #          pnpm audit -P --audit-level critical

      env:
        GITHUB_TOKEN: ${{ secrets.HAVEN_PLATFORM_BOT_PACKAGE_READ_PAT }}
      run: |
        pnpm --filter "haven-hid-api..." install --frozen-lockfile --config.dedupe-peer-dependents=false

    - name: Build
      run: |
        pnpm --filter "haven-hid-api..." build

    - name: Run tests
      run: |
        pnpm --filter "haven-hid-api" test

    - name: Notify about failure
      if: ${{ failure() }}
      run: |
        cat << EOF > message.json
        {"@type":"MessageCard","@context":"https://schema.org/extensions","summary":"Pipeline build failed!","themeColor":"ff0000","title":"$GITHUB_REPOSITORY pipeline failed 💢!","sections":[{"facts":[{"name":"Repository:","value":"$GITHUB_REPOSITORY"},{"name":"Branch:","value":"$GITHUB_REF_NAME"},{"name":"Commit:","value":"$GITHUB_SHA"}]}],"potentialAction":[{"@type":"OpenUri","name":"View on GitHub","targets":[{"os":"default","uri":"$GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID"}]}]}
        EOF
        curl -X POST ${{ secrets.TEAMS_WEBHOOK }} --header 'Content-Type: application/json' -d @message.json
  wait-for-tests:
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/master' && github.event_name != 'schedule'
    steps:
    - name: Setup Make
      run: sudo apt-get update && sudo apt-get install make
      
    - uses: lewagon/wait-on-check-action@v1.3.1
      with:
        ref: master
        check-name: 'Run E2E Tests'
        repo-token: ${{ secrets.GITHUB_TOKEN }}
        wait-interval: 10

  publish:
    name: Publish docker
    if: github.ref == 'refs/heads/master' && github.event_name != 'schedule'
    runs-on: platform-runners-ecr
    needs: wait-for-tests
    concurrency:
      group: docker-publishing-hid-api-${{ github.ref }}
    steps:
    - name: Checkout
      uses: actions/checkout@v3
    - name: Reset tags
      run: |
        git tag -l | xargs git tag -d
        git fetch --tags
    - name: Use Node.js ${{ env.NODE_VERSION }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ env.NODE_VERSION }}
        registry-url: https://npm.pkg.github.com
        scope: '@havenengineering'
    - name: Use pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 8.6.0
    - name: Semantic Release
      id: semantic # Needs it so we can get the outputs
      uses: cycjimmy/semantic-release-action@v3
      with:
        working_directory: ./api/hid
        tag_format: ${{ env.APP_NAME }}-v${version}
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        HUSKY: 0

    - name: Generate APP_VERSION for ReRelease
      if: steps.semantic.outputs.new_release_published != 'true'
      run: |
        LAST_VERSION=$(git describe --tags --match "service-haven-identity-hid-v*" --abbrev=4 HEAD | sed -r "s/service-haven-identity-hid-v//g");
        BUILD_TIMESTAMP=$(date +'%y%m%d%H%M');
        echo "APP_VERSION=${LAST_VERSION}-${BUILD_TIMESTAMP}" >> $GITHUB_ENV

    - name: Set APP_VERSION
      if: steps.semantic.outputs.new_release_published == 'true'
      run: |
        echo "APP_VERSION=${{ steps.semantic.outputs.new_release_version }}" >> $GITHUB_ENV

    - name: Login to Docker registry
      run: |
        aws ecr get-login-password --region eu-west-1 | docker login --username AWS --password-stdin ${ECR_REGISTRY}

    - name: Build and Push Docker image
      env:
        GITHUB_TOKEN: ${{ secrets.HAVEN_PLATFORM_BOT_PACKAGE_READ_PAT }}
      run: |
        ./build.sh haven-hid-api start ${APP_VERSION} ${ECR_REGISTRY}/${TRIBE}/${APP_NAME}:${APP_VERSION}
        docker push ${ECR_REGISTRY}/${TRIBE}/${APP_NAME}:${APP_VERSION}

    - name: Wait for deployment
      env:
        GITHUB_TOKEN: ${{ secrets.HAVEN_PLATFORM_BOT_PACKAGE_READ_PAT }}
      run: |
        cd deploy-check;
        pnpm i
        pnpm build
        pnpm start https://service-haven-identity-hid.dev.haven-leisure.com/status ${APP_VERSION}

    - uses: actions/checkout@v3
    - name: Notify about failure
      if: ${{ failure() }}
      run: |
        cat << EOF > message.json
        {"@type":"MessageCard","@context":"https://schema.org/extensions","summary":"Pipeline deploy failed!","themeColor":"ff0000","title":"$GITHUB_REPOSITORY pipeline failed 💢!","sections":[{"facts":[{"name":"Repository:","value":"$GITHUB_REPOSITORY"},{"name":"Branch:","value":"$GITHUB_REF_NAME"},{"name":"Commit:","value":"$GITHUB_SHA"}]}],"potentialAction":[{"@type":"OpenUri","name":"View on GitHub","targets":[{"os":"default","uri":"$GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID"}]}]}
        EOF
        curl -X POST ${{ secrets.TEAMS_WEBHOOK }} --header 'Content-Type: application/json' -d @message.json

  load-test:
    name: HID Load Test
    needs: publish
    runs-on: platform-runners-ecr
    steps:
    - name: Checkout
      uses: actions/checkout@v3

    - uses: pnpm/action-setup@v2
      name: Install pnpm
      with:
        version: 8
        run_install: false

    - name: Use Node.js ${{ env.NODE_VERSION }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ env.NODE_VERSION }}
        registry-url: https://npm.pkg.github.com
        scope: '@havenengineering'
        cache: 'pnpm'

    - name: Install
      env:
        GITHUB_TOKEN: ${{ secrets.HAVEN_PLATFORM_BOT_PACKAGE_READ_PAT }}
      run: |
        pnpm --filter "load-test" install --frozen-lockfile --config.dedupe-peer-dependents=false

    - name: Build
      run: |
        pnpm --filter "load-test" build

    - name: Setup k6
      uses: grafana/setup-k6-action@v1

    - name: Run Load Test against DEV
      env:
        APP_KEY: ${{ secrets.PERF_TEST_APP_KEY }}
      run: |
        pnpm --filter "load-test" start:all

    - name: Notify about failure
      if: ${{ failure() }}
      run: |
        cat << EOF > message.json
        {"@type":"MessageCard","@context":"https://schema.org/extensions","summary":"Pipeline HID Load Tests failed!","themeColor":"ff0000","title":"$GITHUB_REPOSITORY pipeline failed 💢!","sections":[{"facts":[{"name":"Repository:","value":"$GITHUB_REPOSITORY"},{"name":"Branch:","value":"$GITHUB_REF_NAME"},{"name":"Commit:","value":"$GITHUB_SHA"}]}],"potentialAction":[{"@type":"OpenUri","name":"View on GitHub","targets":[{"os":"default","uri":"$GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID"}]}]}
        EOF
        curl -X POST ${{ secrets.TEAMS_WEBHOOK }} --header 'Content-Type: application/json' -d @message.json
