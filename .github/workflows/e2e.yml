name: E2E TESTS

on:
  pull_request:
    branches:
    - main
    paths:
    - e2e/**
    - adapter/**
    - app/admin/**
    - app/identity/**
    - api/identity/**
    - api/hid/**
    - lib/blapi/**
    - lib/plot/**
    - lib/haven-fastify/**
    - lib/names/**
    - lib/email-validation/**
    - lib/password-strength-ui/**
    - lib/password-strength/**
    - lib/docker-util/**
    - .github/workflows/e2e.yml
    - .github/workflows/admin-app.yml
    - .github/workflows/identity-app.yml
    - .github/workflows/identity-api.yml
    - .github/workflows/hid-api.yml
    - pnpm-workspace.yaml
    - pnpm-lock.yaml
    - .npmrc
    - Dockerfile
  push:
    branches:
    - master
    - dev
    - feat/*
    - chore/*
    - build/*
    - fix/*
    - refactor/*
    paths:
    - e2e/**
    - adapter/**
    - app/admin/**
    - app/identity/**
    - api/identity/**
    - api/hid/**
    - lib/blapi/**
    - lib/plot/**
    - lib/haven-fastify/**
    - lib/names/**
    - lib/email-validation/**
    - lib/password-strength-ui/**
    - lib/password-strength/**
    - lib/docker-util/**
    - .github/workflows/e2e.yml
    - .github/workflows/admin-app.yml
    - .github/workflows/identity-app.yml
    - .github/workflows/identity-api.yml
    - .github/workflows/hid-api.yml
    - pnpm-workspace.yaml
    - pnpm-lock.yaml
    - .npmrc
    - Dockerfile
env:
  NODE_VERSION: "20.x"
  UNLEASH_FRONTEND_API_KEY: ${{secrets.UNLEASH_FRONTEND_API_KEY}}
  UNLEASH_FRONTEND_API_URL: 'https://api.haven.com/features/api/frontend'
  UNLEASH_API_URL: https://api.haven.com/features/api
  UNLEASH_API_KEY: ${{ secrets.UNLEASH_API_KEY }}
  APP_NAME: 'identity-e2e'

jobs:
  e2e:
    name: Run E2E Tests
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v3

    - name: Install bwrap
      run: |
        sudo apt-get update || true
        sudo apt install -y bubblewrap

    - uses: pnpm/action-setup@v2
      name: Install pnpm
      with:
        version: 8
        run_install: false

    - name: Use Node.js ${{ env.NODE_VERSION }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ env.NODE_VERSION }}
        registry-url: https://npm.pkg.github.com
        scope: '@havenengineering'
        cache: 'pnpm'

    - name: Cache Cypress
      id: cypress-cache
      uses: actions/cache@v3
      with:
        path: /home/<USER>/.cache/Cypress
        key: npm-${{ hashFiles('e2e/package.json') }}

    - name: Install dependencies
      env:
        GITHUB_TOKEN: ${{ secrets.HAVEN_PLATFORM_BOT_PACKAGE_READ_PAT }}
      run: |
        pnpm --filter "haven-identity-e2e..." --filter "haven-identity-api..." --filter "haven-hid-api..." --filter "haven-identity-app..." --filter "haven-identity-admin..." install --frozen-lockfile --config.dedupe-peer-dependents=false

    - name: Install Cypress if required
      run: |
        cd e2e
        pnpm cypress install

    - name: Build
      env:
        GITHUB_TOKEN: ${{ secrets.HAVEN_PLATFORM_BOT_PACKAGE_READ_PAT }}
      run: |
        pnpm --filter "haven-identity-e2e..." --filter "haven-identity-api..." --filter "haven-hid-api..." --filter "haven-identity-app..." --filter "haven-identity-admin..." build

    - name: Install mock service workspace dependencies
      env:
        GITHUB_TOKEN: ${{ secrets.HAVEN_PLATFORM_BOT_PACKAGE_READ_PAT }}
      run: |
        pnpm --filter "mock-haven*..." install --frozen-lockfile --config.dedupe-peer-dependents=false

    - name: Build mock services
      run: |
        pnpm --filter "mock-haven*..." build

    # this step is required to create the SSL files used by NGINX container. The files stored locally in the repo cannot be pushed to github for security reasons.
    - name: Create SSL Certificates
      run: |
        echo "${{secrets.HAVEN_LOCAL_TLS_CERT}}" | base64 --decode > ./e2e/static/nginx/etc/ssl/platform/haven.local/signed_chain.crt
        echo "${{secrets.HAVEN_LOCAL_TLS_KEY}}" | base64 --decode > ./e2e/static/nginx/etc/ssl/platform/haven.local/domain.key

    - name: Start containers
      env:
        HAVEN_SPA_CLIENT_SECRET: 'spa_secret'
        HAVEN_OWNERS_CLIENT_SECRET: 'owners_secret'
        HAVEN_MYACCOUNT_CLIENT_SECRET: 'myaccount_secret'
        HAVEN_EXPERIENCE_CLIENT_SECRET: 'experience_secret'
        HAVEN_FOOD_ORDERING_CLIENT_SECRET: 'foodordering_secret'
        PLOT_API_URL: 'https://plot-api.dev.haven-leisure.com/api'
        PLOT_API_KEY: ${{ secrets.PLOT_DEV_KEY }}
        IDENTITY_SSO_API_KEY: ${{ secrets.IDENTITY_SSO_API_KEY }}
        BLAPI_URL: 'https://dev.haven-api.havdev.bll-group.co.uk/v2'
        BLAPI_IDENTITY_URL: 'https://dev.haven-api.havdev.bll-group.co.uk/v2'
        REGISTER_ARRIVAL_WITHOUT_LOGIN_URL: 'https://ownerarrivals.haven-dev.com'
        BLAPI_X_API_KEY: ${{ secrets.BLAPI_DEV_KEY }}
        BLAPI_X_IDENTITY_KEY: ${{ secrets.BLAPI_IDENTITY_KEY }}
        BLAPI_ACCOUNT_URL: 'https://service-haven-blapi-account.dev.haven-leisure.com'
        BLAPI_ACCOUNT_X_API_KEY: ${{ secrets.BLAPI_ACCOUNT_X_API_KEY }}
        OIDC_ISSUER: 'https://identity.haven-local.com/identity'
        OWNERS_DOMAIN: 'https://owners.haven-dev.com'
        OWNERS_LOGIN_DOMAIN: 'https://ownerslogin.haven-dev.com'
        HAVEN_DOMAIN: 'https://www.haven-dev.com'
        FOOD_ORDERING_DOMAIN: 'https://food.haven-dev.com'
        CONTENTFUL_GRAPHQL_URL: 'https://graphql.contentful.com/content/v1/spaces/3w3eqfx5udv1/environments/dev'
        CONTENTFUL_DELIVERY_API_ACCESS_TOKEN: ${{ secrets.CONTENTFUL_API_DELIVERY_EXPERIENCE_DEV }}
        TABLE_BOOKINGS_SERVICE_URL: 'https://service-haven-table-booking.dev.haven-leisure.com'
        ENTERTAINMENT_SERVICE_URL: 'https://service-haven-entertainment.dev.haven-leisure.com'
        ACTIVITIES_SERVICE_URL: 'https://service-haven-activities-team-gateway-v2.dev.haven-leisure.com'
        JWKS: ${{ secrets.JWKS }}
        # ACTIVITIES_API_KEY: ${{ secrets.ACTIVITIES_API_KEY }}
        APP_NAME: 'identity-admin-e2e'
      run: |
        pnpm --filter "haven-oidc-pgsql-adapter" migrate
        pnpm --filter "haven-hid-pgsql-adapter" migrate
        pnpm --filter "haven-hid-pgsql-adapter" seed-e2e
        pnpm --filter "haven-identity-e2e" start

    - name: Run e2e cypress tests
      uses: cypress-io/github-action@v5
      with:
        working-directory: ./e2e
        install: false
        command: pnpm test:e2e

    - name: Run e2e post cypress checks
      run: |
        pnpm --filter "haven-identity-e2e" test:e2e:unit

    - name: Notify about failure
      if: ${{ failure() }}
      run: |
        docker logs havenIdentity || echo "getting logs failed"
        docker logs mockHaven || echo "getting logs failed"
        docker logs mockHavenOwners || echo "getting logs failed"
        docker logs mockHavenHoliday || echo "getting logs failed"
        docker logs mockHavenCheckout || echo "getting logs failed"
        cat << EOF > message.json
        {"@type":"MessageCard","@context":"https://schema.org/extensions","summary":"Pipeline failed!","themeColor":"ff0000","title":"$GITHUB_REPOSITORY pipeline failed 💢!","sections":[{"facts":[{"name":"Repository:","value":"$GITHUB_REPOSITORY"},{"name":"Branch:","value":"$GITHUB_REF_NAME"},{"name":"Commit:","value":"$GITHUB_SHA"}]}],"potentialAction":[{"@type":"OpenUri","name":"View on GitHub","targets":[{"os":"default","uri":"$GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID"}]}]}
        EOF
        curl -X POST ${{ secrets.TEAMS_WEBHOOK }} --header 'Content-Type: application/json' -d @message.json

    - name: Upload test failure artifacts
      if: ${{ failure() }}
      uses: actions/upload-artifact@v4
      with:
        name: screenshots
        path: e2e/cypress/screenshots/**
        retention-days: 2
