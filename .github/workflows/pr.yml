# Use GitHub actions to notify of PRs
name: MS Teams Notification

# Controls when the workflow will run
on:
  # Triggers the workflow on pull request events
  pull_request:
    types: [opened]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  notify:
    env:
      TEAMS_WEBHOOK: ${{ secrets.TEAMS_WEBHOOK }}
      PR_URL: ${{ github.event.pull_request.html_url }}
      PR_USER: ${{ github.event.sender.login }}
    runs-on: platform-runners-dev

    steps:
      - name: Send notification via CURL
        if: ${{ github.event.pull_request.draft == false }}
        run: |
          curl -H 'Content-Type: application/json' -d "{\"text\": \"$PR_USER created PR $PR_URL\"}" $TEAMS_WEBHOOK
