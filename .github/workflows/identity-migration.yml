name: IDENTITY MIGRATION

on:
  schedule:
    - cron: "0 3 * * *"
  pull_request:
    branches:
      - main
    paths:
      - migration/identity/**
      - .github/workflows/identity-migration.yml
      - pnpm-workspace.yaml
      - pnpm-lock.yaml
      - .npmrc
      - Dockerfile
  push:
    branches:
      - master
      - dev
      - feat/*
      - chore/*
      - build/*
      - fix/*
      - refactor/*
    paths:
      - migration/identity/**
      - .github/workflows/identity-migration.yml
      - pnpm-workspace.yaml
      - pnpm-lock.yaml
      - .npmrc
      - Dockerfile
env:
  NODE_VERSION: "20.x"
  APP_NAME: "service-haven-identity-migration"
  ECR_REGISTRY: "745662293263.dkr.ecr.eu-west-1.amazonaws.com"
  HELM_REGISTRY: "http://chartmuseum.chartmuseum.svc:8080"
  HELM_S3_BUCKET_DEV: "aw-blg-sandbox-dev-chartmuseum"
  HELM_S3_BUCKET_PROD: "aws-shared-chartmuseum-s3"
  SPINNAKER: "http://spin-gate.spinnaker.svc:8084"
  BRAND: "haven"
  PRODUCT: "identity"
  TRIBE: "foundation"

jobs:
  build:
    name: Build for Auth0 Migration
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Install bwrap
        run: |
          sudo apt-get update || true
          sudo apt install -y bubblewrap

      - uses: pnpm/action-setup@v2
        name: Install pnpm
        with:
          version: 8
          run_install: false

      - name: Use Node.js ${{ env.NODE_VERSION }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          registry-url: https://npm.pkg.github.com
          scope: '@havenengineering'
          cache: 'pnpm'

      - name: Install
        env:
          GITHUB_TOKEN: ${{ secrets.HAVEN_PLATFORM_BOT_PACKAGE_READ_PAT }}
        run: |
          pnpm --filter "haven-identity-bulk-migration..." install --frozen-lockfile --config.dedupe-peer-dependents=false

      - name: Build
        run: |
          pnpm --filter "haven-identity-bulk-migration..." build

      - name: Start database for adapter tests
        run: |
          pnpm --filter "haven-hid-pgsql-adapter" migrate

      - name: Run tests
        run: |
          pnpm --filter "haven-identity-bulk-migration..." test

      - name: Notify about failure
        if: ${{ failure() }}
        run: |
          cat << EOF > message.json
          {"@type":"MessageCard","@context":"https://schema.org/extensions","summary":"Pipeline build failed!","themeColor":"ff0000","title":"$GITHUB_REPOSITORY pipeline failed 💢!","sections":[{"facts":[{"name":"Repository:","value":"$GITHUB_REPOSITORY"},{"name":"Branch:","value":"$GITHUB_REF_NAME"},{"name":"Commit:","value":"$GITHUB_SHA"}]}],"potentialAction":[{"@type":"OpenUri","name":"View on GitHub","targets":[{"os":"default","uri":"$GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID"}]}]}
          EOF
          curl -X POST ${{ secrets.TEAMS_WEBHOOK }} --header 'Content-Type: application/json' -d @message.json

  publish:
    name: Publish docker
    if: github.ref == 'refs/heads/master' && github.event_name != 'schedule'
    runs-on: platform-runners-ecr
    needs: build
    concurrency:
      group: docker-publishing-${{ github.ref }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Reset tags
        run: |
          git tag -l | xargs git tag -d
          git fetch --tags
      - name: Use Node.js ${{ env.NODE_VERSION }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          registry-url: https://npm.pkg.github.com
          scope: '@havenengineering'

      - name: Semantic Release
        id: semantic   # Needs it so we can get the outputs
        uses: cycjimmy/semantic-release-action@v3
        with:
          working_directory: ./api/hid
          tag_format: ${{ env.APP_NAME }}-v${version}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          HUSKY: 0

      - name: Generate APP_VERSION for ReRelease
        if: steps.semantic.outputs.new_release_published != 'true'
        run: |
          LAST_VERSION=$(git describe --tags --match "service-haven-identity-migration-v*" --abbrev=4 HEAD | sed -r "s/service-haven-identity-migration-v//g");
          BUILD_TIMESTAMP=$(date +'%y%m%d%H%M');
          echo "APP_VERSION=${LAST_VERSION}-${BUILD_TIMESTAMP}" >> $GITHUB_ENV

      - name: Set APP_VERSION
        if: steps.semantic.outputs.new_release_published == 'true'
        run: |
          echo "APP_VERSION=${{ steps.semantic.outputs.new_release_version }}" >> $GITHUB_ENV

      - name: Login to Docker registry
        run: |
          aws ecr get-login-password --region eu-west-1 | docker login --username AWS --password-stdin ${ECR_REGISTRY}

      - name: Build and Push Docker image
        env:
          GITHUB_TOKEN: ${{ secrets.HAVEN_PLATFORM_BOT_PACKAGE_READ_PAT }}
        run: |
          ./build.sh haven-identity-bulk-migration stage-users ${APP_VERSION} ${ECR_REGISTRY}/${TRIBE}/${APP_NAME}:${APP_VERSION}
          docker push ${ECR_REGISTRY}/${TRIBE}/${APP_NAME}:${APP_VERSION}

      - name: Notify about failure
        if: ${{ failure() }}
        run: |
          cat << EOF > message.json
          {"@type":"MessageCard","@context":"https://schema.org/extensions","summary":"Pipeline deploy failed!","themeColor":"ff0000","title":"$GITHUB_REPOSITORY pipeline failed 💢!","sections":[{"facts":[{"name":"Repository:","value":"$GITHUB_REPOSITORY"},{"name":"Branch:","value":"$GITHUB_REF_NAME"},{"name":"Commit:","value":"$GITHUB_SHA"}]}],"potentialAction":[{"@type":"OpenUri","name":"View on GitHub","targets":[{"os":"default","uri":"$GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID"}]}]}
          EOF
          curl -X POST ${{ secrets.TEAMS_WEBHOOK }} --header 'Content-Type: application/json' -d @message.json