name: DATA

on:
  schedule:
    - cron: "0 4 * * *"
  pull_request:
    branches:
      - main
    paths:
      - data/**
      - .github/workflows/data.yml
      - pnpm-workspace.yaml
      - pnpm-lock.yaml
      - .npmrc    
  push:
    branches:
      - master
      - dev
      - feat/*
      - chore/*
      - build/*
      - fix/*
    paths:
      - data/**
      - .github/workflows/data.yml
      - pnpm-workspace.yaml
      - pnpm-lock.yaml           
      - .npmrc
      
env:
  NODE_VERSION: "20.x"

jobs:
  build:
    name: Haven Identity Data build
    runs-on: platform-runners-dev
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Install bwrap
        run: |
          sudo apt-get update || true
          sudo apt install -y bubblewrap

      - uses: pnpm/action-setup@v2
        name: Install pnpm
        with:
          version: 8
          run_install: false

      - name: Use Node.js ${{ env.NODE_VERSION }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          registry-url: https://npm.pkg.github.com
          scope: '@havenengineering'
          cache: 'pnpm'

      - name: Install
        env:
          GITHUB_TOKEN: ${{ secrets.HAVEN_PLATFORM_BOT_PACKAGE_READ_PAT }}
        run: |
          pnpm --filter "haven-identity-data..." install --frozen-lockfile --config.dedupe-peer-dependents=false

      - name: Build
        run: |
          pnpm --filter "haven-identity-data..." build

      - name: Run tests
        run: |
          pnpm --filter "haven-identity-data" full

      - name: Notify about failure
        if: ${{ failure() }}
        run: |
          cat << EOF > message.json
          {"@type":"MessageCard","@context":"https://schema.org/extensions","summary":"Pipeline failed!","themeColor":"ff0000","title":"$GITHUB_REPOSITORY pipeline failed 💢!","sections":[{"facts":[{"name":"Repository:","value":"$GITHUB_REPOSITORY"},{"name":"Branch:","value":"$GITHUB_REF_NAME"},{"name":"Commit:","value":"$GITHUB_SHA"}]}],"potentialAction":[{"@type":"OpenUri","name":"View on GitHub","targets":[{"os":"default","uri":"$GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID"}]}]}
          EOF
          curl -X POST ${{ secrets.TEAMS_WEBHOOK }} --header 'Content-Type: application/json' -d @message.json