name: EMAIL VALIDATION

on:
  pull_request:
    branches:
      - main
    paths:
      - .github/workflows/email-validation.yml
      - lib/email-validation/**
  push:
    branches:
      - master
      - dev
      - feat/*
      - chore/*
      - build/*
      - fix/*
    paths:
      - .github/workflows/email-validation.yml
      - lib/email-validation/**

env:
  NODE_VERSION: "20.x"

jobs:
  build:
    name: Build for Email validation
    runs-on: platform-runners-ecr
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - uses: pnpm/action-setup@v3
        name: Install pnpm
        env:
          GITHUB_TOKEN: ${{ secrets.HAVEN_PLATFORM_BOT_PACKAGE_READ_PAT }}
        with:
          version: 8
          run_install: false

      - name: Use Node.js ${{ env.NODE_VERSION }}
        env:
          GITHUB_TOKEN: ${{ secrets.HAVEN_PLATFORM_BOT_PACKAGE_READ_PAT }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          registry-url: https://npm.pkg.github.com
          cache: 'pnpm'

      - name: Install
        env:
          GITHUB_TOKEN: ${{ secrets.HAVEN_PLATFORM_BOT_PACKAGE_READ_PAT }}
        run: |
          pnpm --filter "@havenengineering/module-shared-email-validation" install --frozen-lockfile --config.dedupe-peer-dependents=false

      - name: Build
        run: |
          pnpm --filter "@havenengineering/module-shared-email-validation" build

      - name: Run tests
        run: |
          pnpm --filter "@havenengineering/module-shared-email-validation" test

      - name: Publish
        if: github.ref == 'refs/heads/master'
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: pnpm --filter "@havenengineering/module-shared-email-validation" publish --no-git-checks

      - name: Notify about failure
        if: ${{ failure() }}
        run: |
          cat << EOF > message.json
          {"@type":"MessageCard","@context":"https://schema.org/extensions","summary":"Pipeline build failed!","themeColor":"ff0000","title":"$GITHUB_REPOSITORY pipeline failed 💢!","sections":[{"facts":[{"name":"Repository:","value":"$GITHUB_REPOSITORY"},{"name":"Branch:","value":"$GITHUB_REF_NAME"},{"name":"Commit:","value":"$GITHUB_SHA"}]}],"potentialAction":[{"@type":"OpenUri","name":"View on GitHub","targets":[{"os":"default","uri":"$GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID"}]}]}
          EOF
          curl -X POST ${{ secrets.TEAMS_WEBHOOK }} --header 'Content-Type: application/json' -d @message.json
