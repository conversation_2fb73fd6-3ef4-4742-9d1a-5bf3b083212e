name: HID LOAD TESTS

on:
  workflow_call:
  schedule:
    - cron: "30 8 * * *"
  pull_request:
    branches:
      - main
    paths:
      - load-test/**
      - .github/workflows/hid-load-test.yml
      - pnpm-lock.yaml
      - .npmrc
  push:
    branches:
      - master
      - dev
      - feat/*
      - chore/*
      - build/*
      - fix/*
    paths:
      - load-test/**
      - .github/workflows/hid-load-test.yml
      - pnpm-lock.yaml
      - .npmrc

env:
  NODE_VERSION: "20.x"

jobs:
  build:
    name: HID service load tests
    runs-on: platform-runners-ecr
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - uses: pnpm/action-setup@v2
        name: Install pnpm
        with:
          version: 8
          run_install: false

      - name: Use Node.js ${{ env.NODE_VERSION }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          registry-url: https://npm.pkg.github.com
          scope: '@havenengineering'
          cache: 'pnpm'

      - name: Install
        env:
          GITHUB_TOKEN: ${{ secrets.HAVEN_PLATFORM_BOT_PACKAGE_READ_PAT }}
        run: |          
          pnpm --filter "load-test" install --frozen-lockfile --config.dedupe-peer-dependents=false

      - name: Build
        run: |
          pnpm --filter "load-test" build

      - name: Setup k6
        uses: grafana/setup-k6-action@v1

      - name: Run Load Test against DEV
        env:
          APP_KEY: ${{ secrets.PERF_TEST_APP_KEY }}
        run: |
          pnpm --filter "load-test" start:all

      - name: Notify about failure
        if: ${{ failure() }}
        run: |
          cat << EOF > message.json
          {"@type":"MessageCard","@context":"https://schema.org/extensions","summary":"Pipeline build failed!","themeColor":"ff0000","title":"$GITHUB_REPOSITORY pipeline failed 💢!","sections":[{"facts":[{"name":"Repository:","value":"$GITHUB_REPOSITORY"},{"name":"Branch:","value":"$GITHUB_REF_NAME"},{"name":"Commit:","value":"$GITHUB_SHA"}]}],"potentialAction":[{"@type":"OpenUri","name":"View on GitHub","targets":[{"os":"default","uri":"$GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID"}]}]}
          EOF
          curl -X POST ${{ secrets.TEAMS_WEBHOOK }} --header 'Content-Type: application/json' -d @message.json
